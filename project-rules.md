# 專案開發規則文檔

## 📋 總則

本文檔規範了 PLC Frontend 專案的開發規則、代碼標準和最佳實踐，所有開發人員必須嚴格遵守。

## 🔧 環境要求

### Node.js 版本限制
- **Node.js**: 18.16.1 (強制要求)
- **npm**: 9.5.1 (強制要求)
- **包管理**: 必須使用 `--legacy-peer-deps` 標誌

### 開發工具
- **IDE**: 推薦使用 VS Code
- **瀏覽器**: Chrome/Firefox 最新版本
- **版本控制**: Git

## 📁 專案結構規範

```
src/
├── components/          # 可重用組件
│   ├── utilities/       # 工具組件
│   ├── charts/          # 圖表組件
│   └── ...
├── stores/              # Pinia 狀態管理
├── vuex/                # Vuex 狀態管理（現有）
├── layout/              # 佈局組件
├── view/                # 頁面組件
├── routes/              # 路由配置
├── static/              # 靜態資源
├── config/              # 配置文件
├── composable/          # 組合式函數
├── utility/             # 工具函數
└── i18n/                # 國際化
```

## 💻 代碼規範

### Vue.js 組件規範

#### 1. 組件命名
- **文件名**: 使用 PascalCase（如：`ThemeToggle.vue`）
- **組件名**: 與文件名保持一致
- **Props**: 使用 camelCase
- **事件**: 使用 kebab-case

#### 2. 組件結構
```vue
<template>
  <!-- 模板內容 -->
</template>

<script>
// 導入語句
import { computed, ref } from 'vue'

export default {
  name: 'ComponentName',
  components: {
    // 子組件
  },
  props: {
    // Props 定義
  },
  emits: ['event-name'],
  setup(props, { emit }) {
    // Composition API 邏輯
    return {
      // 返回值
    }
  }
}
</script>

<style scoped>
/* 樣式定義 */
</style>
```

#### 3. Composition API 使用
- **優先使用**: Composition API 而非 Options API
- **響應式數據**: 使用 `ref()` 和 `reactive()`
- **計算屬性**: 使用 `computed()`
- **生命週期**: 使用 `onMounted()`, `onUnmounted()` 等

### JavaScript 規範

#### 1. ES6+ 語法
- 使用 `const` 和 `let`，避免 `var`
- 使用箭頭函數
- 使用解構賦值
- 使用模板字符串

#### 2. 函數命名
- 使用 camelCase
- 函數名應該描述其功能
- 布爾值返回函數使用 `is`, `has`, `can` 前綴

#### 3. 變數命名
- 使用有意義的變數名
- 常量使用 UPPER_SNAKE_CASE
- 私有變數使用下劃線前綴

### CSS/SCSS 規範

#### 1. 樣式組織
- 使用 `scoped` 樣式
- 優先使用 CSS 變數
- 遵循 BEM 命名規範

#### 2. CSS 變數使用
```css
/* 正確使用 */
.component {
  background: var(--background);
  color: var(--text-primary);
  border: 1px solid var(--border-normal);
}

/* 避免硬編碼顏色 */
.component {
  background: #ffffff; /* ❌ 不推薦 */
}
```

#### 3. 響應式設計
- 使用 Mobile First 方法
- 斷點：768px, 1024px, 1200px
- 使用相對單位（rem, em, %）

## 🎨 主題系統規範

### 1. CSS 變數使用
- **必須使用**: 所有顏色都必須使用 CSS 變數
- **禁止硬編碼**: 不允許在組件中硬編碼顏色值
- **變數命名**: 遵循語義化命名

### 2. 主題適配
- 所有新組件必須支持主題切換
- 測試兩種主題下的顯示效果
- 確保對比度符合無障礙標準

### 3. 主題 Store 使用
```javascript
// 正確使用方式
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)
```

## 📦 狀態管理規範

### 1. Pinia vs Vuex
- **新功能**: 使用 Pinia
- **現有功能**: 保持 Vuex，除非有重構需求
- **主題管理**: 使用 Pinia

### 2. Store 結構
```javascript
export const useExampleStore = defineStore('example', {
  state: () => ({
    // 狀態定義
  }),
  getters: {
    // 計算屬性
  },
  actions: {
    // 方法定義
  }
})
```

## 🛣️ 路由規範

### 1. 路由命名
- 使用 kebab-case
- 路由名稱應該描述頁面功能
- 嵌套路由使用層級結構

### 2. 路由組織
- 按功能模塊組織路由文件
- 使用懶加載導入組件
- 設置適當的路由守衛

## 🧪 測試規範

### 1. 組件測試
- 新組件必須包含基本測試
- 測試主要功能和邊界情況
- 測試主題切換功能

### 2. 測試文件命名
- 測試文件使用 `.spec.js` 或 `.test.js` 後綴
- 與被測試文件同名

## 📝 文檔規範

### 1. 代碼註釋
- 複雜邏輯必須添加註釋
- 使用 JSDoc 格式
- 中文註釋

### 2. README 更新
- 新功能必須更新 README
- 包含使用示例
- 更新依賴列表

## 🚀 部署規範

### 1. 構建前檢查
- 運行 `npm run lint`
- 確保所有測試通過
- 檢查控制台無錯誤

### 2. 環境變數
- 使用 `.env` 文件管理環境變數
- 敏感信息不提交到版本控制
- 不同環境使用不同配置

## 🔒 安全規範

### 1. 數據處理
- 用戶輸入必須驗證
- 避免 XSS 攻擊
- 使用 HTTPS

### 2. 依賴管理
- 定期更新依賴
- 檢查安全漏洞
- 使用 `npm audit`

## 📋 代碼審查規範

### 1. Pull Request
- 描述清楚變更內容
- 包含測試結果
- 關聯相關 Issue

### 2. 審查要點
- 代碼風格一致性
- 功能正確性
- 性能影響
- 安全性考慮

## ⚠️ 禁止事項

### 1. UI/UX 限制
- 不可隨意修改現有 UI 設計
- 不可改變用戶交互流程
- 主題系統除外

### 2. 技術限制
- 不可引入未經批准的新依賴
- 不可修改核心配置文件
- 不可繞過現有架構

## 🔄 版本控制規範

### 1. 分支策略
- `main`: 生產分支
- `develop`: 開發分支
- `feature/*`: 功能分支
- `hotfix/*`: 緊急修復分支

### 2. 提交信息
```
type(scope): description

feat(theme): add dark mode support
fix(api): resolve data loading issue
docs(readme): update installation guide
```

## 📞 聯繫方式

如有疑問或需要澄清，請聯繫：
- 專案負責人
- 技術主管
- 開發團隊

---

**注意**: 本規範文檔會定期更新，請關注最新版本。
