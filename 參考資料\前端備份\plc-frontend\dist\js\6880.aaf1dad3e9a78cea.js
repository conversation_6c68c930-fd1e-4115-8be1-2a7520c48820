"use strict";(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[3003,6880],{73054:function(e,a,t){t.r(a),t.d(a,{default:function(){return wl}});var l=t(20641);function n(e,a,t,n,r,u){const s=(0,l.g2)("sdPageHeader"),o=(0,l.g2)("Setting"),d=(0,l.g2)("a-tab-pane"),i=(0,l.g2)("Meter"),m=(0,l.g2)("Fee"),c=(0,l.g2)("Calculate"),k=(0,l.g2)("Output"),p=(0,l.g2)("a-tabs"),f=(0,l.g2)("sdCards"),y=(0,l.g2)("Main");return(0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(s,{title:"電費計算",class:"ninjadash-page-header-main",routes:[{breadcrumbName:"系統"},{breadcrumbName:"電費計算"}]}),(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(f,null,{default:(0,l.k6)((()=>[(0,l.bF)(p,{activeKey:e.activeTab,"onUpdate:activeKey":a[0]||(a[0]=a=>e.activeTab=a)},{default:(0,l.k6)((()=>[(0,l.bF)(d,{key:"1",tab:"契約"},{default:(0,l.k6)((()=>[(0,l.bF)(o)])),_:1}),(0,l.bF)(d,{key:"2",tab:"電表"},{default:(0,l.k6)((()=>[(0,l.bF)(i)])),_:1}),(0,l.bF)(d,{key:"3",tab:"電價"},{default:(0,l.k6)((()=>[(0,l.bF)(m)])),_:1}),(0,l.bF)(d,{key:"4",tab:"查詢","force-render":""},{default:(0,l.k6)((()=>[(0,l.bF)(c)])),_:1}),(0,l.bF)(d,{key:"6",tab:"匯出","force-render":""},{default:(0,l.k6)((()=>[(0,l.bF)(k)])),_:1})])),_:1},8,["activeKey"])])),_:1})])),_:1})])}var r=t(79570),u=t(79841),s=t(95853);const o=s.Ay.div`
    padding:3rem;
`,d=s.Ay.div`
    margin-top:1rem;
    padding:1rem
`;var i=t(9322);function m(e,a,t,n,r,u){const s=(0,l.g2)("a-input"),o=(0,l.g2)("a-form-item"),d=(0,l.g2)("a-select"),m=(0,l.g2)("a-radio-group"),c=(0,l.g2)("a-spin"),k=(0,l.g2)("sdButton"),p=(0,l.g2)("a-col"),f=(0,l.g2)("a-row"),y=(0,l.g2)("a-form"),v=(0,l.g2)("sdModal"),g=(0,l.g2)("DataTables");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(v,{key:0,title:e.settings.title,visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(y,{model:e.settings,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,rules:e.rules,labelAlign:"left",onFinish:e.saveSetting},{default:(0,l.k6)((()=>[(0,l.bF)(o,{label:"契約名稱",name:"name"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.name,"onUpdate:value":a[0]||(a[0]=a=>e.settings.name=a)},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"電號",name:"no"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.no,"onUpdate:value":a[1]||(a[1]=a=>e.settings.no=a)},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"用電種類",name:"type"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{value:e.settings.type,"onUpdate:value":a[2]||(a[2]=a=>e.settings.type=a),options:e.typeOptions},null,8,["value","options"])])),_:1}),(0,l.bF)(o,{label:"契約",name:"contract"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{value:e.settings.contract,"onUpdate:value":a[3]||(a[3]=a=>e.settings.contract=a),options:e.contractOptions},null,8,["value","options"])])),_:1}),(0,l.bF)(o,{label:"時段",name:"timePeriod"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{value:e.settings.timePeriod,"onUpdate:value":a[4]||(a[4]=a=>e.settings.timePeriod=a),options:e.periodOptions},null,8,["value","options"])])),_:1}),(0,l.bF)(o,{label:"經常契約容量",name:"capacity"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.capacity,"onUpdate:value":a[5]||(a[5]=a=>e.settings.capacity=a),type:"number"},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"備容量",name:"spareCapacity"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.spareCapacity,"onUpdate:value":a[6]||(a[6]=a=>e.settings.spareCapacity=a),type:"number"},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"離峰契約容量",name:"offPeakCapacity"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.offPeakCapacity,"onUpdate:value":a[7]||(a[7]=a=>e.settings.offPeakCapacity=a),type:"number"},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"週六半尖峰契約容量",name:"satHalfPeakCapacity"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.satHalfPeakCapacity,"onUpdate:value":a[8]||(a[8]=a=>e.settings.satHalfPeakCapacity=a),type:"number"},null,8,["value"])])),_:1}),(0,l.bF)(o,{label:"非夏月/半尖峰契約容量",name:"halfPeakCapacity"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.settings.halfPeakCapacity,"onUpdate:value":a[9]||(a[9]=a=>e.settings.halfPeakCapacity=a),type:"number"},null,8,["value"])])),_:1}),(0,l.bF)(f,{gutter:[5,10],align:"center"},{default:(0,l.k6)((()=>[(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)(k,{class:"act-btn",type:"primary","html-type":"submit",disabled:e.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 儲存 "),(0,l.bo)((0,l.bF)(c,{size:"small"},null,512),[[i.aG,e.loading]])])),_:1},8,["disabled"])])),_:1}),(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bF)(k,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0),e.loading?((0,l.uX)(),(0,l.Wv)(c,{key:1})):(0,l.Q3)("",!0),e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(g,{key:2,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,handleAdd:e.openAddModal,handleDataSearch:e.search},null,8,["tableData","columns","addOption","handleAdd","handleDataSearch"]))])}t(1532);var c=t(30995),k=(t(75126),t(56427));t(18111),t(61701);const p=s.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`;var f=t(40834),y=t(45118),v=t(82958),g=t(37859),b=(0,l.pM)({props:{},components:{DataTables:y.A},setup(){const{permission:e}=(0,v.J)(),{dispatch:a}=(0,f.Pj)(),t=(0,u.KR)(g.lp),n=(0,u.KR)(g._K),r=(0,u.KR)(g.iA),s=(0,u.KR)(!1),o=()=>{s.value=!1},d=()=>{Object.assign(P,b),s.value=!0},i=e=>{const a={title:e.name,...e};Object.assign(P,a),s.value=!0},m=[{title:"名稱",dataIndex:"name",key:"name"},{title:"電號",dataIndex:"no",key:"no"},{title:"操作",dataIndex:"action",key:"action"}],y=(0,l.EW)((()=>F.bill.settingListTableData.map((a=>({name:a.name,no:a.no,action:(0,l.bF)(p,null,{default:()=>[e.update&&(0,l.bF)("span",{onClick:()=>i(a)},[(0,l.bF)((0,l.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,l.bF)("span",{onClick:()=>w(a.id)},[(0,l.bF)((0,l.g2)("unicon"),{name:"trash"},null)])]})}))))),b={id:null,no:null,title:"新增設定",name:null,type:"HighVoltage",contract:"Device",timePeriod:2,capacity:0,spareCapacity:0,offPeakCapacity:0,satHalfPeakCapacity:0,halfPeakCapacity:0},S={no:[{required:!0,message:"請輸入電號",trigger:"blur"}],name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],capacity:[{required:!0,message:"請輸入容量",trigger:"blur"}],spareCapacity:[{required:!0,message:"請輸入容量",trigger:"blur"}],offPeakCapacity:[{required:!0,message:"請輸入容量",trigger:"blur"}],satHalfPeakCapacity:[{required:!0,message:"請輸入容量",trigger:"blur"}],halfPeakCapacity:[{required:!0,message:"請輸入容量",trigger:"blur"}]},P=(0,u.Kh)({}),{state:F}=(0,f.Pj)(),x=(0,l.EW)((()=>F.bill.loading)),T={lg:8,md:9,xs:24},h={lg:16,md:15,xs:24},O=e=>{a("bill/filterSettingTable",e.target.value)},L=async()=>{try{let e;P.id?(await a("bill/editSetting",P),e="修改成功"):(await a("bill/addSetting",P),e="新增成功"),s.value=!1,k.A.success({message:e})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}},w=e=>{c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:x.value,onOk:async()=>{try{await a("bill/deleteSetting",e),c.A.success({content:"刪除成功"})}catch(t){c.A.error({title:"發生錯誤",content:t.message})}}})};return{permission:e,loading:x,modal:s,openAddModal:d,closeModal:o,columns:m,tableData:y,settings:P,rules:S,labelCol:T,wrapperCol:h,search:O,saveSetting:L,typeOptions:t,contractOptions:n,periodOptions:r}}}),S=t(66262);const P=(0,S.A)(b,[["render",m]]);var F=P,x=t(72644);const T={key:0,style:{"font-size":"28px","font-weight":"700"}},h=(0,l.Lk)("h3",null,"電費總結:",-1);function O(e,a,t,n,r,u){const s=(0,l.g2)("ModalTable"),o=(0,l.g2)("a-input"),d=(0,l.g2)("a-form-item"),m=(0,l.g2)("a-button"),c=(0,l.g2)("a-col"),k=(0,l.g2)("a-spin"),p=(0,l.g2)("a-row"),f=(0,l.g2)("a-form"),y=(0,l.g2)("sdModal"),v=(0,l.g2)("a-radio-group"),g=(0,l.g2)("MeterSetting"),b=(0,l.g2)("a-range-picker"),S=(0,l.g2)("PeriodSelect"),P=(0,l.g2)("sdButton"),F=(0,l.g2)("DataTables"),O=(0,l.g2)("BillWrap");return(0,l.uX)(),(0,l.CE)(l.FK,null,[e.detailModal?((0,l.uX)(),(0,l.Wv)(s,{key:0,columns:e.detailColumns,tableData:e.detailTableData,modal:e.detailModal,title:e.detailModalTitle,closeModal:e.closeDetailModal,loading:e.loading},null,8,["columns","tableData","modal","title","closeModal","loading"])):(0,l.Q3)("",!0),e.exportModal?((0,l.uX)(),(0,l.Wv)(y,{key:1,visible:e.exportModal,onCancel:e.closeExportModal,title:"匯出"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{labelAlign:"left"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{label:"檔案名稱",labelCol:{span:6}},{default:(0,l.k6)((()=>[(0,l.bF)(o,{value:e.exportFileName,"onUpdate:value":a[0]||(a[0]=a=>e.exportFileName=a)},null,8,["value"])])),_:1}),(0,l.bF)(p,{align:"end",gutter:10},{default:(0,l.k6)((()=>[(0,l.bF)(c,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{ghost:"",type:"primary",style:{height:"45px"},onClick:e.closeExportModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消")])),_:1},8,["onClick"])])),_:1}),(0,l.bF)(c,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{type:"primary",onClick:e.exportReport,style:{height:"45px"},disabled:e.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 匯出 "),(0,l.bo)((0,l.bF)(k,null,null,512),[[i.aG,e.loading]])])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})])),_:1})])),_:1},8,["visible","onCancel"])):(0,l.Q3)("",!0),(0,l.bF)(p,{justify:"center"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{xs:24,sm:18,md:12},{default:(0,l.k6)((()=>[(0,l.bF)(f,{model:e.formState,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left"},{default:(0,l.k6)((()=>[(0,l.bF)(d,{label:"查詢內容",name:"meters"},{default:(0,l.k6)((()=>[(0,l.bF)(v,{options:e.modeOptions,value:e.formState.mode,"onUpdate:value":a[1]||(a[1]=a=>e.formState.mode=a)},null,8,["options","value"])])),_:1}),(0,l.bF)(d,{label:"電錶選擇",name:"meters"},{default:(0,l.k6)((()=>[(0,l.bF)(g,{selectedMeters:e.formState.meters,onSetMeters:e.setMeters},null,8,["selectedMeters","onSetMeters"])])),_:1}),(0,l.bF)(d,{label:"查詢期間",name:"date"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{value:e.formState.date,"onUpdate:value":a[2]||(a[2]=a=>e.formState.date=a),style:{width:"100%"},bordered:!1},null,8,["value"]),(0,l.bF)(S,{noToday:!0,onSetDate:e.setDate},null,8,["onSetDate"])])),_:1})])),_:1},8,["model","label-col","wrapper-col"]),(0,l.bF)(p,{justify:"end"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{class:"act-btn",type:"primary",disabled:!e.submitable,onClick:(0,i.D$)(e.submit,["prevent"])},{default:(0,l.k6)((()=>[(0,l.eW)(" 查詢 "),(0,l.bo)((0,l.bF)(k,{size:"small"},null,512),[[i.aG,e.loading]])])),_:1},8,["disabled","onClick"])])),_:1}),e.submitted?((0,l.uX)(),(0,l.CE)("p",T,"查詢結果")):(0,l.Q3)("",!0),e.submitted?((0,l.uX)(),(0,l.Wv)(F,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.data,columns:e.columns,rowSelection:!1,addOption:!1,exportOption:!0,handleDataSearch:e.search,handleExport:e.openExportModal},null,8,["tableData","columns","handleDataSearch","handleExport"])):(0,l.Q3)("",!0),e.submitted&&e.showSummary?((0,l.uX)(),(0,l.Wv)(O,{key:2},{default:(0,l.k6)((()=>[(0,l.bF)(p,{gutter:[15,15]},{default:(0,l.k6)((()=>[(0,l.bF)(c,{span:24},{default:(0,l.k6)((()=>[h])),_:1}),(0,l.bF)(c,{xs:24,sm:24,md:24}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 尖峰用電度 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.PeakKwh),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 尖峰流動電費 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.PeakFee),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 半尖峰用電度 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.HalfPeakKwh),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 半尖峰流動電費 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.HalfPeakPeakFee),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 週六半尖峰用電度 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.SaturdayHalfPeakKwh),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 週六半尖峰流動電費 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.SaturdayHalfPeakFee),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 離峰用電度 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.OffPeakKwh),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 離峰流動電費 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.OffPeakFee),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 總用電度 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.OffPeakKwh+e.summaryTable.PeakKwh+e.summaryTable.HalfPeakKwh+e.summaryTable.SaturdayHalfPeakKwh),1)])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)(" 總流動電費 ")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.OffPeakFee+e.summaryTable.PeakFee+e.summaryTable.HalfPeakPeakFee+e.summaryTable.SaturdayHalfPeakFee),1)])),_:1}),(0,l.bF)(c,{xs:24,sm:12,md:12}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)("基本費:")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.summaryTable.BasicFee),1)])),_:1}),(0,l.bF)(c,{xs:24,sm:12,md:12}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)("營業稅(5%):")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)((e.summaryTable.BasicFee+e.summaryTable.OffPeakFee+e.summaryTable.PeakFee+e.summaryTable.HalfPeakPeakFee+e.summaryTable.SaturdayHalfPeakFee)*.05.toFixed(2)),1)])),_:1}),(0,l.bF)(c,{xs:24,sm:12,md:12}),(0,l.bF)(c,{xs:12,sm:6,md:6},{default:(0,l.k6)((()=>[(0,l.eW)("總電費:")])),_:1}),(0,l.bF)(c,{xs:12,sm:6,md:6,style:{"text-align":"end"}},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(1.05*(e.summaryTable.BasicFee+e.summaryTable.OffPeakFee+e.summaryTable.PeakFee+e.summaryTable.HalfPeakPeakFee+e.summaryTable.SaturdayHalfPeakFee)),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0)])),_:1})])),_:1})],64)}t(6070);var L=t(7179);t(18237);const w=s.Ay.div`
    margin-top:1rem;
    padding:1rem
`,C=s.Ay.div`
    color: ${({theme:e})=>e["primary-color"]};
    cursor:pointer;
    &:hover{
        text-decoration:underline
    }
   
`;var W=t(74353),U=t.n(W),D=t(25117),_=t(10984),H=t(95804);const M=["onClick"];function K(e,a,t,n,r,u){const s=(0,l.g2)("a-tree-select"),o=(0,l.g2)("a-form-item"),d=(0,l.g2)("a-form"),m=(0,l.g2)("a-select"),c=(0,l.g2)("a-button"),k=(0,l.g2)("a-spin"),p=(0,l.g2)("TagList"),f=(0,l.g2)("sdButton"),y=(0,l.g2)("a-col"),v=(0,l.g2)("a-row"),g=(0,l.g2)("sdModal"),b=(0,l.g2)("Wrap");return(0,l.uX)(),(0,l.Wv)(b,null,{default:(0,l.k6)((()=>[(0,l.Lk)("span",null,"包含 "+(0,x.v_)(e.selectedMeters.length)+" 個電錶",1),(0,l.Lk)("span",{class:"text-primary",onClick:a[0]||(a[0]=(...a)=>e.openModal&&e.openModal(...a))}," 選擇電錶"),e.modal?((0,l.uX)(),(0,l.Wv)(g,{key:0,title:"選擇電錶",visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(d,{"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left"},{default:(0,l.k6)((()=>[(0,l.bF)(o,{label:"地區",style:{"margin-top":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(s,{value:e.formState.regionId,"onUpdate:value":a[1]||(a[1]=a=>e.formState.regionId=a),style:{width:"100%"},"tree-data":e.locations,"allow-clear":"","field-names":{children:"ChildList",label:"Name",value:"Id"},placeholder:"請選擇","tree-node-filter-prop":"label"},null,8,["value","tree-data"])])),_:1})])),_:1},8,["label-col","wrapper-col"]),(0,l.bF)(m,{mode:"multiple",value:e.formState.meters,"onUpdate:value":a[2]||(a[2]=a=>e.formState.meters=a),open:!1,style:{width:"100%"},labelInValue:!0,allowClear:!0},null,8,["value"]),(0,l.bF)(c,{ghost:"",type:"primary",style:{"margin-top":"1rem"},onClick:e.setAllSelected},{default:(0,l.k6)((()=>[(0,l.eW)("全選")])),_:1},8,["onClick"]),(0,l.bF)(p,null,{default:(0,l.k6)((()=>[(0,l.bo)((0,l.bF)(k,null,null,512),[[i.aG,e.tagSearching]]),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.meterOptions,(a=>((0,l.uX)(),(0,l.CE)("div",{key:a.value,class:(0,x.C4)(["tag",e.isExistInSelectedMeters(a)&&"selected"]),onClick:t=>e.setMeters(a)},(0,x.v_)(a.label),11,M)))),128))])),_:1}),(0,l.bF)(v,{gutter:[5,10],align:"center",style:{"margin-top":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(f,{class:"act-btn",type:"primary",onClick:e.submit},{default:(0,l.k6)((()=>[(0,l.eW)(" 選定電錶 ")])),_:1},8,["onClick"])])),_:1}),(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(f,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["visible","onCancel"])):(0,l.Q3)("",!0)])),_:1})}t(44114),t(22489),t(20116);const E=s.Ay.div`
   .text-primary{
       color:${({theme:e})=>e["primary-color"]};
       cursor:pointer;
   }
`,A=s.Ay.div`
    border:1px solid ${({theme:e})=>e["primary-color"]};
    border-radius:5px;
    height:300px;
    width:100%;
    overflow:auto;
    margin-top:1rem;
    .tag{
        padding:0.5rem;
        font-size:16px;
        cursor:pointer;
        color:${({theme:e})=>e["primary-color"]};
       
    }
    .selected{
        background-color:#ff800010;
        color:${({theme:e})=>e["primary-color"]};
        padding-left:1rem;
    }
    ::-webkit-scrollbar {
        width: 12px; 
    }


`;var R=(0,l.pM)({props:{selectedMeters:{type:Array,default:()=>[]}},components:{TagList:A,Wrap:E},setup(e,{emit:a}){const{dispatch:t,state:n}=(0,f.Pj)(),r=(0,u.KR)(!1);(0,l.sV)((async()=>{const e=await Promise.all([t("bill/getMeterList"),t("tags/getTagsRegions")]);try{d.value=e[1]}catch(a){c.A.error({title:"錯誤",content:a.message})}}));const s={lg:6,md:9,xs:24},o={lg:18,md:15,xs:24},d=(0,u.KR)([]),i=(0,l.EW)((()=>{if(k.regionId){const e=n.bill.metersInitData.filter((e=>e.region===k.regionId));return e.map((e=>({value:e.id,label:e.name})))}return n.bill.metersInitData.map((e=>({value:e.id,label:e.name})))})),m=()=>{k.meters=[...k.meters,...JSON.parse(JSON.stringify(i.value))]},k=(0,u.Kh)({regionId:null,meters:[]}),p=e=>{const a=k.meters.find((a=>a.value===e.value));return a},y=e=>{const a=k.meters.find((a=>a.value===e.value));if(a){const e=k.meters.indexOf(a);k.meters.splice(e,1)}else k.meters.push(e)},v=()=>{k.regionId=null,a("setMeters",k.meters),g.value=!1},g=(0,u.KR)(!1),b=()=>{k.meters=e.selectedMeters.map((e=>i.value.find((a=>a.value===e.value))??{value:null,label:"電錶不存在"})),g.value=!0},S=()=>{k.regionId=null,JSON.stringify(k.meters)!==JSON.stringify(e.selectedMeters)?c.A.confirm({title:"提示",content:"電錶將不會選定，確定關閉？",onOk(){g.value=!1}}):g.value=!1};return{tagSearching:r,labelCol:s,wrapperCol:o,meterOptions:i,locations:d,setAllSelected:m,formState:k,isExistInSelectedMeters:p,setMeters:y,modal:g,openModal:b,closeModal:S,submit:v}}});const N=(0,S.A)(R,[["render",K]]);var I=N,X=(0,l.pM)({components:{BillWrap:w,DataTables:y.A,ModalTable:D.A,MeterSetting:I,PeriodSelect:H.A},setup(){const e=L.Ay.SHOW_PARENT,{state:a,dispatch:t}=(0,f.Pj)(),n=(0,l.EW)((()=>a.bill.loading)),r=(0,u.KR)([{label:"用電度",value:"1"},{label:"電費",value:"2"}]),s={lg:8,md:9,xs:24},o={lg:16,md:15,xs:24},d=(0,u.KR)(_._),i=(0,u.Kh)({mode:"1",meters:[],date:[U()().subtract(1,"day"),U()()]}),m=({startTime:e,endTime:a})=>{i.date=[U()(e),U()(a)]},k=e=>{i.meters=e},p=(0,l.EW)((()=>i.meters.length>0&&i.date&&!n.value)),y=(0,u.KR)([]),v=(0,u.KR)(),g=(0,u.KR)(!1),b=(0,u.KR)(),S=(0,u.KR)(!1),P=async()=>{"1"===i.mode?(await t("bill/calculateVariation",(0,u.ux)(i)),y.value=a.bill.calculateTableData.map((e=>({name:(0,l.bF)(C,{onClick:()=>K(e)},{default:()=>[e.MeterName]}),region:e.RegionName,unit:e.MeterUnit,total:e.Data.reduce(((e,a)=>e+a.Variation),0).toFixed(2)}))),v.value=[{title:"名稱",dataIndex:"name",key:"name",fixed:"left"},{title:"地區",dataIndex:"region",key:"region"},{title:"單位",dataIndex:"unit",key:"unit"},{title:"總用電量",dataIndex:"total",key:"total",align:"right"}],M.value=[{title:"時間",dataIndex:"Date",key:"Date",fixed:"left"},{title:"起始值",dataIndex:"FirstValue",key:"FirstValue"},{title:"結束值",dataIndex:"LastValue",key:"LastValue"},{title:"總用電量",dataIndex:"Variation",key:"Variation",align:"right"}],g.value=!1):(await t("bill/calculateFee",(0,u.ux)(i)),y.value=a.bill.calculateTableData.map((e=>({name:(0,l.bF)(C,{onClick:()=>K(e)},{default:()=>[e.MeterName]}),region:e.RegionName,unit:e.MeterUnit,totalPeakKwh:e.Data.reduce(((e,a)=>e+a.PeakKwh),0).toFixed(2),totalPeakFee:e.Data.reduce(((e,a)=>e+a.PeakFee),0).toFixed(2),totalHalfPeakKwh:e.Data.reduce(((e,a)=>e+a.HalfPeakKwh),0).toFixed(2),totalHalfPeakFee:e.Data.reduce(((e,a)=>e+a.HalfPeakFee),0).toFixed(2),totalSaturdayHalfPeakKwh:e.Data.reduce(((e,a)=>e+a.SaturdayHalfPeakKwh),0).toFixed(2),totalSaturdayHalfPeakFee:e.Data.reduce(((e,a)=>e+a.SaturdayHalfPeakFee),0).toFixed(2),totalOffPeakKwh:e.Data.reduce(((e,a)=>e+a.OffPeakKwh),0).toFixed(2),totalOffPeakFee:e.Data.reduce(((e,a)=>e+a.OffPeakFee),0).toFixed(2)}))),v.value=[{title:"名稱",dataIndex:"name",key:"name",fixed:"left"},{title:"地區",dataIndex:"region",key:"region"},{title:"單位",dataIndex:"unit",key:"unit"},{title:"尖峰用電量",dataIndex:"totalPeakKwh",key:"totalPeakKwh"},{title:"尖峰電費",dataIndex:"totalPeakFee",key:"totalPeakFee"},{title:"半尖峰用電量",dataIndex:"totalHalfPeakKwh",key:"totalHalfPeakKwh"},{title:"半尖峰電費",dataIndex:"totalHalfPeakFee",key:"totalHalfPeakFee"},{title:"周六半尖峰用電量",dataIndex:"totalSaturdayHalfPeakKwh",key:"totalSaturdayHalfPeakKwh"},{title:"周六半尖峰電費",dataIndex:"totalSaturdayHalfPeakFee",key:"totalSaturdayHalfPeakFee"},{title:"離峰用電量",dataIndex:"totalOffPeakKwh",key:"totalOffPeakKwh"},{title:"離峰電費",dataIndex:"totalOffPeakFee",key:"totalOffPeakFee"}],M.value=[{title:"時間",dataIndex:"Date",key:"Date",fixed:"left"},{title:"尖峰用電度",dataIndex:"PeakKwh",key:"PeakKwh"},{title:"尖峰電費",dataIndex:"PeakFee",key:"PeakFee"},{title:"半尖峰用電度",dataIndex:"HalfPeakKwh",key:"HalfPeakKwh"},{title:"半尖峰電費",dataIndex:"HalfPeakFee",key:"HalfPeakFee"},{title:"周六半尖峰用電度",dataIndex:"SaturdayHalfPeakKwh",key:"SaturdayHalfPeakKwh"},{title:"周六半尖峰電費",dataIndex:"SaturdayHalfPeakFee",key:"SaturdayHalfPeakFee"},{title:"離峰用電度",dataIndex:"OffPeakKwh",key:"OffPeakKwh"},{title:"離峰電費",dataIndex:"OffPeakFee",key:"OffPeakFee"}],b.value=a.bill.calculateSummary,g.value=!0),S.value=!0},F=e=>{t("bill/filterCalculateTable",e.target.value)},x=(0,u.KR)(!1),T=(0,u.KR)(""),h=()=>{T.value=U()().format("YYYYMMDDHHmmss"),x.value=!0},O=()=>{x.value=!1},w=async()=>{try{"1"===i.mode?await t("bill/exportVariation",{...(0,u.ux)(i),fileName:T.value}):await t("bill/exportFee",{...(0,u.ux)(i),fileName:T.value}),x.value=!1}catch(e){c.A.error({title:"發生錯誤",content:e.message})}},W=(0,u.KR)(),D=(0,u.KR)(""),H=(0,u.KR)([]),M=(0,u.KR)([]),K=e=>{H.value=e.Data,D.value=`${e.MeterName} 詳情`,W.value=!0},E=()=>{W.value=!1};return{SHOW_PARENT:e,loading:n,modeOptions:r,labelCol:s,wrapperCol:o,schedulePeriodOptions:d,formState:i,data:y,columns:v,showSummary:g,summaryTable:b,setDate:m,setMeters:k,submitable:p,submitted:S,submit:P,exportModal:x,exportFileName:T,openExportModal:h,closeExportModal:O,exportReport:w,search:F,detailModal:W,detailModalTitle:D,detailTableData:H,detailColumns:M,closeDetailModal:E}}});const Y=(0,S.A)(X,[["render",O]]);var j=Y;function B(e,a,t,n,r,u){const s=(0,l.g2)("BasicSetting"),o=(0,l.g2)("a-tab-pane"),d=(0,l.g2)("MeterSetting"),m=(0,l.g2)("a-tabs"),c=(0,l.g2)("a-button"),k=(0,l.g2)("a-col"),p=(0,l.g2)("a-spin"),f=(0,l.g2)("a-row"),y=(0,l.g2)("a-form"),v=(0,l.g2)("sdModal"),g=(0,l.g2)("DataTables"),b=(0,l.g2)("sdCards");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(v,{key:0,title:"設定排程",visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{activeKey:e.activeTab,"onUpdate:activeKey":a[1]||(a[1]=a=>e.activeTab=a)},{default:(0,l.k6)((()=>[(0,l.bF)(o,{key:"1",tab:"基礎設定",forceRender:!0},{default:(0,l.k6)((()=>[(0,l.bF)(s,{endTimeOptions:e.endTimeOptions,repeatOptions:e.repeatOptions,formState:e.formState,rules:e.rules,onUpdate:e.updateBasicForm},null,8,["endTimeOptions","repeatOptions","formState","rules","onUpdate"])])),_:1}),(0,l.bF)(o,{key:"2",tab:"電錶設定",forceRender:!0},{default:(0,l.k6)((()=>["2"===e.activeTab?((0,l.uX)(),(0,l.Wv)(d,{key:0,modelValue:e.meterFormState,"onUpdate:modelValue":a[0]||(a[0]=a=>e.meterFormState=a),usePeriod:!0},null,8,["modelValue"])):(0,l.Q3)("",!0)])),_:1})])),_:1},8,["activeKey"]),(0,l.bF)(f,{gutter:[10,10],justify:"center"},{default:(0,l.k6)((()=>[(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(c,{type:"primary",ghost:"",style:{height:"40px"},onClick:(0,i.D$)(e.closeModal,["prevent"])},{default:(0,l.k6)((()=>[(0,l.eW)("取消")])),_:1},8,["onClick"])])),_:1}),(0,l.bF)(k,null,{default:(0,l.k6)((()=>[(0,l.bF)(c,{"html-type":"submit",type:"primary",style:{height:"40px"},disabled:e.submitingForm,onClick:(0,i.D$)(e.submitForm,["prevent"])},{default:(0,l.k6)((()=>[(0,l.eW)("儲存"),e.loading?((0,l.uX)(),(0,l.Wv)(p,{key:0,size:"small"})):(0,l.Q3)("",!0)])),_:1},8,["disabled","onClick"])])),_:1})])),_:1})])),_:1})])),_:1},8,["visible","onCancel"])):(0,l.Q3)("",!0),(0,l.bF)(b,null,{default:(0,l.k6)((()=>[e.loading?((0,l.uX)(),(0,l.Wv)(p,{key:0})):(0,l.Q3)("",!0),e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(g,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,handleAdd:e.openAddModal,handleDataSearch:e.search},null,8,["tableData","columns","addOption","handleAdd","handleDataSearch"]))])),_:1})])}var q=t(27426),V=t(80587),Q=(0,l.pM)({props:{settingOptions:{type:Array,default:()=>[]}},components:{DataTables:y.A,BasicSetting:q.A,MeterSetting:I},setup(){const{permission:e}=(0,v.J)(),{state:a,dispatch:t}=(0,f.Pj)(),n=(0,l.EW)((()=>a.bill.loading));(0,l.sV)((async()=>{const e=await Promise.all([t("bill/getSchedule"),t("schedule/getWorkOption")]);m.value=e[1].endTime,i.value=e[1].repeat}));const s=[{title:"排程名稱",dataIndex:"name",key:"name"},{title:"開始時間",dataIndex:"startTime",key:"startTime"},{title:"重複",dataIndex:"repeat",key:"repeat"},{title:"操作",dataIndex:"action",key:"action"}],o=(0,l.EW)((()=>a.bill.scheduleTableData.map((a=>({name:a.name,startTime:a.startTime,repeat:(0,V.Op)(a.repeat.cron,a.repeat.count),action:(0,l.bF)(r.J9,null,{default:()=>[e.update&&(0,l.bF)("span",{onClick:()=>C(a)},[(0,l.bF)((0,l.g2)("unicon"),{name:"edit"},null)]),e.delete&&(0,l.bF)("span",{onClick:()=>H(a.id)},[(0,l.bF)((0,l.g2)("unicon"),{name:"trash"},null)])]})}))))),d=e=>{t("bill/filterSchedule",e.target.value)},i=(0,u.KR)([]),m=(0,u.KR)([]),p=(0,u.KR)("1"),y=(0,u.KR)(!1),g={lg:6,md:9,xs:24},b={lg:18,md:15,xs:24},S=(0,u.Kh)({display:"flex",marginTop:"0.2rem"}),P=(0,u.KR)();(0,l.Gt)("basicForm",P);const F=(0,u.Kh)({id:null,name:"",startTime:null,repeatCount:1,repeat:null,repeatValue:null,repeatLabel:null,endTime:"1",endTimeAddition:{}}),x=e=>{Object.assign(F,e)},T=async()=>{const e=m.value.find((e=>e.id===F.endTime));return e&&e.settings&&!F.endTimeAddition[F.endTime]?Promise.reject("請輸入"):Promise.resolve()},h={name:{required:!0,trigger:"blur",message:"請填入名稱"},tags:{required:!0,trigger:"blur",message:"請選擇"},startTime:{required:!0,trigger:"blur",message:"請選擇"},repeatCount:{required:!0,trigger:"blur",message:"請選擇"},repeat:{required:!0,trigger:"blur",message:"請選擇"},repeatValue:{required:!0,trigger:"blur",message:"請選擇"},endTimeAddition:[{required:!0,trigger:"blur",message:"請選擇"},{validator:T}]},O={meters:[],date:1},L=(0,u.Kh)(O),w=()=>{const e={id:null,name:"",startTime:null,season:null,repeatCount:1,repeat:null,repeatValue:null,repeatLabel:null,endTime:"1",endTimeAddition:{}};Object.assign(F,e),Object.assign(L,O),y.value=!0},C=({id:e,name:a,startTime:t,season:l,repeat:n,endTime:r,meter:u})=>{const s={id:e,name:a,startTime:U()(t,"YYYY-MM-DD HH:mm:ss"),season:l?l.id:null,repeatCount:n.count,repeat:n.type,repeatValue:n.cron,repeatLabel:n.text,endTime:r.id,endTimeAddition:{[r.id]:U()(r.value,"YYYY-MM-DD HH:mm:ss").isValid()?U()(r.value,"YYYY-MM-DD HH:mm:ss"):r.value}};Object.assign(F,s);const o={meters:u.meters,date:u.date};Object.assign(L,o),y.value=!0},W=(0,u.KR)(!1),D=async()=>{try{W.value=!0;const e=[];if(await P.value.validateFields().catch((()=>{e.push("基礎設定")})),0===L.meters.length&&e.push("電錶設定"),e.length>0){let a="";return e.forEach((e=>a+=`"${e}" `)),a+="尚未設置完成",c.A.error({title:"請將欄位填寫完整",content:a}),void(W.value=!1)}const a={...(0,u.ux)(F),startTime:F.startTime.format("YYYY-MM-DD HH:mm:ss"),endTimeValue:U()(F.endTimeAddition[F.endTime],"YYYY-MM-DD HH:mm:ss").isValid()?F.endTimeAddition[F.endTime].format("YYYY-MM-DD HH:mm:ss"):F.endTimeAddition[F.endTime]};let l;a.id?(l="編輯成功",await t("bill/editSchedule",a)):(l="新增成功",await t("bill/addSchedule",a)),W.value=!1,y.value=!1,k.A.success({message:l})}catch(e){W.value=!1,c.A.error({title:"發生錯誤",content:e.message})}},_=()=>{y.value=!1},H=async e=>{c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:n.value,onOk:async()=>{try{await t("schedule/deleteWork",e),k.A.success({message:"刪除成功"})}catch(a){c.A.error({title:"發生錯誤",content:a.message})}}})};return{permission:e,loading:n,columns:s,tableData:o,repeatOptions:i,endTimeOptions:m,activeTab:p,modal:y,labelCol:g,wrapperCol:b,radioStyle:S,formState:F,updateBasicForm:x,rules:h,meterFormState:L,openAddModal:w,submitingForm:W,submitForm:D,closeModal:_,search:d}}});const $=(0,S.A)(Q,[["render",B]]);var J=$;function z(e,a,t,n,r,u){const s=(0,l.g2)("a-spin"),o=(0,l.g2)("DataTables"),d=(0,l.g2)("sdCards");return(0,l.uX)(),(0,l.Wv)(d,null,{default:(0,l.k6)((()=>[e.loading?((0,l.uX)(),(0,l.Wv)(s,{key:0})):(0,l.Q3)("",!0),e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(o,{key:1,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,handleDataSearch:e.search},null,8,["tableData","columns","handleDataSearch"]))])),_:1})}var G=(0,l.pM)({components:{DataTables:y.A},setup(){const{state:e,dispatch:a}=(0,f.Pj)();(0,l.sV)((async()=>{a("bill/getAllBill")}));const t=(0,l.EW)((()=>e.bill.loading)),n=[{title:"檔案名稱",dataIndex:"name",key:"name"},{title:"操作",dataIndex:"action",key:"action"}],s=(0,l.EW)((()=>e.bill.billListTableData.map((e=>({name:e.name,action:(0,l.bF)(r.J9,null,{default:()=>[(0,l.bF)("span",{onClick:()=>o(e)},[(0,l.bF)((0,l.g2)("unicon"),{name:"download-alt"},null)])]})}))))),o=()=>{const e=(0,u.KR)("Hello World"),a=new Blob([e.value],{type:"text/plain"}),t=URL.createObjectURL(a),l=document.createElement("a");l.href=t,l.download="filename.txt",l.click(),URL.revokeObjectURL(t)},d=e=>{a("bill/filterBill",e.target.value)};return{loading:t,columns:n,tableData:s,downloadBill:o,search:d}}});const Z=(0,S.A)(G,[["render",z]]);var ee=Z;function ae(e,a,t,n,r,u){const s=(0,l.g2)("a-spin"),o=(0,l.g2)("a-input"),d=(0,l.g2)("a-form-item"),m=(0,l.g2)("a-select"),c=(0,l.g2)("a-select-option"),k=(0,l.g2)("TagFilter"),p=(0,l.g2)("Notice"),f=(0,l.g2)("sdButton"),y=(0,l.g2)("a-col"),v=(0,l.g2)("a-row"),g=(0,l.g2)("a-form"),b=(0,l.g2)("sdModal"),S=(0,l.g2)("DataTables");return(0,l.uX)(),(0,l.CE)(l.FK,null,[e.loading?((0,l.uX)(),(0,l.Wv)(s,{key:0})):(0,l.Q3)("",!0),e.modal?((0,l.uX)(),(0,l.Wv)(b,{key:1,title:e.settings.title,visible:e.modal,onCancel:e.closeModal},{default:(0,l.k6)((()=>[(0,l.bF)(g,{model:e.settings,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,labelAlign:"left",rules:e.rules,onFinish:e.saveSetting},{default:(0,l.k6)((()=>[(0,l.bF)(d,{label:"電表名稱",name:"name"},{default:(0,l.k6)((()=>[(0,l.bF)(o,{value:e.settings.name,"onUpdate:value":a[0]||(a[0]=a=>e.settings.name=a),ref:"autofocus"},null,8,["value"])])),_:1}),(0,l.bF)(d,{label:"契約",name:"contract"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{value:e.settings.contract,"onUpdate:value":a[1]||(a[1]=a=>e.settings.contract=a),options:e.contractOptions},null,8,["value","options"])])),_:1}),(0,l.bF)(d,{label:"地區",name:"region"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{value:e.settings.region,"onUpdate:value":a[2]||(a[2]=a=>e.settings.region=a)},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.regionOptions,(e=>((0,l.uX)(),(0,l.Wv)(c,{key:e.Id,value:e.Id},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e.Name),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])])),_:1}),(0,l.bF)(d,{label:"用電單位",name:"unit"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{value:e.settings.unit,"onUpdate:value":a[3]||(a[3]=a=>e.settings.unit=a),options:e.unitOptions},null,8,["value","options"])])),_:1}),(0,l.bF)(d,{label:"用電量測點",name:"electric"},{default:(0,l.k6)((()=>[(0,l.bF)(k,{multiple:!1,value:e.settings.electric,title:`${e.settings.name} 選擇測點`,onSetSingleTag:e.setElectric},null,8,["value","title","onSetSingleTag"])])),_:1}),(0,l.bF)(d,{label:"功率測點",name:"power"},{default:(0,l.k6)((()=>[(0,l.bF)(k,{multiple:!1,value:e.settings.power,title:`${e.settings.name} 選擇測點`,onSetSingleTag:e.setPower},null,8,["value","title","onSetSingleTag"])])),_:1}),(0,l.bF)(p,{content:e.noticeContent},null,8,["content"]),(0,l.bF)(v,{gutter:[5,10],align:"center",style:{"margin-top":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(f,{class:"act-btn",type:"primary","html-type":"submit",disabled:e.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 儲存 "),(0,l.bo)((0,l.bF)(s,{size:"small"},null,512),[[i.aG,e.loading]])])),_:1},8,["disabled"])])),_:1}),(0,l.bF)(y,null,{default:(0,l.k6)((()=>[(0,l.bF)(f,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","label-col","wrapper-col","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0),e.loading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.Wv)(S,{key:2,filterOption:!0,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:e.permission.create,handleAdd:e.openAddModal,handleDataSearch:e.search},null,8,["tableData","columns","addOption","handleAdd","handleDataSearch"]))],64)}const te=s.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`;var le=t(82046),ne=t(31858),re=(0,l.pM)({components:{DataTables:y.A,TagFilter:le.A,Notice:ne.A},setup(){const e=(0,u.KR)("注意!\n  &nbsp;&nbsp;&nbsp;&nbsp;被綁定的測點將會每分鐘記錄一次歷史紀錄，原本的測點設定將會失效");(0,l.sV)((async()=>{const e=await n("tags/getTagsRegions");a.value=e}));const a=(0,u.KR)([]),{permission:t}=(0,v.J)(),{dispatch:n}=(0,f.Pj)(),r=(0,u.KR)(!1),s=()=>{r.value=!1},o=()=>{Object.assign(P,b),r.value=!0,(0,l.dY)((()=>{g.value.focus()}))},d=e=>{const a={title:`編輯${e.name}`,...e};Object.assign(P,a),r.value=!0,(0,l.dY)((()=>{g.value.focus()}))},i=[{title:"名稱",dataIndex:"name",key:"name"},{title:"地區",dataIndex:"region",key:"region"},{title:"操作",dataIndex:"action",key:"action"}],m=(0,l.EW)((()=>F.bill.settingListTableData.map((e=>({label:e.name,value:e.id}))))),p=[{label:"Mwh",value:"Mwh"},{label:"Kwh",value:"Kwh"}],y=(0,l.EW)((()=>F.bill.metersTableData.map((e=>({name:e.name,region:e.regionName,action:(0,l.bF)(te,null,{default:()=>[t.update&&(0,l.bF)("span",{onClick:()=>d(e)},[(0,l.bF)((0,l.g2)("unicon"),{name:"edit"},null)]),t.delete&&(0,l.bF)("span",{onClick:()=>W(e.id)},[(0,l.bF)((0,l.g2)("unicon"),{name:"trash"},null)])]})}))))),g=(0,u.KR)(),b={id:null,title:"新增電錶",name:null,region:null,contract:null,unit:"Kwh",power:null,electric:null},S={name:[{required:!0,message:"請輸入名稱",trigger:"blur"}],region:[{required:!0,message:"請選擇地區",trigger:"blur"}],contract:[{required:!0,message:"請選擇契約",trigger:"blur"}],power:[{required:!0,message:"請選擇功率點",trigger:"blur"}],electric:[{required:!0,message:"請選擇用電量點",trigger:"blur"}]},P=(0,u.Kh)({}),{state:F}=(0,f.Pj)(),x=(0,l.EW)((()=>F.bill.loading)),T={lg:8,md:9,xs:24},h={lg:16,md:15,xs:24},O=({value:e})=>{P.electric=e},L=({value:e})=>{P.power=e},w=e=>{n("bill/filterMeterTable",e.target.value)},C=async()=>{try{let e;P.id?(await n("bill/editMeter",P),e="修改成功"):(await n("bill/addMeter",P),e="新增成功"),r.value=!1,k.A.success({message:e})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}},W=e=>{c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:x.value,onOk:async()=>{try{await n("bill/deleteMeter",e),c.A.success({content:"刪除成功"})}catch(a){c.A.error({title:"發生錯誤",content:a.message})}}})};return{noticeContent:e,regionOptions:a,permission:t,loading:x,modal:r,openAddModal:o,closeModal:s,columns:i,contractOptions:m,unitOptions:p,tableData:y,autofocus:g,settings:P,rules:S,labelCol:T,wrapperCol:h,setElectric:O,setPower:L,search:w,saveSetting:C}}});const ue=(0,S.A)(re,[["render",ae]]);var se=ue;function oe(e,a,t,n,r,u){const s=(0,l.g2)("HighTwo"),o=(0,l.g2)("HighThree"),d=(0,l.g2)("a-spin"),m=(0,l.g2)("sdButton"),c=(0,l.g2)("a-col"),k=(0,l.g2)("a-row"),p=(0,l.g2)("a-form"),f=(0,l.g2)("sdModal"),y=(0,l.g2)("a-select"),v=(0,l.g2)("DataTables");return(0,l.uX)(),(0,l.CE)("div",null,[e.modal?((0,l.uX)(),(0,l.Wv)(f,{key:0,title:e.formState.title,visible:e.modal,onCancel:e.closeModal,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(p,{model:e.formState,rules:e.rules,onFinish:e.submit},{default:(0,l.k6)((()=>["1"===e.fee||"3"===e.fee?((0,l.uX)(),(0,l.Wv)(s,{key:0,value:e.formState,"onUpdate:value":a[0]||(a[0]=a=>e.formState=a)},null,8,["value"])):(0,l.Q3)("",!0),"2"===e.fee||"4"===e.fee?((0,l.uX)(),(0,l.Wv)(o,{key:1,value:e.formState,"onUpdate:value":a[1]||(a[1]=a=>e.formState=a)},null,8,["value"])):(0,l.Q3)("",!0),(0,l.bF)(k,{gutter:[5,10],align:"center",style:{"margin-top":"1rem"}},{default:(0,l.k6)((()=>[(0,l.bF)(c,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{class:"act-btn",type:"primary","html-type":"submit",disabled:e.loading},{default:(0,l.k6)((()=>[(0,l.eW)(" 儲存 "),(0,l.bo)((0,l.bF)(d,{size:"small"},null,512),[[i.aG,e.loading]])])),_:1},8,["disabled"])])),_:1}),(0,l.bF)(c,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{class:"act-btn",type:"light",onClick:e.closeModal},{default:(0,l.k6)((()=>[(0,l.eW)(" 取消 ")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["model","rules","onFinish"])])),_:1},8,["title","visible","onCancel"])):(0,l.Q3)("",!0),(0,l.eW)(" 選擇計費方式: "),(0,l.bF)(y,{options:e.feeOptions,value:e.fee,"onUpdate:value":a[2]||(a[2]=a=>e.fee=a),style:{width:"200px","margin-left":"1rem"}},null,8,["options","value"]),!e.loading&&e.fee?((0,l.uX)(),(0,l.Wv)(v,{key:1,filterOption:!1,filterOnchange:!0,tableData:e.tableData,columns:e.columns,rowSelection:!1,addOption:!0,handleAdd:e.openAddModal},null,8,["tableData","columns","handleAdd"])):(0,l.Q3)("",!0)])}const de=s.Ay.div`
    display:flex;
    justify-content:end;
    svg{
       height:20px;
       cursor:pointer;
       margin:0 5px;
    }
`,ie={key:0,class:"d-flex align-items-center"},me=(0,l.Lk)("p",{style:{"white-space":"nowrap","margin-bottom":"0","margin-right":"0.5rem"}}," 起始年份: ",-1),ce=(0,l.Lk)("p",{style:{"white-space":"nowrap","margin-bottom":"0","margin-right":"0.5rem"},type:"number"}," 起始月份: ",-1),ke=(0,l.Lk)("div",{style:{"font-size":"1.2rem"}},"單位:元",-1),pe={class:"tg"},fe=(0,l.Lk)("tr",null,[(0,l.Lk)("td",{class:"tg-0lax",colspan:"6",rowspan:"2"},"分類"),(0,l.Lk)("td",{class:"tg-0lax",colspan:"2"},"供電")],-1),ye={class:"tg-0lax"},ve=(0,l.Lk)("br",null,null,-1),ge=(0,l.Lk)("p",{style:{"margin-bottom":"0"}},"至",-1),be=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),Se=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},[(0,l.eW)("基本"),(0,l.Lk)("br"),(0,l.eW)("電費")],-1),Pe=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"經常契約",-1),Fe=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},[(0,l.eW)("每瓩"),(0,l.Lk)("br"),(0,l.eW)("每月")],-1),xe={class:"tg-0lax"},Te={class:"tg-0lax"},he=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"非夏月契約",-1),Oe=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Le={class:"tg-0lax"},we=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"週六半尖峰契約",-1),Ce={class:"tg-0lax"},We={class:"tg-0lax"},Ue=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"離峰契約",-1),De={class:"tg-0lax"},_e={class:"tg-0lax"},He=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"9"},[(0,l.eW)("流動"),(0,l.Lk)("br"),(0,l.eW)("電費")],-1),Me=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},[(0,l.eW)("週一"),(0,l.Lk)("br"),(0,l.eW)("至"),(0,l.Lk)("br"),(0,l.eW)("週五")],-1),Ke=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"尖峰時間",-1),Ee=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),Ae={class:"tg-0lax"},Re=["onClick"],Ne=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"9"},"每度",-1),Ie={class:"tg-0lax"},Xe=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Ye=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),je={class:"tg-0lax"},Be=["onClick"],qe=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Ve={class:"tg-0lax"},Qe=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"離峰時間",-1),$e=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),Je={class:"tg-0lax"},ze=["onClick"],Ge={class:"tg-0lax"},Ze=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),ea=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),aa={class:"tg-0lax"},ta=["onClick"],la=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),na={class:"tg-0lax"},ra=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},"週六",-1),ua=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"半尖峰時間",-1),sa=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),oa={class:"tg-0lax"},da=["onClick"],ia={class:"tg-0lax"},ma=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),ca=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),ka={class:"tg-0lax"},pa=["onClick"],fa=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),ya={class:"tg-0lax"},va=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"離峰時間",-1),ga=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),ba={class:"tg-0lax"},Sa=["onClick"],Pa={class:"tg-0lax"},Fa=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),xa=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),Ta={class:"tg-0lax"},ha=["onClick"],Oa=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),La={class:"tg-0lax"},wa=(0,l.Lk)("td",{class:"tg-0lax"},[(0,l.eW)("週日及"),(0,l.Lk)("br"),(0,l.eW)("離峰日")],-1),Ca=(0,l.Lk)("td",{class:"tg-0lax"},"離峰時間",-1),Wa=(0,l.Lk)("td",{class:"tg-0lax",colspan:"2"},"全日",-1),Ua={class:"tg-0lax"},Da={class:"tg-0lax"};function _a(e,a,t,n,r,u){const s=(0,l.g2)("a-input"),o=(0,l.g2)("a-row"),d=(0,l.g2)("a-select-option"),i=(0,l.g2)("a-select"),m=(0,l.g2)("a-time-picker"),c=(0,l.g2)("a-form");return(0,l.uX)(),(0,l.Wv)(c,{style:{overflow:"auto",width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(o,{style:{"margin-bottom":"1rem"},justify:"space-between",align:"middle"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",null,["create"===e.formState.mode?((0,l.uX)(),(0,l.CE)("div",ie,[me,(0,l.bF)(s,{value:e.formState.year,"onUpdate:value":a[0]||(a[0]=a=>e.formState.year=a),style:{width:"100px","margin-right":"0.5rem"},type:"number"},null,8,["value"]),ce,(0,l.bF)(s,{value:e.formState.month,"onUpdate:value":a[1]||(a[1]=a=>e.formState.month=a),style:{width:"100px","margin-right":"0.5rem"}},null,8,["value"])])):(0,l.Q3)("",!0)]),ke])),_:1}),(0,l.Lk)("table",pe,[(0,l.Lk)("tbody",null,[fe,(0,l.Lk)("tr",null,[(0,l.Lk)("td",ye,[(0,l.eW)(" 夏月 "),ve,(0,l.bF)(i,{value:e.startSummerMonth,"onUpdate:value":a[2]||(a[2]=a=>e.startSummerMonth=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(12,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),(0,l.eW)(" / "),(0,l.bF)(i,{value:e.startSummerDate,"onUpdate:value":a[3]||(a[3]=a=>e.startSummerDate=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(31,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),ge,(0,l.bF)(i,{value:e.endSummerMonth,"onUpdate:value":a[4]||(a[4]=a=>e.endSummerMonth=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(12,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),(0,l.eW)(" / "),(0,l.bF)(i,{value:e.endSummerDate,"onUpdate:value":a[5]||(a[5]=a=>e.endSummerDate=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(31,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"])]),be]),(0,l.Lk)("tr",null,[Se,Pe,Fe,(0,l.Lk)("td",xe,[(0,l.bF)(s,{value:e.formState.summer.contractRegular,"onUpdate:value":a[6]||(a[6]=a=>e.formState.summer.contractRegular=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",Te,[(0,l.bF)(s,{value:e.formState.noSummer.contractRegular,"onUpdate:value":a[7]||(a[7]=a=>e.formState.noSummer.contractRegular=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[he,Oe,(0,l.Lk)("td",Le,[(0,l.bF)(s,{value:e.formState.noSummer.contractNonSummer,"onUpdate:value":a[8]||(a[8]=a=>e.formState.noSummer.contractNonSummer=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[we,(0,l.Lk)("td",Ce,[(0,l.bF)(s,{value:e.formState.summer.contractSaturdayHalfPeak,"onUpdate:value":a[9]||(a[9]=a=>e.formState.summer.contractSaturdayHalfPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",We,[(0,l.bF)(s,{value:e.formState.noSummer.contractSaturdayHalfPeak,"onUpdate:value":a[10]||(a[10]=a=>e.formState.noSummer.contractSaturdayHalfPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[Ue,(0,l.Lk)("td",De,[(0,l.bF)(s,{value:e.formState.summer.contractOffPeak,"onUpdate:value":a[11]||(a[11]=a=>e.formState.summer.contractOffPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",_e,[(0,l.bF)(s,{value:e.formState.noSummer.contractOffPeak,"onUpdate:value":a[12]||(a[12]=a=>e.formState.noSummer.contractOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[He,Me,Ke,Ee,(0,l.Lk)("td",Ae,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayPeak(t)}," 刪除 ",8,Re)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[13]||(a[13]=a=>e.addWeekdayPeak())}," 新增時段 + ")]),Ne,(0,l.Lk)("td",Ie,[(0,l.bF)(s,{value:e.formState.summer.weekdaysPeak,"onUpdate:value":a[14]||(a[14]=a=>e.formState.summer.weekdaysPeak=a),type:"number"},null,8,["value"])]),Xe]),(0,l.Lk)("tr",null,[Ye,(0,l.Lk)("td",je,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayNoSummerPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayNoSummerPeak(t)}," 刪除 ",8,Be)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[15]||(a[15]=a=>e.addWeekdayNoSummerPeak())}," 新增時段 + ")]),qe,(0,l.Lk)("td",Ve,[(0,l.bF)(s,{value:e.formState.noSummer.weekdaysPeak,"onUpdate:value":a[16]||(a[16]=a=>e.formState.noSummer.weekdaysPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[Qe,$e,(0,l.Lk)("td",Je,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayOffPeak(t)}," 刪除 ",8,ze)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[17]||(a[17]=a=>e.addWeekdayOffPeak())}," 新增時段 + ")]),(0,l.Lk)("td",Ge,[(0,l.bF)(s,{value:e.formState.summer.weekdaysOffPeak,"onUpdate:value":a[18]||(a[18]=a=>e.formState.summer.weekdaysOffPeak=a),type:"number"},null,8,["value"])]),Ze]),(0,l.Lk)("tr",null,[ea,(0,l.Lk)("td",aa,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayNoSummerOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayNoSummerOffPeak(t)}," 刪除 ",8,ta)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[19]||(a[19]=a=>e.addWeekdayNoSummerOffPeak())}," 新增時段 + ")]),la,(0,l.Lk)("td",na,[(0,l.bF)(s,{value:e.formState.noSummer.weekdaysOffPeak,"onUpdate:value":a[20]||(a[20]=a=>e.formState.noSummer.weekdaysOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[ra,ua,sa,(0,l.Lk)("td",oa,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayHalfPeak(t)}," 刪除 ",8,da)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[21]||(a[21]=a=>e.addSaturdayHalfPeak())}," 新增時段 + ")]),(0,l.Lk)("td",ia,[(0,l.bF)(s,{value:e.formState.summer.saturdayHalfPeak,"onUpdate:value":a[22]||(a[22]=a=>e.formState.summer.saturdayHalfPeak=a),type:"number"},null,8,["value"])]),ma]),(0,l.Lk)("tr",null,[ca,(0,l.Lk)("td",ka,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayNoSummerHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayNoSummerHalfPeak(t)}," 刪除 ",8,pa)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[23]||(a[23]=a=>e.addSaturdayNoSummerHalfPeak())}," 新增時段 + ")]),fa,(0,l.Lk)("td",ya,[(0,l.bF)(s,{value:e.formState.noSummer.saturdayHalfPeak,"onUpdate:value":a[24]||(a[24]=a=>e.formState.noSummer.saturdayHalfPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[va,ga,(0,l.Lk)("td",ba,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayOffPeak(t)}," 刪除 ",8,Sa)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[25]||(a[25]=a=>e.addSaturdayOffPeak())}," 新增時段 + ")]),(0,l.Lk)("td",Pa,[(0,l.bF)(s,{value:e.formState.summer.saturdayOffPeak,"onUpdate:value":a[26]||(a[26]=a=>e.formState.summer.saturdayOffPeak=a),type:"number"},null,8,["value"])]),Fa]),(0,l.Lk)("tr",null,[xa,(0,l.Lk)("td",Ta,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayNoSummerOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayNoSummerOffPeak(t)}," 刪除 ",8,ha)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[27]||(a[27]=a=>e.addSaturdayNoSummerOffPeak())}," 新增時段 + ")]),Oa,(0,l.Lk)("td",La,[(0,l.bF)(s,{value:e.formState.noSummer.saturdayOffPeak,"onUpdate:value":a[28]||(a[28]=a=>e.formState.noSummer.saturdayOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[wa,Ca,Wa,(0,l.Lk)("td",Ua,[(0,l.bF)(s,{value:e.formState.summer.sundayAndOffDayPeak,"onUpdate:value":a[29]||(a[29]=a=>e.formState.summer.sundayAndOffDayPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",Da,[(0,l.bF)(s,{value:e.formState.noSummer.sundayAndOffDayPeak,"onUpdate:value":a[30]||(a[30]=a=>e.formState.noSummer.sundayAndOffDayPeak=a),type:"number"},null,8,["value"])])])])])])),_:1})}var Ha=(0,l.pM)({props:{value:{type:Object,default:null}},setup(e,{emit:a}){const t=(0,u.Kh)(e.value),n=(0,u.KR)(t.summerMonth.startDate.split("-")[0]),r=(0,u.KR)(t.summerMonth.startDate.split("-")[1]);(0,l.wB)((()=>[r.value,n.value]),(()=>{t.summerMonth.startDate=`${n.value}-${r.value}`}),{deep:!0});const s=(0,u.KR)(t.summerMonth.endDate.split("-")[0]),o=(0,u.KR)(t.summerMonth.endDate.split("-")[1]);(0,l.wB)((()=>[s.value,o.value]),(()=>{t.summerMonth.endDate=`${s.value}-${o.value}`}),{deep:!0});const d=(0,u.KR)(t.weekDay.filter((e=>"Peak"===e.PeakType&&"Summer"===e.UseType))),i=()=>{d.value.push({PeakType:"Peak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},m=e=>{d.value.splice(e,1)},c=(0,u.KR)(t.weekDay.filter((e=>"Peak"===e.PeakType&&"NonSummer"===e.UseType))),k=()=>{c.value.push({PeakType:"Peak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},p=e=>{c.value.splice(e,1)},f=(0,u.KR)(t.weekDay.filter((e=>"OffPeak"===e.PeakType&&"Summer"===e.UseType))),y=()=>{f.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},v=e=>{f.value.splice(e,1)},g=(0,u.KR)(t.weekDay.filter((e=>"OffPeak"===e.PeakType&&"NonSummer"===e.UseType))),b=()=>{g.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},S=e=>{g.value.splice(e,1)};(0,l.wB)((()=>[...d.value,...f.value,...c.value,...g.value]),(()=>{t.weekDay=[...d.value,...f.value,...c.value,...g.value]}),{deep:!0});const P=(0,u.KR)(t.saturday.filter((e=>"SaturdayHalfPeak"===e.PeakType&&"Summer"===e.UseType))),F=()=>{P.value.push({PeakType:"SaturdayHalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},x=e=>{P.value.splice(e,1)},T=(0,u.KR)(t.saturday.filter((e=>"SaturdayHalfPeak"===e.PeakType&&"NonSummer"===e.UseType))),h=()=>{T.value.push({PeakType:"SaturdayHalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},O=e=>{T.value.splice(e,1)},L=(0,u.KR)(t.saturday.filter((e=>"OffPeak"===e.PeakType&&"Summer"===e.UseType))),w=()=>{L.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},C=e=>{L.value.splice(e,1)},W=(0,u.KR)(t.saturday.filter((e=>"OffPeak"===e.PeakType&&"NonSummer"===e.UseType))),D=()=>{W.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},_=e=>{W.value.splice(e,1)};return(0,l.wB)((()=>[...P.value,...T.value,...L.value,...W.value]),(()=>{t.saturday=[...P.value,...T.value,...L.value,...W.value]}),{deep:!0}),(0,l.wB)((()=>t),(e=>{a("update:value",e)}),{deep:!0}),{addSaturdayHalfPeak:F,addSaturdayNoSummerHalfPeak:h,addSaturdayNoSummerOffPeak:D,addSaturdayOffPeak:w,addWeekdayNoSummerOffPeak:b,addWeekdayNoSummerPeak:k,addWeekdayOffPeak:y,addWeekdayPeak:i,delSaturdayHalfPeak:x,delSaturdayNoSummerHalfPeak:O,delSaturdayNoSummerOffPeak:_,delSaturdayOffPeak:C,delWeekdayNoSummerOffPeak:S,delWeekdayNoSummerPeak:p,delWeekdayOffPeak:v,delWeekdayPeak:m,endSummerDate:o,endSummerMonth:s,formState:t,saturdayHalfPeak:P,saturdayNoSummerHalfPeak:T,saturdayNoSummerOffPeak:W,saturdayOffPeak:L,startSummerDate:r,startSummerMonth:n,weekdayNoSummerOffPeak:g,weekdayNoSummerPeak:c,weekdayOffPeak:f,weekdayPeak:d}}});const Ma=(0,S.A)(Ha,[["render",_a]]);var Ka=Ma;const Ea={key:0,class:"d-flex align-items-center"},Aa=(0,l.Lk)("p",{style:{"white-space":"nowrap","margin-bottom":"0","margin-right":"0.5rem"}}," 起始年份: ",-1),Ra=(0,l.Lk)("p",{style:{"white-space":"nowrap","margin-bottom":"0","margin-right":"0.5rem"},type:"number"}," 起始月份: ",-1),Na=(0,l.Lk)("div",{style:{"font-size":"1.2rem"}},"單位:元",-1),Ia={class:"tg"},Xa=(0,l.Lk)("tr",null,[(0,l.Lk)("td",{class:"tg-0lax",colspan:"6",rowspan:"2"},"分類"),(0,l.Lk)("td",{class:"tg-0lax",colspan:"2"},"供電")],-1),Ya={class:"tg-0lax"},ja=(0,l.Lk)("br",null,null,-1),Ba=(0,l.Lk)("p",{style:{"margin-bottom":"0"}},"至",-1),qa=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),Va=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},[(0,l.eW)("基本"),(0,l.Lk)("br"),(0,l.eW)("電費")],-1),Qa=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"經常契約",-1),$a=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},[(0,l.eW)("每瓩"),(0,l.Lk)("br"),(0,l.eW)("每月")],-1),Ja={class:"tg-0lax"},za={class:"tg-0lax"},Ga=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"半尖峰契約",-1),Za={class:"tg-0lax"},et={class:"tg-0lax"},at=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"週六半尖峰契約",-1),tt={class:"tg-0lax"},lt={class:"tg-0lax"},nt=(0,l.Lk)("td",{class:"tg-0lax",colspan:"4"},"離峰契約",-1),rt={class:"tg-0lax"},ut={class:"tg-0lax"},st=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"10"},[(0,l.eW)("流動"),(0,l.Lk)("br"),(0,l.eW)("電費")],-1),ot=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"5"},[(0,l.eW)("週一"),(0,l.Lk)("br"),(0,l.eW)("至"),(0,l.Lk)("br"),(0,l.eW)("週五")],-1),dt=(0,l.Lk)("td",{class:"tg-0lax"},"尖峰時間",-1),it=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),mt={class:"tg-0lax"},ct=["onClick"],kt=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"10"},"每度",-1),pt={class:"tg-0lax"},ft=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),yt=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"半尖峰時間",-1),vt=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),gt={class:"tg-0lax"},bt=["onClick"],St={class:"tg-0lax"},Pt=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Ft=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),xt={class:"tg-0lax"},Tt=["onClick"],ht=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Ot={class:"tg-0lax"},Lt=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"離峰時間",-1),wt=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),Ct={class:"tg-0lax"},Wt=["onClick"],Ut={class:"tg-0lax"},Dt=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),_t=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),Ht={class:"tg-0lax"},Mt=["onClick"],Kt=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Et={class:"tg-0lax"},At=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"4"},"週六",-1),Rt=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"半尖峰時間",-1),Nt=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),It={class:"tg-0lax"},Xt=["onClick"],Yt={class:"tg-0lax"},jt=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),Bt=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),qt={class:"tg-0lax"},Vt=["onClick"],Qt=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),$t={class:"tg-0lax"},Jt=(0,l.Lk)("td",{class:"tg-0lax",rowspan:"2"},"離峰時間",-1),zt=(0,l.Lk)("td",{class:"tg-0lax"},"夏月",-1),Gt={class:"tg-0lax"},Zt=["onClick"],el={class:"tg-0lax"},al=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),tl=(0,l.Lk)("td",{class:"tg-0lax"},"非夏月",-1),ll={class:"tg-0lax"},nl=["onClick"],rl=(0,l.Lk)("td",{class:"tg-0lax"},"-",-1),ul={class:"tg-0lax"},sl=(0,l.Lk)("td",{class:"tg-0lax"},[(0,l.eW)("週日及"),(0,l.Lk)("br"),(0,l.eW)("離峰日")],-1),ol=(0,l.Lk)("td",{class:"tg-0lax"},"離峰時間",-1),dl=(0,l.Lk)("td",{class:"tg-0lax",colspan:"2"},"全日",-1),il={class:"tg-0lax"},ml={class:"tg-0lax"};function cl(e,a,t,n,r,u){const s=(0,l.g2)("a-input"),o=(0,l.g2)("a-row"),d=(0,l.g2)("a-select-option"),i=(0,l.g2)("a-select"),m=(0,l.g2)("a-time-picker"),c=(0,l.g2)("a-form");return(0,l.uX)(),(0,l.Wv)(c,{style:{overflow:"auto",width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(o,{style:{"margin-bottom":"1rem"},justify:"space-between",align:"middle"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",null,["create"===e.formState.mode?((0,l.uX)(),(0,l.CE)("div",Ea,[Aa,(0,l.bF)(s,{value:e.formState.year,"onUpdate:value":a[0]||(a[0]=a=>e.formState.year=a),style:{width:"100px","margin-right":"0.5rem"},type:"number"},null,8,["value"]),Ra,(0,l.bF)(s,{value:e.formState.month,"onUpdate:value":a[1]||(a[1]=a=>e.formState.month=a),style:{width:"100px","margin-right":"0.5rem"}},null,8,["value"])])):(0,l.Q3)("",!0)]),Na])),_:1}),(0,l.Lk)("table",Ia,[(0,l.Lk)("tbody",null,[Xa,(0,l.Lk)("tr",null,[(0,l.Lk)("td",Ya,[(0,l.eW)(" 夏月 "),ja,(0,l.bF)(i,{value:e.startSummerMonth,"onUpdate:value":a[2]||(a[2]=a=>e.startSummerMonth=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(12,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),(0,l.eW)(" / "),(0,l.bF)(i,{value:e.startSummerDate,"onUpdate:value":a[3]||(a[3]=a=>e.startSummerDate=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(31,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),Ba,(0,l.bF)(i,{value:e.endSummerMonth,"onUpdate:value":a[4]||(a[4]=a=>e.endSummerMonth=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(12,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"]),(0,l.eW)(" / "),(0,l.bF)(i,{value:e.endSummerDate,"onUpdate:value":a[5]||(a[5]=a=>e.endSummerDate=a),class:"custom-select"},{default:(0,l.k6)((()=>[((0,l.uX)(),(0,l.CE)(l.FK,null,(0,l.pI)(31,(e=>(0,l.bF)(d,{key:e,value:e<10?"0"+e:e},{default:(0,l.k6)((()=>[(0,l.eW)((0,x.v_)(e<10?"0"+e:e),1)])),_:2},1032,["value"]))),64))])),_:1},8,["value"])]),qa]),(0,l.Lk)("tr",null,[Va,Qa,$a,(0,l.Lk)("td",Ja,[(0,l.bF)(s,{value:e.formState.summer.contractRegular,"onUpdate:value":a[6]||(a[6]=a=>e.formState.summer.contractRegular=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",za,[(0,l.bF)(s,{value:e.formState.noSummer.contractRegular,"onUpdate:value":a[7]||(a[7]=a=>e.formState.noSummer.contractRegular=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[Ga,(0,l.Lk)("td",Za,[(0,l.bF)(s,{value:e.formState.summer.contractNonSummer,"onUpdate:value":a[8]||(a[8]=a=>e.formState.summer.contractNonSummer=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",et,[(0,l.bF)(s,{value:e.formState.noSummer.contractNonSummer,"onUpdate:value":a[9]||(a[9]=a=>e.formState.noSummer.contractNonSummer=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[at,(0,l.Lk)("td",tt,[(0,l.bF)(s,{value:e.formState.summer.contractSaturdayHalfPeak,"onUpdate:value":a[10]||(a[10]=a=>e.formState.summer.contractSaturdayHalfPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",lt,[(0,l.bF)(s,{value:e.formState.noSummer.contractSaturdayHalfPeak,"onUpdate:value":a[11]||(a[11]=a=>e.formState.noSummer.contractSaturdayHalfPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[nt,(0,l.Lk)("td",rt,[(0,l.bF)(s,{value:e.formState.summer.contractOffPeak,"onUpdate:value":a[12]||(a[12]=a=>e.formState.summer.contractOffPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",ut,[(0,l.bF)(s,{value:e.formState.noSummer.contractOffPeak,"onUpdate:value":a[13]||(a[13]=a=>e.formState.noSummer.contractOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[st,ot,dt,it,(0,l.Lk)("td",mt,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayPeak(t)}," 刪除 ",8,ct)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[14]||(a[14]=a=>e.addWeekdayPeak())}," 新增時段 + ")]),kt,(0,l.Lk)("td",pt,[(0,l.bF)(s,{value:e.formState.summer.weekdaysPeak,"onUpdate:value":a[15]||(a[15]=a=>e.formState.summer.weekdaysPeak=a),type:"number"},null,8,["value"])]),ft]),(0,l.Lk)("tr",null,[yt,vt,(0,l.Lk)("td",gt,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayHalfPeak(t)}," 刪除 ",8,bt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[16]||(a[16]=a=>e.addWeekdayHalfPeak())}," 新增時段 + ")]),(0,l.Lk)("td",St,[(0,l.bF)(s,{value:e.formState.summer.weekdaysHalfPeak,"onUpdate:value":a[17]||(a[17]=a=>e.formState.summer.weekdaysHalfPeak=a),type:"number"},null,8,["value"])]),Pt]),(0,l.Lk)("tr",null,[Ft,(0,l.Lk)("td",xt,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayNoSummerHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayNoSummerHalfPeak(t)}," 刪除 ",8,Tt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[18]||(a[18]=a=>e.addWeekdayNoSummerHalfPeak())}," 新增時段 + ")]),ht,(0,l.Lk)("td",Ot,[(0,l.bF)(s,{value:e.formState.noSummer.weekdaysHalfPeak,"onUpdate:value":a[19]||(a[19]=a=>e.formState.noSummer.weekdaysHalfPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[Lt,wt,(0,l.Lk)("td",Ct,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayOffPeak(t)}," 刪除 ",8,Wt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[20]||(a[20]=a=>e.addWeekdayOffPeak())}," 新增時段 + ")]),(0,l.Lk)("td",Ut,[(0,l.bF)(s,{value:e.formState.summer.weekdaysOffPeak,"onUpdate:value":a[21]||(a[21]=a=>e.formState.summer.weekdaysOffPeak=a),type:"number"},null,8,["value"])]),Dt]),(0,l.Lk)("tr",null,[_t,(0,l.Lk)("td",Ht,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.weekdayNoSummerOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delWeekdayNoSummerOffPeak(t)}," 刪除 ",8,Mt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[22]||(a[22]=a=>e.addWeekdayNoSummerOffPeak())}," 新增時段 + ")]),Kt,(0,l.Lk)("td",Et,[(0,l.bF)(s,{value:e.formState.noSummer.weekdaysOffPeak,"onUpdate:value":a[23]||(a[23]=a=>e.formState.noSummer.weekdaysOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[At,Rt,Nt,(0,l.Lk)("td",It,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayHalfPeak(t)}," 刪除 ",8,Xt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[24]||(a[24]=a=>e.addSaturdayHalfPeak())}," 新增時段 + ")]),(0,l.Lk)("td",Yt,[(0,l.bF)(s,{value:e.formState.summer.saturdayHalfPeak,"onUpdate:value":a[25]||(a[25]=a=>e.formState.summer.saturdayHalfPeak=a),type:"number"},null,8,["value"])]),jt]),(0,l.Lk)("tr",null,[Bt,(0,l.Lk)("td",qt,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayNoSummerHalfPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayNoSummerHalfPeak(t)}," 刪除 ",8,Vt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[26]||(a[26]=a=>e.addSaturdayNoSummerHalfPeak())}," 新增時段 + ")]),Qt,(0,l.Lk)("td",$t,[(0,l.bF)(s,{value:e.formState.noSummer.saturdayHalfPeak,"onUpdate:value":a[27]||(a[27]=a=>e.formState.noSummer.saturdayHalfPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[Jt,zt,(0,l.Lk)("td",Gt,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayOffPeak(t)}," 刪除 ",8,Zt)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[28]||(a[28]=a=>e.addSaturdayOffPeak())}," 新增時段 + ")]),(0,l.Lk)("td",el,[(0,l.bF)(s,{value:e.formState.summer.saturdayOffPeak,"onUpdate:value":a[29]||(a[29]=a=>e.formState.summer.saturdayOffPeak=a),type:"number"},null,8,["value"])]),al]),(0,l.Lk)("tr",null,[tl,(0,l.Lk)("td",ll,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.saturdayNoSummerOffPeak,((a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,style:{"margin-top":"0.5rem"}},[(0,l.bF)(m,{value:a.StartTime,"onUpdate:value":e=>a.StartTime=e},null,8,["value","onUpdate:value"]),(0,l.eW)(" ~ "),(0,l.bF)(m,{value:a.EndTime,"onUpdate:value":e=>a.EndTime=e},null,8,["value","onUpdate:value"]),(0,l.Lk)("span",{style:{color:"red",cursor:"pointer"},onClick:a=>e.delSaturdayNoSummerOffPeak(t)}," 刪除 ",8,nl)])))),128)),(0,l.Lk)("div",{style:{cursor:"pointer",color:"#1890ff","margin-top":"0.5rem"},onClick:a[30]||(a[30]=a=>e.addSaturdayNoSummerOffPeak())}," 新增時段 + ")]),rl,(0,l.Lk)("td",ul,[(0,l.bF)(s,{value:e.formState.noSummer.saturdayOffPeak,"onUpdate:value":a[31]||(a[31]=a=>e.formState.noSummer.saturdayOffPeak=a),type:"number"},null,8,["value"])])]),(0,l.Lk)("tr",null,[sl,ol,dl,(0,l.Lk)("td",il,[(0,l.bF)(s,{value:e.formState.summer.sundayAndOffDayPeak,"onUpdate:value":a[32]||(a[32]=a=>e.formState.summer.sundayAndOffDayPeak=a),type:"number"},null,8,["value"])]),(0,l.Lk)("td",ml,[(0,l.bF)(s,{value:e.formState.noSummer.sundayAndOffDayPeak,"onUpdate:value":a[33]||(a[33]=a=>e.formState.noSummer.sundayAndOffDayPeak=a),type:"number"},null,8,["value"])])])])])])),_:1})}var kl=(0,l.pM)({props:{value:{type:Object,default:null}},setup(e,{emit:a}){const t=(0,u.Kh)(e.value),n=(0,u.KR)(t.summerMonth.startDate.split("-")[0]),r=(0,u.KR)(t.summerMonth.startDate.split("-")[1]);(0,l.wB)((()=>[n,r]),(()=>{t.summerMonth.startDate=`${n.value}-${r.value}`}),{deep:!0});const s=(0,u.KR)(t.summerMonth.endDate.split("-")[0]),o=(0,u.KR)(t.summerMonth.endDate.split("-")[1]);(0,l.wB)((()=>[s.value,o.value]),(()=>{t.summerMonth.endDate=`${s.value}-${o.value}`}),{deep:!0});const d=(0,u.KR)(t.weekDay.filter((e=>"Peak"===e.PeakType))),i=()=>{d.value.push({PeakType:"Peak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},m=e=>{d.value.splice(e,1)},c=(0,u.KR)(t.weekDay.filter((e=>"HalfPeak"===e.PeakType&&"Summer"===e.UseType))),k=()=>{c.value.push({PeakType:"HalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},p=e=>{c.value.splice(e,1)},f=(0,u.KR)(t.weekDay.filter((e=>"HalfPeak"===e.PeakType&&"NonSummer"===e.UseType))),y=()=>{f.value.push({PeakType:"HalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},v=e=>{f.value.splice(e,1)},g=(0,u.KR)(t.weekDay.filter((e=>"OffPeak"===e.PeakType&&"Summer"===e.UseType))),b=()=>{g.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},S=e=>{g.value.splice(e,1)},P=(0,u.KR)(t.weekDay.filter((e=>"OffPeak"===e.PeakType&&"NonSummer"===e.UseType))),F=()=>{P.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},x=e=>{P.value.splice(e,1)};(0,l.wB)((()=>[P,f,g,c,d]),(()=>{t.weekDay=[...P.value,...f.value,...g.value,...c.value,...d.value]}),{deep:!0});const T=(0,u.KR)(t.saturday.filter((e=>"SaturdayHalfPeak"===e.PeakType&&"Summer"===e.UseType))),h=()=>{T.value.push({PeakType:"SaturdayHalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},O=e=>{T.value.splice(e,1)},L=(0,u.KR)(t.saturday.filter((e=>"HalfPeak"===e.PeakType&&"NonSummer"===e.UseType))),w=()=>{L.value.push({PeakType:"HalfPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},C=e=>{L.value.splice(e,1)},W=(0,u.KR)(t.saturday.filter((e=>"OffPeak"===e.PeakType&&"Summer"===e.UseType))),D=()=>{W.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"Summer"})},_=e=>{W.value.splice(e,1)},H=(0,u.KR)(t.saturday.filter((e=>"OffPeak"===e.PeakType&&"NonSummer"===e.UseType))),M=()=>{H.value.push({PeakType:"OffPeak",StartTime:U()().startOf("day"),EndTime:U()().startOf("day"),UseType:"NonSummer"})},K=e=>{H.value.splice(e,1)};return(0,l.wB)((()=>[H,L,W,T]),(()=>{t.saturday=[...H.value,...L.value,...W.value,...T.value]}),{deep:!0}),(0,l.wB)((()=>t),(e=>{a("update:value",e)}),{deep:!0}),{addSaturdayHalfPeak:h,addSaturdayNoSummerHalfPeak:w,addSaturdayNoSummerOffPeak:M,addSaturdayOffPeak:D,addWeekdayHalfPeak:k,addWeekdayNoSummerHalfPeak:y,addWeekdayNoSummerOffPeak:F,addWeekdayOffPeak:b,addWeekdayPeak:i,delSaturdayHalfPeak:O,delSaturdayNoSummerHalfPeak:C,delSaturdayNoSummerOffPeak:K,delSaturdayOffPeak:_,delWeekdayHalfPeak:p,delWeekdayNoSummerHalfPeak:v,delWeekdayNoSummerOffPeak:x,delWeekdayOffPeak:S,delWeekdayPeak:m,endSummerDate:o,endSummerMonth:s,formState:t,saturdayHalfPeak:T,saturdayNoSummerHalfPeak:L,saturdayNoSummerOffPeak:H,saturdayOffPeak:W,startSummerDate:r,startSummerMonth:n,weekdayHalfPeak:c,weekdayNoSummerHalfPeak:f,weekdayNoSummerOffPeak:P,weekdayOffPeak:g,weekdayPeak:d}}});const pl=(0,S.A)(kl,[["render",cl]]);var fl=pl,yl=(0,l.pM)({components:{DataTables:y.A,HighTwo:Ka,HighThree:fl},setup(){const e=(0,l.EW)((()=>n.bill.loading)),{permission:a}=(0,v.J)(),{dispatch:t,state:n}=(0,f.Pj)(),r=(0,u.KR)([{value:"1",label:"高壓-二段式",supply:"HighVoltage",segment:2},{value:"2",label:"高壓-三段式",supply:"HighVoltage",segment:3},{value:"3",label:"特高壓-二段式",supply:"ExtremeHighVoltage",segment:2},{value:"4",label:"特高壓-三段式",supply:"ExtremeHighVoltage",segment:3}]),s=(0,u.KR)(null),o={lg:8,md:9,xs:24},d={lg:16,md:15,xs:24},i={date:[{required:!0,message:"請選擇日期",trigger:"blur"}],contractRegular:[{required:!0,message:"請輸入金額",trigger:"blur"}],contractNonSummer:[{required:!0,message:"請輸入金額",trigger:"blur"}],contractSaturdayHalfPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],contractOffPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],weekdaysHalfPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],weekdaysPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],weekdaysOffPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],saturdayHalfPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],saturdayOffPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}],sundayAndOffDayPeak:[{required:!0,message:"請輸入金額",trigger:"blur"}]},m=(0,u.Kh)({}),p={mode:"create",title:"新增設定",year:2024,month:1,supply:null,segment:null,weekDay:[],saturday:[],offPeakDay:[{PeakType:"OffPeak",StartTime:"00:00:00",EndTime:"00:00:00",UseType:"Default"}],summerMonth:{startDate:"01-01",endDate:"12-31"},summer:{contractRegular:0,contractNonSummer:0,contractSaturdayHalfPeak:0,contractOffPeak:0,weekdaysPeak:0,weekdaysHalfPeak:0,weekdaysOffPeak:0,saturdayHalfPeak:0,saturdayOffPeak:0,sundayAndOffDayPeak:0},noSummer:{contractRegular:0,contractNonSummer:0,contractSaturdayHalfPeak:0,contractOffPeak:0,weekdaysPeak:0,weekdaysHalfPeak:0,weekdaysOffPeak:0,saturdayHalfPeak:0,saturdayOffPeak:0,sundayAndOffDayPeak:0}},y=[{title:"時間",dataIndex:"date",key:"date"},{title:"操作",dataIndex:"action",key:"action"}];(0,l.wB)((()=>s.value),(e=>{const a=r.value.find((a=>a.value===e));t("bill/fetchFeeList",a)}));const g=(0,l.EW)((()=>n.bill.feeListTableData.map((e=>({date:`${e.Year}-${e.Month}`,action:(0,l.bF)(de,null,{default:()=>[a.update&&(0,l.bF)("span",{onClick:()=>P(e)},[(0,l.bF)((0,l.g2)("unicon"),{name:"edit"},null)]),a.delete&&(0,l.bF)("span",{onClick:()=>x(e)},[(0,l.bF)((0,l.g2)("unicon"),{name:"trash"},null)])]})}))))),b=(0,u.KR)(!1),S=()=>{Object.assign(m,p);const{supply:e,segment:a}=r.value.find((e=>e.value===s.value));m.segment=a,m.supply=e,b.value=!0},P=async({Year:e,Month:a})=>{const{supply:l,segment:n}=r.value.find((e=>e.value===s.value)),u=await t("bill/fetchFeeDetail",{year:e,month:a,supply:l,segment:n}),{WeekDays:o,Saturday:d,OffPeakDay:i}=u.PeakTimeRanges,{StartDate:c,EndDate:k}=u.SummerMonth||{startDate:"01-01",endDate:"12-31"},{RateId:p,DemandContractRegular:f,DemandContractNonSummer:y,DemandContractSaturdayHalfPeak:v,DemandContractOffPeak:g,MobileRateWeekdaysPeak:S,MobileRateWeekdaysHalfPeak:P,MobileRateWeekdaysOffPeak:F,MobileRateSaturdayHalfPeak:x,MobileRateSaturdayOffPeak:T,MobileRateSundayAndOffDayPeak:h}=u.DetailItems.find((e=>e.IsSummer)),{RateId:O,DemandContractRegular:L,DemandContractNonSummer:w,DemandContractSaturdayHalfPeak:C,DemandContractOffPeak:W,MobileRateWeekdaysPeak:D,MobileRateWeekdaysHalfPeak:_,MobileRateWeekdaysOffPeak:H,MobileRateSaturdayHalfPeak:M,MobileRateSaturdayOffPeak:K,MobileRateSundayAndOffDayPeak:E}=u.DetailItems.find((e=>!e.IsSummer)),A={mode:"edit",title:`編輯${e}-${a}`,year:e,month:a,weekDay:o?o.map((e=>({...e,StartTime:U()(e.StartTime,"HH:mm:ss"),EndTime:U()(e.EndTime,"HH:mm:ss")}))):[],saturday:d?d.map((e=>({...e,StartTime:U()(e.StartTime,"HH:mm:ss"),EndTime:U()(e.EndTime,"HH:mm:ss")}))):[],offPeakDay:i,summerMonth:{startDate:c,endDate:k},supply:l,segment:n,summer:{rateId:p,contractRegular:f,contractNonSummer:y,contractSaturdayHalfPeak:v,contractOffPeak:g,weekdaysPeak:S,weekdaysHalfPeak:P,weekdaysOffPeak:F,saturdayHalfPeak:x,saturdayOffPeak:T,sundayAndOffDayPeak:h},noSummer:{rateId:O,contractRegular:L,contractNonSummer:w,contractSaturdayHalfPeak:C,contractOffPeak:W,weekdaysPeak:D,weekdaysHalfPeak:_,weekdaysOffPeak:H,saturdayHalfPeak:M,saturdayOffPeak:K,sundayAndOffDayPeak:E}};Object.assign(m,A),b.value=!0},F=()=>{b.value=!1},x=({Year:a,Month:l})=>{const{supply:n,segment:u}=r.value.find((e=>e.value===s.value));c.A.confirm({title:"確認刪除?",okText:"確認",cancelText:"取消",confirmLoading:e.value,onOk:async()=>{try{await t("bill/deleteFee",{year:a,month:l,supply:n,segment:u}),c.A.success({content:"刪除成功"})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}}})},T=async()=>{try{let e;"create"===m.mode?(await t("bill/addFee",(0,u.ux)(m)),e="新增成功"):(await t("bill/editFee",(0,u.ux)(m)),e="修改成功"),b.value=!1,k.A.success({message:e})}catch(e){c.A.error({title:"發生錯誤",content:e.message})}};return{loading:e,columns:y,fee:s,labelCol:o,wrapperCol:d,rules:i,formState:m,feeOptions:r,tableData:g,modal:b,openAddModal:S,closeModal:F,submit:T}}});const vl=(0,S.A)(yl,[["render",oe]]);var gl=vl,bl=(t(21902),t(18787)),Sl=t(6049),Pl=t(96763);const Fl=(0,l.Lk)("h3",null,"整合報表產生",-1);var xl={__name:"Index",setup(e){const a=(0,u.KR)(),t=(0,u.KR)(),n=(0,u.KR)(!1),r=(0,u.KR)(""),s=(0,u.KR)(""),o=()=>a.value?t.value?!a.value.isAfter(t.value)||(bl.Ay.warning("開始日期不能晚於結束日期"),!1):(bl.Ay.warning("請選擇結束日期"),!1):(bl.Ay.warning("請選擇開始日期"),!1),d=async()=>{if(r.value="",s.value="",o()){n.value=!0;try{const e=a.value.format("YYYY-MM-DD"),l=t.value.format("YYYY-MM-DD"),n=`http://localhost:5146/integrated-report/process/date-range?startDate=${e}&endDate=${l}`;Pl.log("API URL:",n),await Sl.u.post(n),r.value="整合報表產生成功！",bl.Ay.success("整合報表產生成功！")}catch(e){const a=e.response?.data?.message||e.message||"報表產生失敗，請稍後再試";s.value=a,bl.Ay.error(a)}finally{n.value=!1}}};return(e,u)=>{const o=(0,l.g2)("a-date-picker"),i=(0,l.g2)("a-form-item"),m=(0,l.g2)("a-button"),c=(0,l.g2)("a-form"),k=(0,l.g2)("a-divider"),p=(0,l.g2)("a-alert");return(0,l.uX)(),(0,l.CE)("div",null,[Fl,(0,l.bF)(c,{layout:"inline"},{default:(0,l.k6)((()=>[(0,l.bF)(i,{label:"開始日期"},{default:(0,l.k6)((()=>[(0,l.bF)(o,{value:a.value,"onUpdate:value":u[0]||(u[0]=e=>a.value=e),format:"YYYY-MM-DD",placeholder:"請選擇開始日期"},null,8,["value"])])),_:1}),(0,l.bF)(i,{label:"結束日期"},{default:(0,l.k6)((()=>[(0,l.bF)(o,{value:t.value,"onUpdate:value":u[1]||(u[1]=e=>t.value=e),format:"YYYY-MM-DD",placeholder:"請選擇結束日期"},null,8,["value"])])),_:1}),(0,l.bF)(i,null,{default:(0,l.k6)((()=>[(0,l.bF)(m,{type:"primary",loading:n.value,onClick:d},{default:(0,l.k6)((()=>[(0,l.eW)(" 執行整合報表 ")])),_:1},8,["loading"])])),_:1})])),_:1}),(0,l.bF)(k),r.value?((0,l.uX)(),(0,l.Wv)(p,{key:0,message:r.value,type:"success","show-icon":"",closable:"",onClose:u[2]||(u[2]=e=>r.value="")},null,8,["message"])):(0,l.Q3)("",!0),s.value?((0,l.uX)(),(0,l.Wv)(p,{key:1,message:s.value,type:"error","show-icon":"",closable:"",onClose:u[3]||(u[3]=e=>s.value="")},null,8,["message"])):(0,l.Q3)("",!0)])}}};const Tl=xl;var hl=Tl,Ol=(0,l.pM)({components:{Main:r.gZ,FormWrap:o,BillWrap:d,Setting:F,Calculate:j,Schedule:J,BillList:ee,Meter:se,Fee:gl,Output:hl},setup(){const{dispatch:e}=(0,f.Pj)();(0,l.sV)((async()=>{await Promise.all([e("bill/getSettingList"),e("bill/getMeterList")])}));const a=(0,u.KR)("1");return{activeTab:a}}});const Ll=(0,S.A)(Ol,[["render",n]]);var wl=Ll}}]);