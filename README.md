# ⚠️ 專案啟動環境說明

> **重要：每一次啟動、開發、部署本專案前，所有人員都必須優先閱讀本說明，並嚴格遵守下列 Node.js 與 npm 版本條件，否則請勿進行任何操作。**

本專案必須使用下列 Node.js 與 npm 版本，否則可能無法正常啟動：

- Node.js：**18.16.1**
- npm：**9.5.1**

## 啟動步驟

```bash
nvm install 18.16.1
nvm alias default 18.16.1
nvm use 18.16.1
node -v   # ✅ v18.16.1
npm -v    # ✅ v9.5.1
npm install
npm run serve
```

> 請務必確認 `node -v` 與 `npm -v` 版本正確，否則請勿進行開發或部署。


GITHUB
https://github.com/ococomtw/plc-frontend



# 🎨 主題系統

本專案已整合 Pinia 主題管理系統，支援淺色和深色主題動態切換。

## 主題功能特色

- **雙主題支持**: 淺色主題和深色主題
- **動態切換**: 實時主題切換，無需重新加載頁面
- **本地存儲**: 主題偏好自動保存到 localStorage
- **系統偏好檢測**: 自動檢測用戶系統主題偏好
- **CSS 變數**: 基於 CSS 自定義屬性的主題系統

## 主題系統使用

### 在組件中使用

```javascript
import { useThemeStore } from '@/stores/theme'
import { computed } from 'vue'

export default {
  setup() {
    const themeStore = useThemeStore()

    const currentTheme = computed(() => themeStore.currentTheme)
    const isDarkMode = computed(() => themeStore.isDarkMode)

    const toggleTheme = () => {
      themeStore.toggleTheme()
    }

    return { currentTheme, isDarkMode, toggleTheme }
  }
}
```

### 在樣式中使用 CSS 變數

```css
.my-component {
  background: var(--background);
  color: var(--text-primary);
  border: 1px solid var(--border-normal);
}
```

### 主題測試

訪問 `/theme-test` 頁面可以測試主題系統功能。

## 技術實現

- **狀態管理**: Pinia 2.0.28
- **主題 Store**: `src/stores/theme.js`
- **主題切換組件**: `src/components/utilities/ThemeToggle.vue`
- **CSS 變數定義**: `src/static/css/style.css`

# UIUX 不可異動規則
- 任何 UI 或 UX 相關的元件、樣式、版面、互動設計，均不可異動、不可調整。
- 包含但不限於：色彩、字型、間距、按鈕樣式、版面配置、動畫、響應式設計、使用者流程等。
- 若有任何 UIUX 相關需求，需經專案負責人書面同意。
- **主題系統例外**: 主題切換功能和相關 CSS 變數可依需求調整。