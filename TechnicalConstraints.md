# 技術限制約束文檔

## 📋 概述

本文檔定義了 PLC Frontend 專案的技術限制和約束條件，確保專案的穩定性、安全性和可維護性。所有開發人員必須嚴格遵守這些限制。

## 🚫 嚴格禁止事項

### 1. 版本限制
#### Node.js 和 npm 版本
- **禁止使用**: 除 Node.js 18.16.1 和 npm 9.5.1 以外的任何版本
- **後果**: 可能導致依賴衝突、構建失敗、部署問題
- **檢查方式**: 每次開發前必須執行 `node -v` 和 `npm -v` 確認版本

#### 依賴版本鎖定
- **禁止升級**: 核心依賴版本（Vue.js 3.2.45, Ant Design Vue 3.2.15）
- **禁止降級**: 已安裝的依賴版本
- **例外情況**: 安全漏洞修復需要技術主管批准

### 2. UI/UX 限制
#### 設計系統限制
- **禁止修改**: 現有組件的視覺設計
- **禁止調整**: 色彩、字型、間距、按鈕樣式
- **禁止改變**: 版面配置、動畫效果、響應式斷點
- **禁止更改**: 使用者交互流程和操作邏輯

#### 主題系統例外
- **允許調整**: CSS 變數值（需要文檔記錄）
- **允許新增**: 新的主題變數（需要兩套主題都定義）
- **允許修改**: 主題切換邏輯和相關功能

### 3. 架構限制
#### 狀態管理限制
- **禁止移除**: 現有 Vuex store 模塊
- **禁止修改**: 現有 Vuex state 結構
- **禁止繞過**: 現有狀態管理流程
- **新功能要求**: 使用 Pinia 而非 Vuex

#### 路由限制
- **禁止修改**: 現有路由路徑和參數
- **禁止移除**: 現有路由配置
- **禁止改變**: 路由守衛邏輯
- **新路由要求**: 遵循現有命名規範

## ⚠️ 技術約束

### 1. 依賴管理約束
#### 新依賴引入限制
```bash
# 禁止的安裝方式
npm install package-name
npm install package-name --save

# 必須使用的安裝方式
npm install package-name --legacy-peer-deps
```

#### 依賴類型限制
- **禁止引入**: jQuery 或其他 DOM 操作庫
- **禁止引入**: 與 Vue.js 衝突的框架（React, Angular）
- **禁止引入**: 未經安全審查的第三方庫
- **禁止引入**: 體積過大的庫（> 500KB）

#### 依賴版本約束
```json
{
  "vue": "3.2.45",           // 鎖定版本
  "ant-design-vue": "3.2.15", // 鎖定版本
  "pinia": "2.0.28",         // 鎖定版本
  "vuex": "^4.0.0"           // 現有版本範圍
}
```

### 2. 代碼結構約束
#### 文件組織限制
- **禁止創建**: 根目錄下的新文件夾
- **禁止修改**: 現有文件夾結構
- **禁止移動**: 現有文件位置
- **命名要求**: 遵循既定命名規範

#### 組件開發限制
```javascript
// 禁止的組件結構
export default {
  data() {
    return {
      // Options API 在新組件中禁用
    }
  }
}

// 必須使用的組件結構
export default {
  setup() {
    // Composition API 為新組件標準
  }
}
```

### 3. 樣式約束
#### CSS 使用限制
```css
/* 禁止硬編碼顏色 */
.component {
  color: #ffffff;           /* ❌ 禁止 */
  background: #000000;      /* ❌ 禁止 */
}

/* 必須使用 CSS 變數 */
.component {
  color: var(--text-primary);      /* ✅ 正確 */
  background: var(--background);   /* ✅ 正確 */
}
```

#### 樣式作用域限制
- **禁止使用**: 全局樣式（除非在 `style.css` 中）
- **必須使用**: `scoped` 樣式
- **禁止覆蓋**: Ant Design Vue 的核心樣式
- **禁止使用**: `!important`（除非絕對必要）

### 4. API 約束
#### HTTP 請求限制
- **禁止使用**: `fetch()` API
- **必須使用**: Axios 實例
- **禁止繞過**: 現有攔截器
- **禁止硬編碼**: API 端點 URL

#### 數據處理限制
```javascript
// 禁止直接修改 props
props.data.value = newValue;  // ❌ 禁止

// 必須使用響應式數據
const localData = ref(props.data);
localData.value = newValue;   // ✅ 正確
```

## 🔒 安全約束

### 1. 數據安全限制
#### 敏感信息處理
- **禁止硬編碼**: API 密鑰、密碼、令牌
- **禁止提交**: 包含敏感信息的文件
- **禁止使用**: 不安全的數據傳輸方式
- **必須驗證**: 所有用戶輸入

#### 本地存儲限制
```javascript
// 禁止存儲敏感信息
localStorage.setItem('password', password);     // ❌ 禁止
localStorage.setItem('apiKey', apiKey);         // ❌ 禁止

// 允許存儲的信息
localStorage.setItem('theme', 'dark');          // ✅ 允許
localStorage.setItem('language', 'zh-TW');      // ✅ 允許
```

### 2. 代碼安全限制
#### 動態代碼執行
- **禁止使用**: `eval()`
- **禁止使用**: `Function()` 構造函數
- **禁止使用**: `innerHTML`（使用 `textContent`）
- **禁止使用**: 動態 `import()` 未知模塊

#### XSS 防護要求
```javascript
// 禁止的做法
element.innerHTML = userInput;        // ❌ XSS 風險

// 正確的做法
element.textContent = userInput;      // ✅ 安全
```

## 📊 性能約束

### 1. 資源使用限制
#### 內存使用限制
- **組件數量**: 單頁面不超過 50 個組件實例
- **數據量**: 單次 API 響應不超過 10MB
- **圖片大小**: 單張圖片不超過 2MB
- **總資源**: 頁面總資源不超過 20MB

#### 網絡請求限制
- **並發請求**: 同時不超過 6 個 HTTP 請求
- **請求頻率**: 同一 API 間隔不少於 100ms
- **超時設置**: 所有請求必須設置超時（最大 30 秒）
- **重試機制**: 失敗請求最多重試 3 次

### 2. 渲染性能限制
#### 組件渲染約束
```javascript
// 禁止在模板中使用複雜計算
<template>
  <div>{{ expensiveCalculation() }}</div>  <!-- ❌ 禁止 -->
</template>

// 必須使用計算屬性
<template>
  <div>{{ computedValue }}</div>           <!-- ✅ 正確 -->
</template>

<script>
const computedValue = computed(() => {
  return expensiveCalculation();
});
</script>
```

#### 列表渲染限制
- **最大項目數**: v-for 渲染不超過 1000 項
- **必須使用**: `key` 屬性
- **推薦使用**: 虛擬滾動（超過 100 項）
- **禁止使用**: 嵌套 v-for（超過 2 層）

## 🧪 測試約束

### 1. 測試覆蓋率要求
#### 最低覆蓋率標準
- **整體覆蓋率**: 不低於 70%
- **關鍵功能**: 不低於 90%
- **主題系統**: 必須 100% 覆蓋
- **API 調用**: 必須包含錯誤處理測試

#### 測試類型要求
```javascript
// 必須包含的測試類型
describe('ComponentName', () => {
  it('should render correctly', () => {});           // 渲染測試
  it('should handle user interactions', () => {});   // 交互測試
  it('should support theme switching', () => {});    // 主題測試
  it('should handle error states', () => {});        // 錯誤處理測試
});
```

### 2. 測試環境限制
- **禁止跳過**: 失敗的測試
- **禁止提交**: 測試覆蓋率下降的代碼
- **必須模擬**: 外部 API 調用
- **必須清理**: 測試後的副作用

## 🚀 構建和部署約束

### 1. 構建限制
#### 構建配置約束
- **禁止修改**: Webpack 核心配置
- **禁止禁用**: 代碼檢查和格式化
- **必須通過**: 所有 ESLint 規則
- **必須優化**: 生產構建資源

#### 構建產物限制
```bash
# 構建產物大小限制
dist/js/chunk-vendors.*.js    # < 2MB
dist/js/app.*.js             # < 1MB
dist/css/app.*.css           # < 500KB
```

### 2. 部署限制
#### 環境變數約束
- **禁止硬編碼**: 環境相關配置
- **必須使用**: `.env` 文件
- **禁止提交**: 生產環境配置
- **必須驗證**: 部署前環境變數

#### 部署檢查清單
```bash
# 部署前必須執行的檢查
npm run lint          # 代碼檢查
npm run test          # 測試執行
npm run build         # 構建成功
npm audit             # 安全審計
```

## 📝 文檔約束

### 1. 代碼文檔要求
#### 註釋要求
- **複雜邏輯**: 必須添加中文註釋
- **公共方法**: 必須使用 JSDoc 格式
- **組件 Props**: 必須包含類型和說明
- **API 調用**: 必須說明用途和參數

#### 文檔更新要求
- **新功能**: 必須更新 README.md
- **API 變更**: 必須更新 API 文檔
- **配置變更**: 必須更新配置說明
- **依賴變更**: 必須更新依賴列表

### 2. 變更記錄要求
```markdown
# 變更記錄格式要求
## [版本號] - 日期
### 新增
- 功能描述

### 修改
- 變更描述

### 修復
- 問題描述

### 移除
- 移除內容描述
```

## ⚡ 違規處理

### 1. 違規等級
#### 嚴重違規（立即停止開發）
- 使用錯誤的 Node.js/npm 版本
- 修改核心 UI/UX 設計
- 引入未批准的依賴
- 提交敏感信息

#### 一般違規（需要修正）
- 代碼風格不符合規範
- 測試覆蓋率不足
- 文檔更新不及時
- 性能指標不達標

### 2. 檢查機制
#### 自動檢查
- **Git hooks**: 提交前自動檢查
- **CI/CD**: 構建時自動驗證
- **代碼審查**: Pull Request 必須審查
- **定期審計**: 每週依賴安全檢查

#### 手動檢查
- **代碼審查**: 同事互相檢查
- **功能測試**: 手動測試關鍵功能
- **性能測試**: 定期性能基準測試
- **安全審查**: 月度安全檢查

## 📞 支持和諮詢

### 技術支持聯繫方式
- **緊急技術問題**: 技術主管
- **架構決策**: 系統架構師
- **規範解釋**: 開發團隊負責人
- **工具使用**: 資深開發工程師

### 例外申請流程
1. **提交申請**: 說明違規原因和必要性
2. **技術評估**: 評估風險和影響
3. **團隊討論**: 團隊會議討論決定
4. **文檔記錄**: 記錄例外情況和原因
5. **定期審查**: 定期檢查例外是否仍然必要

---

**重要提醒**: 這些技術限制是為了確保專案的穩定性和可維護性。違反限制可能導致嚴重的技術債務和系統風險。如有疑問，請及時諮詢技術團隊。
