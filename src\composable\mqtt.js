import * as mqtt from "mqtt";
import { ref } from "vue";

export function useMqtt() {
  const client = ref(null);
  const url = process.env.mqttURL;
  const connect = () => {
    client.value = mqtt.connect(url);

    return new Promise((resolve, reject) => {
      client.value.on("connect", () => {
        resolve();
      });

      client.value.on("error", (err) => {
        reject(err);
      });
    });
  };

  const subscribe = (topic, callback) => {
    if (client.value) {
      client.value.subscribe(topic);
      client.value.on("message", (receivedTopic, message) => {
        if (receivedTopic === topic) {
          // subscribe
          callback(message.toString());
        }
      });
    }
  };

  const unSubscribe = (topic) => {
    if (client.value) {
      client.value.unsubscribe(topic, (error) => {
        if (error) {
          console.log("Unsubscribe error", error);
        }
      });
    }
  };

  const disconnect = () => {
    if (client.value) {
      client.value.end();
    }
  };

  return {
    client,
    connect,
    subscribe,
    unSubscribe,
    disconnect,
  };
}
