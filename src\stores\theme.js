import { defineStore } from 'pinia'
import themes from '@/config/theme/themeConfigure'

const { lightMode, blackMode } = themes

export const useThemeStore = defineStore('theme', {
  state: () => ({
    currentTheme: 'light',
    themes: {
      light: {
        // 主要顏色
        primary: '#FF8000',
        primaryHover: '#FFC04D',
        secondary: '#66B2FF',
        secondaryHover: '#4CA6FF',
        
        // 背景顏色
        background: '#F8F9FB',
        cardBackground: '#FFFFFF',
        siderBackground: '#FFFFFF',
        headerBackground: '#FFFFFF',
        
        // 文字顏色
        textPrimary: '#404040',
        textSecondary: '#8C90A4',
        headingColor: 'rgba(0, 0, 0, 0.85)',
        
        // 邊框顏色
        borderLight: '#F1F2F6',
        borderNormal: '#E3E6EF',
        borderDeep: '#C6D0DC',
        
        // 狀態顏色
        success: '#01B81A',
        warning: '#FA8B0C',
        error: '#FF0F0F',
        info: '#00AAFF',
        
        // 其他主題變數
        ...lightMode
      },
      dark: {
        // 主要顏色
        primary: '#FF8000',
        primaryHover: '#FFC04D',
        secondary: '#66B2FF',
        secondaryHover: '#4CA6FF',
        
        // 背景顏色
        background: '#010413',
        cardBackground: '#1B1E2B',
        siderBackground: '#1B1E2B',
        headerBackground: '#1B1E2B',
        
        // 文字顏色
        textPrimary: '#E1E1E3',
        textSecondary: '#A4A5AA',
        headingColor: '#E1E1E3',
        
        // 邊框顏色
        borderLight: '#323541',
        borderNormal: '#494B55',
        borderDeep: '#070A19',
        
        // 狀態顏色
        success: '#01B81A',
        warning: '#FA8B0C',
        error: '#FF0F0F',
        info: '#00AAFF',
        
        // 其他主題變數
        ...blackMode
      }
    }
  }),

  getters: {
    getCurrentThemeColors: (state) => {
      return state.themes[state.currentTheme]
    },
    
    isDarkMode: (state) => {
      return state.currentTheme === 'dark'
    },
    
    isLightMode: (state) => {
      return state.currentTheme === 'light'
    }
  },

  actions: {
    setTheme(theme) {
      if (this.themes[theme]) {
        this.currentTheme = theme
        this.applyTheme()
        this.saveThemeToStorage()
      }
    },

    toggleTheme() {
      const newTheme = this.currentTheme === 'light' ? 'dark' : 'light'
      this.setTheme(newTheme)
    },

    applyTheme() {
      const theme = this.themes[this.currentTheme]
      const root = document.documentElement

      // 應用 CSS 變數
      Object.entries(theme).forEach(([key, value]) => {
        // 將 camelCase 轉換為 kebab-case
        const cssVarName = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        root.style.setProperty(`--${cssVarName}`, value)
      })

      // 設置 body 的 data-theme 屬性，方便 CSS 選擇器使用
      document.body.setAttribute('data-theme', this.currentTheme)
      
      // 設置 Ant Design 的主題類
      if (this.currentTheme === 'dark') {
        document.body.classList.add('dark-theme')
        document.body.classList.remove('light-theme')
      } else {
        document.body.classList.add('light-theme')
        document.body.classList.remove('dark-theme')
      }
    },

    saveThemeToStorage() {
      localStorage.setItem('theme', this.currentTheme)
    },

    loadThemeFromStorage() {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme && this.themes[savedTheme]) {
        this.currentTheme = savedTheme
      }
      this.applyTheme()
    },

    initTheme() {
      // 檢查系統偏好設定
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      
      // 優先使用儲存的主題，否則使用系統偏好
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme && this.themes[savedTheme]) {
        this.currentTheme = savedTheme
      } else {
        this.currentTheme = prefersDark ? 'dark' : 'light'
      }
      
      this.applyTheme()
      
      // 監聽系統主題變化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          this.setTheme(e.matches ? 'dark' : 'light')
        }
      })
    }
  }
})
