<template>
  <div>
    <!-- 非時間電價標題 -->
    <div class="title-header">
      <h2>非時間電價</h2>
      <div class="unit-label">單位：元</div>
    </div>

    <!-- 表格容器置中 -->
    <div style="display: flex; justify-content: center;">
      <table style="border-collapse: collapse; font-size: 16px; text-align: center; border: 2px solid #000;">
      <thead>
        <tr>
          <th colspan="4" style="border: 1px solid #000; padding: 8px; background-color: #90EE90; text-align: center;">分類</th>
          <th style="border: 1px solid #000; padding: 8px; background-color: #90EE90;">夏月</th>
          <th style="border: 1px solid #000; padding: 8px; background-color: #90EE90;">非夏月</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td rowspan="3" style="border: 1px solid #000; padding: 8px; background-color: #F0F8E8; vertical-align: middle; text-align: center;">基<br>本<br>電<br>費</td>
          <td colspan="3" style="border: 1px solid #000; padding: 8px; background-color: #F0F8E8;">裝置契約</td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a1" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a2" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
        </tr>
        <tr>
          <td rowspan="2" style="border: 1px solid #000; padding: 4px; background-color: #F0F8E8; font-size: 12px; vertical-align: middle; text-align: center;">需量契約</td>
          <td style="border: 1px solid #000; padding: 4px; background-color: #F0F8E8; font-size: 12px;">經常契約</td>
          <td rowspan="2" style="border: 1px solid #000; padding: 4px; background-color: #F0F8E8; font-size: 12px; vertical-align: middle; text-align: center;">每瓩每月</td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a3" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a4" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
        </tr>
        <tr>
          <td style="border: 1px solid #000; padding: 4px; background-color: #F0F8E8; font-size: 12px;">非夏月契約</td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a5" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.a6" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
        </tr>

        <tr>
          <td colspan="3" style="border: 1px solid #000; padding: 8px; background-color: #F0F8E8; text-align: center;">流動電費</td>
          <td style="border: 1px solid #000; padding: 8px; background-color: #F0F8E8;">每度</td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.b1" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
          <td style="border: 1px solid #000; padding: 8px;"><input v-model="form.b2" style="border: 1px solid #ccc; padding: 4px; text-align: center; width: 100%; box-sizing: border-box;" /></td>
        </tr>
      </tbody>
      </table>
    </div>

    <!-- 日期區間設定 -->
    <div style="display: flex; justify-content: center; margin-top: 20px;">
      <div style="border: 1px solid #ccc; padding: 15px; border-radius: 5px; background-color: #f9f9f9;">
        <div style="margin-bottom: 10px; font-weight: bold; text-align: center;">夏月期間設定</div>
        <div style="display: flex; align-items: center; gap: 10px;">
          <span>夏月：</span>
          <input
            v-model="dateRange.startMonth"
            type="number"
            min="1"
            max="12"
            style="border: 1px solid #ccc; padding: 4px; width: 50px; text-align: center;"
          />
          <span>月</span>
          <input
            v-model="dateRange.startDay"
            type="number"
            min="1"
            max="31"
            style="border: 1px solid #ccc; padding: 4px; width: 50px; text-align: center;"
          />
          <span>日 至 </span>
          <input
            v-model="dateRange.endMonth"
            type="number"
            min="1"
            max="12"
            style="border: 1px solid #ccc; padding: 4px; width: 50px; text-align: center;"
          />
          <span>月</span>
          <input
            v-model="dateRange.endDay"
            type="number"
            min="1"
            max="31"
            style="border: 1px solid #ccc; padding: 4px; width: 50px; text-align: center;"
          />
          <span>日</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

const form = reactive({
  a1: '', a2: '', a3: '', a4: '', a5: '', a6: '',
  b1: '', b2: '',
})

// 夏月日期區間設定
const dateRange = reactive({
  startMonth: 6,
  startDay: 1,
  endMonth: 9,
  endDay: 30
})
</script>

<style scoped>
/* 標題區域 */
.title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-header h2 {
  color: #52c41a;
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  text-align: center;
  flex: 1;
}

.unit-label {
  color: #666;
  font-size: 14px;
  font-weight: normal;
}
</style>
