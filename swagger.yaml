openapi: 3.0.3
info:
  title: PLC Frontend API Documentation
  description: |
    PLC Frontend 工業控制系統前端 API 文檔
    
    ## 功能概述
    - 設備監控和管理
    - 實時數據獲取
    - 報警系統
    - 用戶認證和授權
    - 主題管理
    
    ## 認證方式
    使用 Bearer Token 進行 API 認證
    
    ## 響應格式
    所有 API 響應都遵循統一格式：
    ```json
    {
      "success": true,
      "data": {},
      "message": "操作成功",
      "code": 200
    }
    ```
  version: 1.0.0
  contact:
    name: PLC Frontend Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api
    description: 開發環境
  - url: https://api.plc-frontend.com
    description: 生產環境

security:
  - bearerAuth: []

paths:
  # 認證相關 API
  /auth/login:
    post:
      tags:
        - 認證管理
      summary: 用戶登入
      description: 用戶使用帳號密碼登入系統
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: 用戶名
                  example: admin
                password:
                  type: string
                  description: 密碼
                  example: password123
      responses:
        '200':
          description: 登入成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          token:
                            type: string
                            description: JWT Token
                          user:
                            $ref: '#/components/schemas/User'
        '401':
          description: 認證失敗
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - 認證管理
      summary: 用戶登出
      description: 用戶登出系統
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 用戶管理 API
  /users:
    get:
      tags:
        - 用戶管理
      summary: 獲取用戶列表
      description: 獲取系統中所有用戶的列表
      parameters:
        - name: page
          in: query
          description: 頁碼
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: 每頁數量
          schema:
            type: integer
            default: 10
        - name: search
          in: query
          description: 搜索關鍵字
          schema:
            type: string
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          users:
                            type: array
                            items:
                              $ref: '#/components/schemas/User'
                          total:
                            type: integer
                          page:
                            type: integer
                          limit:
                            type: integer

  # 設備管理 API
  /devices:
    get:
      tags:
        - 設備管理
      summary: 獲取設備列表
      description: 獲取所有 PLC 設備的列表
      parameters:
        - name: status
          in: query
          description: 設備狀態篩選
          schema:
            type: string
            enum: [online, offline, error]
        - name: type
          in: query
          description: 設備類型篩選
          schema:
            type: string
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Device'

    post:
      tags:
        - 設備管理
      summary: 新增設備
      description: 新增一個 PLC 設備
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceInput'
      responses:
        '201':
          description: 新增成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Device'

  /devices/{deviceId}:
    get:
      tags:
        - 設備管理
      summary: 獲取設備詳情
      description: 根據設備 ID 獲取設備詳細信息
      parameters:
        - name: deviceId
          in: path
          required: true
          description: 設備 ID
          schema:
            type: string
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Device'
        '404':
          description: 設備不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # 實時數據 API
  /realtime/data:
    get:
      tags:
        - 實時數據
      summary: 獲取實時數據
      description: 獲取所有設備的實時數據
      parameters:
        - name: deviceIds
          in: query
          description: 設備 ID 列表（逗號分隔）
          schema:
            type: string
            example: "device1,device2,device3"
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/RealtimeData'

  # 報警系統 API
  /alarms:
    get:
      tags:
        - 報警系統
      summary: 獲取報警列表
      description: 獲取系統報警信息
      parameters:
        - name: level
          in: query
          description: 報警等級
          schema:
            type: string
            enum: [critical, warning, info]
        - name: status
          in: query
          description: 報警狀態
          schema:
            type: string
            enum: [active, acknowledged, resolved]
        - name: startTime
          in: query
          description: 開始時間
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 結束時間
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Alarm'

  /alarms/{alarmId}/acknowledge:
    post:
      tags:
        - 報警系統
      summary: 確認報警
      description: 確認指定的報警
      parameters:
        - name: alarmId
          in: path
          required: true
          description: 報警 ID
          schema:
            type: string
      responses:
        '200':
          description: 確認成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 主題管理 API
  /theme/preferences:
    get:
      tags:
        - 主題管理
      summary: 獲取用戶主題偏好
      description: 獲取當前用戶的主題偏好設置
      responses:
        '200':
          description: 獲取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ThemePreference'

    put:
      tags:
        - 主題管理
      summary: 更新主題偏好
      description: 更新用戶的主題偏好設置
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThemePreference'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 操作是否成功
        message:
          type: string
          description: 響應消息
        code:
          type: integer
          description: 響應代碼
        timestamp:
          type: string
          format: date-time
          description: 響應時間

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            success:
              type: boolean
              example: false
            error:
              type: object
              properties:
                type:
                  type: string
                  description: 錯誤類型
                details:
                  type: string
                  description: 錯誤詳情

    User:
      type: object
      properties:
        id:
          type: string
          description: 用戶 ID
        username:
          type: string
          description: 用戶名
        email:
          type: string
          format: email
          description: 電子郵件
        role:
          type: string
          enum: [admin, operator, viewer]
          description: 用戶角色
        status:
          type: string
          enum: [active, inactive]
          description: 用戶狀態
        createdAt:
          type: string
          format: date-time
          description: 創建時間
        lastLoginAt:
          type: string
          format: date-time
          description: 最後登入時間

    Device:
      type: object
      properties:
        id:
          type: string
          description: 設備 ID
        name:
          type: string
          description: 設備名稱
        type:
          type: string
          description: 設備類型
        model:
          type: string
          description: 設備型號
        status:
          type: string
          enum: [online, offline, error]
          description: 設備狀態
        ipAddress:
          type: string
          description: IP 地址
        port:
          type: integer
          description: 端口號
        location:
          type: string
          description: 設備位置
        createdAt:
          type: string
          format: date-time
          description: 創建時間
        updatedAt:
          type: string
          format: date-time
          description: 更新時間

    DeviceInput:
      type: object
      required:
        - name
        - type
        - ipAddress
        - port
      properties:
        name:
          type: string
          description: 設備名稱
        type:
          type: string
          description: 設備類型
        model:
          type: string
          description: 設備型號
        ipAddress:
          type: string
          description: IP 地址
        port:
          type: integer
          description: 端口號
        location:
          type: string
          description: 設備位置

    RealtimeData:
      type: object
      properties:
        deviceId:
          type: string
          description: 設備 ID
        timestamp:
          type: string
          format: date-time
          description: 數據時間戳
        values:
          type: object
          additionalProperties:
            oneOf:
              - type: number
              - type: string
              - type: boolean
          description: 實時數據值

    Alarm:
      type: object
      properties:
        id:
          type: string
          description: 報警 ID
        deviceId:
          type: string
          description: 設備 ID
        level:
          type: string
          enum: [critical, warning, info]
          description: 報警等級
        status:
          type: string
          enum: [active, acknowledged, resolved]
          description: 報警狀態
        message:
          type: string
          description: 報警消息
        description:
          type: string
          description: 報警描述
        createdAt:
          type: string
          format: date-time
          description: 報警時間
        acknowledgedAt:
          type: string
          format: date-time
          description: 確認時間
        acknowledgedBy:
          type: string
          description: 確認人員

    ThemePreference:
      type: object
      properties:
        theme:
          type: string
          enum: [light, dark, auto]
          description: 主題模式
        primaryColor:
          type: string
          description: 主要顏色
          example: "#FF8000"
        autoSwitch:
          type: boolean
          description: 是否自動切換主題
        switchTime:
          type: object
          properties:
            lightModeStart:
              type: string
              description: 淺色模式開始時間
              example: "06:00"
            darkModeStart:
              type: string
              description: 深色模式開始時間
              example: "18:00"

tags:
  - name: 認證管理
    description: 用戶認證和授權相關 API
  - name: 用戶管理
    description: 用戶信息管理 API
  - name: 設備管理
    description: PLC 設備管理 API
  - name: 實時數據
    description: 實時數據獲取 API
  - name: 報警系統
    description: 報警管理 API
  - name: 主題管理
    description: 主題偏好管理 API
