export const allEnableProps = [];

export const reportType = [
  {
    label: "年報表",
    value: "yearly",
  },
  {
    label: "月報表",
    value: "monthly",
  },
  {
    label: "日報表",
    value: "daily",
  },
  {
    label: "明細",
    value: "detail",
  },
];

export const statisticMethod = [
  {
    label: "總和",
    value: "Summation",
  },
  {
    label: "平均",
    value: "Average",
  },
  {
    label: "最大",
    value: "Maximum",
  },
  {
    label: "最小",
    value: "Minimum",
  },
  {
    label: "變化量",
    value: "Variation",
  },
  {
    label: "第一筆",
    value: "FirstValue",
  },
];

export const paramStatisticMethod = [
  {
    label: "總和",
    value: "Summation",
  },
  {
    label: "平均",
    value: "Average",
  },
  {
    label: "最大",
    value: "Maximum",
  },
  {
    label: "最小",
    value: "Minimum",
  },
];

export const electricContractTimePeriod = [
 {
    label: "非時間電價",
    value: 0,
  },
  {

    label: "二段式",
    value: 2,
  },
  {
    label: "三段式",
    value: 3,
  },
];

export const electricContractType = [
  {
    label: "裝置契約",
    value: "Device",
  },
  {
    label: "需量契約",
    value: "Demand",
  },
];

export const electricContractSupplyType = [
  {
    label: "低壓",
    value: "LowVoltage",
  },
  {
    label: "高壓",
    value: "HighVoltage",
  },
  {
    label: "特高壓",
    value: "ExtremeHighVoltage",
  },
];
