import { defineComponent, onMounted, reactive, toRaw, ref } from "vue";
import { getCCTVImage } from "@/composable/cctvConnection.js";
import { useStore } from "vuex";
export default defineComponent({
  props: {
    cctv: {
      type: Array,
      default: () => [],
    },
    dontShowOnAlarm: {
      type: Boolean,
      default: false,
    },
    alarm: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["changeAlarmSetting"],
  async setup(props, { emit }) {
    const { dispatch } = useStore();
    onMounted(() => {
      alarmShow.value = props.dontShowOnAlarm;
      if (props.cctv.length > 0) {
        dispatch("cctv/getCCTVImage", toRaw(props.cctv));
        getCCTVImage(CCTVCallback);
      }
    });

    const CCTVCallback = (cctvid, bytes) => {
      if (props.cctv.includes(cctvid)) {
        imagebase64[cctvid] = `data:image/RAW;base64,${bytes}`;
      }
    };

    const imagebase64 = reactive({});
    const onDontShowChanged = (e) => {
      emit("changeAlarmSetting", e.target.checked);
    };

    const alarmShow = ref(false);

    return { imagebase64, alarmShow, onDontShowChanged };
  },
});
