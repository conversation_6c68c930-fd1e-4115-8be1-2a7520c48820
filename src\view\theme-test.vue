<template>
  <div class="theme-test-page">
    <div class="theme-test-container">
      <h1>主題系統測試頁面</h1>
      
      <div class="test-section">
        <h2>當前主題狀態</h2>
        <div class="theme-info">
          <p><strong>當前主題:</strong> {{ currentTheme }}</p>
          <p><strong>是否為深色模式:</strong> {{ isDarkMode ? '是' : '否' }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>主題切換測試</h2>
        <div class="theme-controls">
          <a-button @click="setLightTheme" type="primary">
            設置淺色主題
          </a-button>
          <a-button @click="setDarkTheme" type="primary">
            設置深色主題
          </a-button>
          <a-button @click="toggleTheme" type="default">
            切換主題
          </a-button>
        </div>
      </div>

      <div class="test-section">
        <h2>顏色變數測試</h2>
        <div class="color-samples">
          <div class="color-sample primary">
            <span>主要顏色 (--primary)</span>
          </div>
          <div class="color-sample background">
            <span>背景顏色 (--background)</span>
          </div>
          <div class="color-sample card-background">
            <span>卡片背景 (--card-background)</span>
          </div>
          <div class="color-sample text-primary">
            <span>主要文字 (--text-primary)</span>
          </div>
          <div class="color-sample text-secondary">
            <span>次要文字 (--text-secondary)</span>
          </div>
          <div class="color-sample border">
            <span>邊框顏色 (--border-normal)</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>組件測試</h2>
        <div class="component-samples">
          <a-card title="測試卡片" style="width: 300px; margin-bottom: 16px;">
            <p>這是一個測試卡片，用來驗證主題顏色是否正確應用。</p>
            <a-button type="primary">主要按鈕</a-button>
            <a-button style="margin-left: 8px;">次要按鈕</a-button>
          </a-card>

          <a-input 
            placeholder="測試輸入框" 
            style="width: 300px; margin-bottom: 16px;"
          />

          <a-select 
            placeholder="測試選擇框" 
            style="width: 300px; margin-bottom: 16px;"
          >
            <a-select-option value="option1">選項 1</a-select-option>
            <a-select-option value="option2">選項 2</a-select-option>
          </a-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

export default {
  name: 'ThemeTest',
  setup() {
    const themeStore = useThemeStore()

    const currentTheme = computed(() => themeStore.currentTheme)
    const isDarkMode = computed(() => themeStore.isDarkMode)

    const setLightTheme = () => {
      themeStore.setTheme('light')
    }

    const setDarkTheme = () => {
      themeStore.setTheme('dark')
    }

    const toggleTheme = () => {
      themeStore.toggleTheme()
    }

    return {
      currentTheme,
      isDarkMode,
      setLightTheme,
      setDarkTheme,
      toggleTheme
    }
  }
}
</script>

<style scoped>
.theme-test-page {
  padding: 24px;
  background: var(--background);
  min-height: 100vh;
  color: var(--text-primary);
}

.theme-test-container {
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 32px;
  padding: 24px;
  background: var(--card-background);
  border-radius: 8px;
  border: 1px solid var(--border-normal);
}

.test-section h1 {
  color: var(--text-primary);
  margin-bottom: 24px;
}

.test-section h2 {
  color: var(--text-primary);
  margin-bottom: 16px;
}

.theme-info p {
  margin-bottom: 8px;
  color: var(--text-primary);
}

.theme-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.color-samples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.color-sample {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-normal);
  text-align: center;
}

.color-sample.primary {
  background: var(--primary);
  color: white;
}

.color-sample.background {
  background: var(--background);
  color: var(--text-primary);
  border: 2px solid var(--border-normal);
}

.color-sample.card-background {
  background: var(--card-background);
  color: var(--text-primary);
}

.color-sample.text-primary {
  background: var(--card-background);
  color: var(--text-primary);
}

.color-sample.text-secondary {
  background: var(--card-background);
  color: var(--text-secondary);
}

.color-sample.border {
  background: var(--card-background);
  color: var(--text-primary);
  border: 3px solid var(--border-normal);
}

.component-samples {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .theme-test-page {
    padding: 16px;
  }
  
  .test-section {
    padding: 16px;
  }
  
  .theme-controls {
    flex-direction: column;
  }
  
  .color-samples {
    grid-template-columns: 1fr;
  }
}
</style>
