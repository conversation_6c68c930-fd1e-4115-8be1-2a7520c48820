# `plc-frontend` 專案架構分析報告

本文件旨在對 `plc-frontend` 專案的技術架構、狀態管理、程式碼組織及潛在風險進行全面且細緻的分析，並提供相應的建議。

## 1. 總體架構圖

此圖描繪了從使用者互動到後端數據處理的完整流程。

```mermaid
graph TD
    subgraph "用戶端 (Client)"
        User[👤 使用者] --> Browser[🌐 瀏覽器]
    end

    subgraph "前端應用 (plc-frontend)"
        Browser -- HTTP/HTTPS --> WebServer[Nginx / 開發伺服器]
        WebServer -- 提供靜態資源 --> VueApp[Vue.js 應用]
        
        VueApp --> Router[Vue Router <br> (路由管理)]
        VueApp --> Components[元件層 <br> (Ant Design Vue, 自定義元件)]
        VueApp --> State[狀態管理層 <br> (Vuex)]
        
        Components -- 觸發事件 --> State
        State -- 更新狀態 --> Components
        
        State -- 調用 API --> DataService[DataService <br> (Axios 封裝)]
    end

    subgraph "後端服務 (Backend)"
        DataService -- RESTful API 請求 --> APIServer[API 伺服器]
        APIServer <--> Database[資料庫]
    end

    User -- 操作 --> Components
```

## 2. 技術棧 (Technology Stack)

專案採用的主要技術與函式庫如下：

- **核心框架:** **Vue.js** (推斷為 Vue 3，基於 `composable` 和編譯後模板語法)。
- **UI 框架:** **Ant Design Vue** (基於原始碼中大量出現的 `a-*` 系列元件)。
- **狀態管理:** **Vuex** (明確記載於開發文件中，用於集中管理應用狀態)。
- **路由管理:** **Vue Router** (基於單頁應用架構推斷，用於頁面導航)。
- **HTTP 客戶端:** **Axios** (推斷，並由 `DataService` 進行統一封裝和管理)。
- **打包/建構工具:** **Vue CLI / Webpack** (基於 `babel-loader` 快取檔案推斷)。
- **開發環境:**
  - **Node.js:** **18.16.1** (在 `README.md` 中嚴格鎖定)。
  - **npm:** **9.5.1** (在 `README.md` 中嚴格鎖定)。

## 3. 專案結構分析

專案的目錄結構清晰，遵循了現代前端框架的最佳實踐：

- `src/`: 應用程式原始碼根目錄。
  - `assets/`: 存放圖片、全域樣式表等靜態資源。
  - `components/`: 存放全域可複用的 UI 元件 (如 `DataTables`, `PermissionButton`, `ModalTable`)。
  - `config/dataService/`: 存放統一的 API 請求服務 (`DataService`)，封裝了 HTTP 客戶端，是與後端溝通的唯一出口。
  - `composable/`: 存放 Vue 3 Composition API 的可複用邏輯 (如 `usePermission`)。
  - `directives/`: 存放自定義指令 (如 `v-permission`)。
  - `mixins/`: 存放 Mixin 混入 (如 `permission.js`)，可能是從舊專案遷移或過渡時期的程式碼。
  - `router/`: 存放 Vue Router 的路由設定。
  - `view/`: 存放頁面級別的元件，並按功能模組進行組織 (如 `oco/user/list`, `oco/system/bill`)。
  - `vuex/modules/`: 存放 Vuex 的狀態模組，目前主要按功能劃分 (如 `user.js`)。
- `開發文件/`: 存放專案的核心開發文件與修改記錄，是了解專案歷史與決策的重要來源。
- `README.md`: 提供專案的啟動指南與重要的開發約束。

## 4. 核心功能模組分析

### 權限系統
這是應用的核心控制機制，深度整合到各個層面，確保了操作的安全性。
- **UI 層控制:**
  - `v-permission` 指令：直接在模板中根據使用者權限控制元素的顯示與隱藏，簡單高效。
  - `PermissionButton` 元件：將權限邏輯封裝在按鈕元件內，提高複用性。
- **邏輯層控制:**
  - `usePermission` Composable / `usePermissionMixin`: 在 `<script>` 區塊中提供 `permission.create` 等檢查方法，用於控制表單提交、API 呼叫等核心業務邏輯。
- **模組劃分:** 權限系統定義了清晰的業務模組邊界，涵蓋 `dashboard`, `gui`, `database`, `alarm`, `system`, `notify`, `tags`, `user`, `schedule` 等九大模組及其子模組。

### 數據流 (以新增使用者為例)
專案遵循了標準的單向數據流模式，確保了數據的可追蹤性和可預測性。
1.  **使用者操作:** 在 `user-list` 頁面點擊「新增」按鈕並提交表單。
2.  **元件互動:** 觸發 `submitForm` 方法。
3.  **調用 Action:** `submitForm` 內部 `dispatch('user/addUser', ...)`，將表單數據傳遞給 Vuex Action。
4.  **執行異步邏輯:** `addUser` Action 整理請求參數，調用 `DataService.post('/api/staff', ...)`。
5.  **API 請求:** `DataService` 發出 HTTP POST 請求到後端。
6.  **狀態更新:** API 請求成功後，`submitForm` 方法會再次 `dispatch('user/getUserList')` 來請求最新的使用者列表。
7.  **Action -> Mutation -> State:** `getUserList` Action 獲取數據後，`commit` 一個 Mutation，將新列表寫入 Vuex State。
8.  **畫面響應:** `user-list` 頁面因依賴 Vuex State，自動重新渲染，顯示包含新使用者的列表。

## 5. 狀態管理策略

- **工具:** 採用 **Vuex** 進行集中式狀態管理。
- **模式:**
  - **模組化:** 根據業務功能（目前主要看到 `user` 模組）將 Store 拆分為不同模組，便於維護。
  - **嚴格模式:** 遵循 Vuex 的單向數據流，只能通過 Mutation 修改 State，並由 Action 觸發 Mutation。
- **潛在問題:**
  - **模組粒度過粗:** `user` 模組同時處理「人員管理」和「權限角色管理」，隨著功能擴展，可能變得臃腫。建議可考慮拆分為 `staff` 和 `role` 兩個獨立模組。
  - **缺乏類型系統:** Store 中的 State 和 Payload 未使用 TypeScript 等類型系統，在大型專案中可能導致數據類型錯誤，增加調試難度。

## 6. 潛在問題與風險分析

### 技術債與程式碼品質
- **混合程式碼模式:** 專案同時存在 `mixins` 和 `composable`，顯示出從 Vue 2/Options API 到 Vue 3/Composition API 的過渡痕跡。這可能導致開發模式不統一，增加新進人員的學習成本。
  - **建議：** 制定計畫，逐步將現有 Mixin 重構為 Composable，統一開發範式。
- **浮點數精度問題:** 在電費計算 (`system-bill`) 的模板中，直接使用 `* 1.05` 和 `* 0.05.toFixed(2)` 進行金額計算。
  - `toFixed(2)` 返回的是**字串**，與數字相乘可能導致 `NaN` 或非預期的結果。
  - JavaScript 的浮點數運算存在精度問題，直接用於電費計算是**高風險**操作。
  - **建議：** 引入 `Decimal.js` 等高精度計算庫，或將金額轉換為最小單位（如：分）後再進行整數運算，以確保計算結果的準確性。
- **模板中存在重複計算:** 電費總結中，「營業稅」和「總電費」的計算邏輯重複了大量的加法。
  - **建議：** 將這些計算邏輯抽離到 `computed` 屬性中，提高性能和可維護性。
- **殘留的模擬數據 (Mock Data):** 根據開發文件，多個核心功能近期才從 Mock Data 切換為真實 API。`權限控制實施總結.md` 中明確指出大量頁面「待實施」。
  - **風險:** 專案中可能仍有未串接 API 的功能，是交付延遲和功能不完整的**重大風險**。需進行全面審計。

### 開發與維護
- **嚴格的環境鎖定:** `README.md` 強制使用 Node.js `18.16.1` 和 npm `9.5.1`。雖然能保證開發環境一致，但也使專案無法享受新版本的性能優化和安全更新，長期來看會增加維護成本。
- **僵化的 UI/UX 規則:** `README.md` 中「UIUX 不可異動」的規則過於嚴格。這會阻礙前端開發者進行必要的重構、優化使用者體驗或修復響應式佈局問題，降低開發靈活性和效率。
- **前後端職責不清:** 前端需要處理 `r/c/u/d` 到 `Read/Create/Update/Delete` 的轉換。這種數據格式的轉換應盡可能由後端或 API Gateway 處理，以降低前後端耦合度。

### 安全性
- **前端權限的局限性:** 目前的權限控制 (`v-permission` 等) 僅作用於 UI 層面（隱藏按鈕），無法阻止惡意使用者繞過前端直接調用 API。
  - **必須強調：** 所有權限驗證的最後防線必須在**後端 API**。後端需要對每個請求進行嚴格的權限校驗，前端控制僅為提升使用者體驗，不能作為安全保障。

### 功能完整性
- **權限控制未完成:** 文件顯示權限控制僅完成約 **40%**。這是一個嚴重的安全漏洞和功能缺陷，未受保護的頁面和操作可能被未授權的使用者訪問。這是當前專案的**最高優先級風險**，應立即規劃完成剩餘的權限控制實施。