{
  "//AccessTokenExpireMinute": "AccessToken的逾時時間(分)",
  "AccessTokenExpireMinute": 14400,

  "//RefreshtokenExpireMinute": "RefreshToken的逾時時間(分)",
  "RefreshtokenExpireMinute": 14400,

  "//CustomerIdNameInRequestUrlSegmentSn": "客戶ID名稱在Request Url 字節的第?段(從第0段開始) ",
  "CustomerIdNameInRequestUrlSegmentSn": 1,

  "//SerilogPath": "SeriLog 檔案路徑",
  "SerilogPath": "D:\\Product02\\logs\\WebApi\\",

  "RtuComportStartSn": 1,
  "RtuComportEndSn": 255,

  //"TemporaryTagFilePath": "D:\\Product02\\Task\\TemporaryTagFilePath\\",
  "TemporaryTagFilePath": "D:\\Product02\\TemporaryTagFilePath\\",

  "SaveRealTimeTagData": true,

  "IconWeb": "",
  "IconLocation": "..\\..\\Icons\\",

  "//TryDecigoCCConfig": "是否測試 DesigoCC 組態正確性",
  "TryDecigoCCConfig": true,

  "//SaveDecigoCCConfig": "是否將DesigoCC 組態存檔",
  "SaveDecigoCCConfig": true,

  "//DesigoCCProcessExpireTime": "處理 Desigo CC 逾時時間 (ms)",
  "DesigoCCProcessExpireTime": 30000,
  "ObixProcessExpireTime": 3000,

  "//每個Batch的 Obix 測點數量": "",
  "ObixElementsEachBatch": 1,

  "//LineNotifyApiUrl": "發送Line Notify 訊息Api 的URL",
  "LineNotifyApiUrl": "https://notify-api.line.me/api/notify",

  "//LineNotifyRegCallbackUrl": "註冊LINE Notify服務的回呼的網頁URL",
  "LineNotifyRegCallbackUrl": "http://localhost/LineAuth/callback.html",

  "//LineNotifyAuthApiUrl": "Line Notify 認證Api的URL",
  "LineNotifyAuthApiUrl": "https://notify-bot.line.me/oauth/authorize",

  "//LineNotifyAuthResponseMode": "Line Notify 認證的 response Mode",
  "LineNotifyAuthResponseMode": "form_get",

  "//LineNotifyCreateAccessTokenApiUrl": "Line Notify 取得AccessToken Api 的URL",
  "LineNotifyCreateAccessTokenApiUrl": "https://notify-bot.line.me/oauth/token",

  "//LineNotifyRevokeAccessTokenApiUrl": "Line Notify 取消 AccessToken Api 的URL",
  "LineNotifyRevokeAccessTokenApiUrl": "https://notify-api.line.me/api/revoke",


  "//ProcessAlarmFromDesigoCC": "要接收從Desigocc 送來的警報?",
  "ProcessAlarmFromDesigoCC": false,

  "//ProcessRealTimeDataValueFromDesigoCC": "是否處理 DesigoCC 送來的即時資料",
  "ProcessRealTimeDataValueFromDesigoCC": false,

  "//SendAlarmSummaryToFront": "是否傳送AlarmSUmmary 到前端",
  "SendAlarmSummaryToFront": false,

  "//InformMessagePattern": "通知訊息的Pattern; 代碼參考:Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern",
  "//InformMessagePattern-1": "桃園會展中心",
  "//InformMessagePattern-2": "利健~南港生技園區(尚未實作)",
  "InformMessagePattern": 1,

  "//CheckConnectionAlarmSummaryCodeList": "需要檢查連線代碼集合;代碼參考:Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionCategory",
  "//CheckConnectionAlarmSummaryCodeList-1": "1. Obix 資料來源端連線, 指的是 API 與 Data Provider  的連線",
  "//CheckConnectionAlarmSummaryCodeList-2": "2. Obix 資料處理端連線, 指的是 API 與 背景端(Worker)  的連線",
  "--CheckConnectionAlarmSummaryCodeList": "如果都不必檢查, 維持空集合即可",
  "CheckConnectionAlarmSummaryCodeList": [ 1, 2 ],

  "//TagImportAndExportConfig": "匯出/匯入測點相關設定",
  "TagImportAndExportConfig": {
    "//FileExtensionName": "檔案的副檔名",
    "FileExtensionName": ".csv",

    "//ImportFileDir": "匯入測點檔案上傳後存放位置(Task上五層, Main上兩層)",
    "ImportFileDir(Task上五層, Main上兩層)": "..\\..\\Icons\\TagImportAndExport\\ImportFile\\",
    "ImportFileDir": "..\\..\\..\\..\\..\\Icons\\TagImportAndExport\\ImportFile\\",

    "//ExportTagFileWebPath": "匯出檔案時的URL 路徑",
    "ExportTagFileWebPath": "TagImportAndExport/ImportFile/"
  }
}
