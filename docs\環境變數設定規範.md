# 環境變數設定規範

## 概述

本專案嚴格禁止在程式碼中使用硬編碼的 URL、IP 地址或端口號。所有的端點設定都必須透過環境變數來管理，以確保：

1. **環境一致性**：開發、測試、生產環境可以使用不同的設定
2. **安全性**：敏感資訊不會被提交到版本控制系統
3. **可維護性**：集中管理所有端點設定
4. **部署靈活性**：可以輕鬆切換不同的後端服務

## 環境變數列表

### 主要 API 端點
```bash
# 主要後端 API 服務
VUE_APP_API_ENDPOINT="http://*************:8345/"

# 前端開發服務器 (用於 CORS 設定)
VUE_APP_FRONTEND_URL="http://*************:8999/"

# 匯出 API 服務
VUE_APP_EXPORT_API_ENDPOINT="http://*************:5146/"

# 圖片和檔案服務
VUE_APP_IMAGE_URL="http://*************:7654/"
IMAGE_URL="http://*************:7654/"

# MQTT WebSocket 服務
VUE_APP_MQTT_URL='ws://***************:8083'
mqttUrl='ws://***************:8083'
```

### 第三方服務
```bash
# 個人資料圖片端點
VUE_APP_PROFILE_IMAGE_ENDPOINT="http://api.masudr.com"

# 子路由設定
VUE_APP_SUB_ROUTE="/"
```

## 使用規範

### ✅ 正確做法

1. **在 JavaScript/Vue 中使用環境變數**：
```javascript
// ✅ 正確
const client = axios.create({
  baseURL: process.env.VUE_APP_API_ENDPOINT,
});

// ✅ 正確
const connection = new signalR.HubConnectionBuilder()
  .withUrl(`${process.env.VUE_APP_API_ENDPOINT}/api/Cctv`)
  .build();
```

2. **在 Vue 配置檔案中使用環境變數**：
```javascript
// ✅ 正確 - customize-vue-config.js
proxy: {
  "/api": {
    target: process.env.VUE_APP_API_ENDPOINT,
    changeOrigin: true,
  },
  "/exportApi": {
    target: process.env.VUE_APP_EXPORT_API_ENDPOINT,
    changeOrigin: true,
  }
}
```

### ❌ 錯誤做法

```javascript
// ❌ 錯誤 - 硬編碼 URL
const client = axios.create({
  baseURL: "http://*************:8345/",
});

// ❌ 錯誤 - 硬編碼 IP 和端口
const connection = new signalR.HubConnectionBuilder()
  .withUrl("http://localhost:8629/api/Cctv")
  .build();

// ❌ 錯誤 - 使用 OR 運算子作為後備
target: process.env.VUE_APP_EXPORT_API_ENDPOINT || "http://*************:5146",
```

## 檔案說明

### 需要遵守環境變數規範的檔案

1. **前端程式碼**：
   - `src/config/dataService/dataService.js` - API 客戶端設定
   - `src/composable/*.js` - SignalR 連接設定
   - `src/vuex/modules/*/actionCreator.js` - API 調用
   - `customize-vue-config.js` - 開發服務器代理設定

2. **設定檔案**：
   - `.env` - 環境變數定義
   - `nginx.conf` - 生產環境代理設定（需要手動更新）

### 特殊情況處理

1. **nginx.conf**：
   - nginx 設定檔案無法直接使用環境變數
   - 需要在部署時手動更新或使用模板生成

2. **public/web.config**：
   - IIS 設定檔案，僅在 Windows IIS 部署時使用
   - 如果使用 nginx，可以忽略此檔案

## 部署注意事項

### 開發環境
```bash
# 使用本地開發設定
VUE_APP_API_ENDPOINT="http://localhost:8345/"
VUE_APP_FRONTEND_URL="http://localhost:8999/"
```

### 測試環境
```bash
# 使用測試服務器設定
VUE_APP_API_ENDPOINT="http://*************:8345/"
VUE_APP_FRONTEND_URL="http://*************:8999/"
```

### 生產環境
```bash
# 使用生產服務器設定
VUE_APP_API_ENDPOINT="https://api.production.com/"
VUE_APP_FRONTEND_URL="https://app.production.com/"
```

## 檢查清單

在提交程式碼前，請確認：

- [ ] 沒有硬編碼的 IP 地址或端口號
- [ ] 所有 API 端點都使用環境變數
- [ ] `.env` 檔案包含所有必要的環境變數
- [ ] 新增的端點已添加到此文檔中
- [ ] nginx.conf 中的設定與環境變數一致

## 常見問題

**Q: 為什麼不能使用 `||` 運算子提供預設值？**
A: 使用 `||` 運算子會掩蓋環境變數未設定的問題，應該在 `.env` 檔案中明確定義所有變數。

**Q: 如何在不同環境中使用不同的設定？**
A: 創建不同的 `.env` 檔案（如 `.env.development`、`.env.production`），或在部署時覆蓋環境變數。

**Q: nginx.conf 如何使用環境變數？**
A: nginx 本身不支援環境變數，需要在部署時使用腳本或模板引擎生成最終的設定檔案。
