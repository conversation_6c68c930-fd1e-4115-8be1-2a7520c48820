<template>
  <Div :darkMode="darkMode">
    <sdModal
      v-if="CCTVModal"
      :visible="CCTVModal"
      :onCancel="closeCCTVModal"
      :width="1000"
    >
      <cctvStream
        :cctv="currCCTV"
        :alarm="true"
        :dontShowOnAlarm="pause"
        @changeAlarmSetting="onPauseChanged"
      ></cctvStream>
    </sdModal>
    <Layout class="layout">
      <Header
        :style="{
          position: 'fixed',
          width: '100%',
          top: 0,
          [!rtl ? 'left' : 'right']: 0,
        }"
      >
        <div class="ninjadash-header-content d-flex">
          <div class="ninjadash-header-content__left">
            <div class="navbar-brand align-cener-v">
              <router-link
                :class="
                  topMenu && innerWidth > 991
                    ? 'ninjadash-logo top-menu'
                    : 'ninjadash-logo'
                "
                to="/"
              >
                <img
                  :src="
                    !darkMode
                      ? require(`@/static/img/oco_logo.png`)
                      : require(`@/static/img/oco_logo.png`)
                  "
                  alt="logo"
                />
              </router-link>
              <sdButton
                v-if="!topMenu || innerWidth <= 991"
                @click="toggleCollapsed"
                type="white"
              >
                <img
                  :src="require(`../static/img/icon/align-center-alt.svg`)"
                  alt="menu"
                />
              </sdButton>
            </div>
          </div>
          <div class="ninjadash-header-content__center">
            <div class="customer-name-display" v-if="customerName">
              <span class="customer-name-text">{{ customerName }}</span>
            </div>
          </div>
          <div class="ninjadash-header-content__right d-flex">
            <div class="ninjadash-nav-actions">
              <ThemeToggle />
              <Notification :alarms="importantAlarm" />
              <AuthInfo />
            </div>
          </div>
          <!-- <div class="ninjadash-header-content__fluid">
            <div class="ninjadash-header-content__fluid__action">
              <a
                class="btn-search"
                @click="handleSearchHide(searchHide)"
                href="#"
              >
                <Notification :alarms="alarms" />
              </a>
              <a class="btn-auth" @click="onShowHide(hide)" href="#">
                <unicon name="ellipsis-v"></unicon>
              </a>
            </div>
          </div> -->
        </div>
      </Header>
      <!-- <div class="header-more">
        <a-row>
          <a-col :md="0" :sm="24" :xs="24">
            <div class="small-screen-headerRight">
              <SmallScreenSearch :hide="searchHide" :darkMode="darkMode">
                <HeaderSearch />
              </SmallScreenSearch>
              <SmallScreenAuthInfo :hide="hide" :darkMode="darkMode">
                <AuthInfo :rtl="rtl" />
              </SmallScreenAuthInfo>
            </div>
          </a-col>
        </a-row>
      </div> -->
      <Layout>
        <template v-if="!topMenu || innerWidth <= 991">
          <Sider
            :width="280"
            :style="{
              margin: '65px 0 0 0',
              padding: `${!rtl ? '20px 20px 55px 0px' : '20px 0px 55px 20px'}`,
              overflowY: 'auto',
              height: '100vh',
              position: 'fixed',
              [!rtl ? 'left' : 'right']: 0,
              zIndex: 998,
            }"
            :collapsed="collapsed"
            :theme="!darkMode ? 'light' : 'dark'"
          >
            <perfect-scrollbar
              :options="{
                wheelSpeed: 1,
                swipeEasing: true,
                suppressScrollX: true,
              }"
            >
              <AsideItems
                :toggleCollapsed="toggleCollapsedMobile"
                :topMenu="topMenu"
                :rtl="rtl"
                :darkMode="darkMode"
                :events="onEventChange"
              />
            </perfect-scrollbar>
          </Sider>
        </template>
        <Layout class="ninjadash-main-layout">
          <Content>
            <Suspense>
              <template #default>
                <router-view
                  :style="{
                    minHeight: 'calc(100vh - 65px - 42px)',
                  }"
                ></router-view>
              </template>
              <template #fallback>
                <div class="spin">
                  <a-spin />
                </div>
              </template>
            </Suspense>
            <Footer
              class="admin-footer"
              :style="{
                padding: '10px 30px 10px',
                color: 'rgba(0, 0, 0, 0.65)',
                fontSize: '14px',
                background: 'rgba(255, 255, 255, .90)',
                width: '100%',
                boxShadow: '0 -5px 10px rgba(146,153,184, 0.05)',
              }"
            >
              <a-row>
                <a-col :span="12">
                  <span class="admin-footer__copyright"
                    >2023 ©
                    <a href="http://www.oco.com.tw" style="">橙設科技有限公司</a>
                  </span>
                </a-col>
                <a-col :span="12" style="text-align: right;">
                  <span class="admin-footer__version"
                    >版本 v{{ appVersion }} | 更新日期：{{ buildDate }}
                  </span>
                </a-col>
              </a-row>
            </Footer>
          </Content>
          <Realtime
            :pause="pause"
            :collapsed="alarmCollapsed"
            :alarmSummary="alarmSummary"
            @onPauseChanged="onPauseChanged"
            @changePanel="changePanel"
          />
        </Layout>
      </Layout>
    </Layout>
  </Div>
</template>
<script>
import { Layout } from "ant-design-vue";
import {
  Div,
  // SmallScreenSearch,
  // SmallScreenAuthInfo,
  // TopMenuSearch,
} from "./style";
// import HeaderSearch from "../components/header-search/HeaderSearch.vue";
import Notification from "@/components/utilities/auth-info/Notification.vue";
import Realtime from "@/components/oco/alarm/Realtime.vue";
import AuthInfo from "../components/utilities/auth-info/info.vue";
import ThemeToggle from "@/components/utilities/ThemeToggle.vue";
import AsideItems from "./Aside";
// import TopMenu from "./TopMenuItems";
import { PerfectScrollbar } from "vue3-perfect-scrollbar";
import "vue3-perfect-scrollbar/dist/vue3-perfect-scrollbar.css";
import {
  computed,
  ref,
  defineComponent,
  onBeforeUnmount,
  watch,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { useThemeStore } from "@/stores/theme";
import { useAlarmConnection } from "@/composable/alarmConnection";
import { useCCTVConnection } from "@/composable/cctvConnection";
import dayjs from "dayjs";
import router from "@/routes/protectedRoute";
import cctvStream from "@/components/oco/util/cctvModal/Index.vue";
import { useTagInfo } from "@/composable/tagInfo";
import { getItem } from "@/utility/localStorageControl";
const { Header, Footer, Sider, Content } = Layout;

export default defineComponent({
  name: "WithAdminLayout",
  components: {
    Div,
    Header,
    Layout,
    Footer,
    Sider,
    Content,
    // HeaderSearch,
    // SmallScreenSearch,
    // SmallScreenAuthInfo,
    // TopMenuSearch,
    AuthInfo,
    Notification,
    ThemeToggle,
    AsideItems,
    // TopMenu,
    PerfectScrollbar,
    Realtime,
    cctvStream,
  },
  setup() {
    // 初始化主題系統
    const themeStore = useThemeStore();

    // mqtt
    onMounted(() => {
      dispatch("tags/getAllTagsAndOptions");
      // 初始化主題
      themeStore.initTheme();
    });

    const collapsed = ref(false);
    const hide = ref(true);
    const searchHide = ref(true);
    const customizerAction = ref(false);
    const activeSearch = ref(false);

    // const store = useStore();
    const { dispatch, state } = useStore();

    const rtl = computed(() => state.themeLayout.rtlData);
    const darkMode = computed(() => state.themeLayout.data);
    const topMenu = computed(() => state.themeLayout.topMenu);

    // 取得 CustomerName 從 localStorage，只有在登入狀態下才顯示
    const customerName = computed(() => {
      try {
        // 檢查登入狀態
        const isLoggedIn = state.auth.login;
        if (!isLoggedIn) {
          return "";
        }

        const userData = getItem("userData");
        // getItem 已經會自動解析 JSON，所以 userData 可能是物件或字串
        if (userData && typeof userData === 'object' && userData.CustomerName) {
          return userData.CustomerName;
        }
        return "";
      } catch (error) {
        // 靜默處理錯誤，避免控制台噪音
        return "";
      }
    });
    const alarmCollapsed = ref("1");
    const alarmSummary = ref([]);

    // 版本號和建置日期
    const appVersion = ref("2.2.7");
    const buildDate = ref("2025-07-25");
    const importantAlarm = ref([]);
    collapsed.value = window.innerWidth <= 1200 && true;

    let audioQueue = [];
    let currentIndex = 0;
    let isQueueUpdated = false;

    const playCurrentSpeech = () => {
      if (speechSynthesis.speaking) return;

      if (audioQueue.length > 0) {
        const utterance = new SpeechSynthesisUtterance(
          audioQueue[currentIndex]
        );
        utterance.onend = handleSpeechEnd;
        speechSynthesis.speak(utterance);
      }
    };

    const handleSpeechEnd = () => {
      if (isQueueUpdated) {
        currentIndex = 0;
        isQueueUpdated = false; // 重置標誌位
      } else {
        currentIndex = (currentIndex + 1) % audioQueue.length;
      }
      playCurrentSpeech();
    };

    const updateAudioQueue = (newQueue) => {
      const newAudioQueue = newQueue.map((el) => el.AlarmDescription);
      audioQueue = newAudioQueue;
      isQueueUpdated = true;
    };

    const alarmCallback = (sourceData) => {
      const source = JSON.parse(sourceData);

      alarmSummary.value = JSON.parse(source.AlarmSummaryJson).sort((a, b) => {
        return dayjs(b.AlarmTime).unix() - dayjs(a.AlarmTime).unix();
      });
      importantAlarm.value = alarmSummary.value.filter(
        (el) => el.AlarmStatus === 3
      );

      updateAudioQueue(
        alarmSummary.value.filter((el) => el.IsAudio && el.AlarmState === 1)
      );
      playCurrentSpeech();
      const newAlarm = alarmSummary.value[0];
      if (newAlarm.AlarmState === 1) {
        alarmCollapsed.value = "1";
        if (newAlarm.PageId) {
          router.push({
            name: "gui-main",
            params: { id: newAlarm.PageId },
          });
        }
        // if (newAlarm.IsAudio) {
        //   const speech = new SpeechSynthesisUtterance("警報");
        //   speechSynthesis.speak(speech);
        // } else {
        //   if (speechSynthesis.speaking) {
        //     speechSynthesis.cancel();
        //   }
        // }
        const cctvList = useTagInfo(newAlarm.ComponentId, "CctvList");
        if (cctvList && cctvList.length > 0) {
          openCCTVModal({ CctvIdList: cctvList.map((el) => el.Id) });
        }
      }
    };

    const { connection } = useAlarmConnection(alarmCallback);
    onBeforeUnmount(() => {
      connection.stop();
    });

    // const cctvCallback = (cctvId, bytes) => {
    //   console.log(cctvId, bytes);
    // };

    const CCTVModal = ref(false);
    const currCCTV = ref([]);

    const changeAlarmCCTVSetting = (val) => {
      pause.value = val;
    };

    const openCCTVModal = ({ CctvIdList }) => {
      if (pause.value) return;
      currCCTV.value = CctvIdList;
      CCTVModal.value = true;
    };

    const closeCCTVModal = () => {
      CCTVModal.value = false;
    };

    const { connection: cctvConnection } = useCCTVConnection();
    onBeforeUnmount(() => {
      if (cctvConnection) cctvConnection.stop();
    });

    const pause = ref(false);
    const onPauseChanged = (val) => {
      pause.value = val;
    };

    const changePanel = (val) => {
      alarmCollapsed.value = val;
    };

    watch(
      () => pause.value,
      (val) => {
        if (val) {
          connection.stop();
        } else {
          connection.start();
        }
      }
    );

    const toggleCollapsed = (e) => {
      e.preventDefault();
      collapsed.value = !collapsed.value;
    };
    const handleSearchHide = (search) => {
      searchHide.value = !search;
      hide.value = true;
    };
    const onShowHide = (h) => {
      hide.value = !h;
      searchHide.value = true;
    };
    const toggleSearch = () => {
      activeSearch.value = !activeSearch.value;
    };

    const toggleCollapsedMobile = () => {
      // const aside = document.querySelector(".ps--active-y");
      // aside.scrollTop = 0;

      if (innerWidth <= 990) {
        collapsed.value = !collapsed.value;
      }
    };
    if (innerWidth <= 990) {
      document.body.addEventListener("click", (e) => {
        if (
          !e.target.closest(".ant-layout-sider") &&
          !e.target.closest(".navbar-brand .ant-btn")
        ) {
          collapsed.value = true;
        }
      });
    }

    const onRtlChange = () => {
      const html = document.querySelector("html");
      html.setAttribute("dir", "rtl");
      dispatch("changeRtlMode", true);
    };

    const onLtrChange = () => {
      const html = document.querySelector("html");
      html.setAttribute("dir", "ltr");
      dispatch("changeRtlMode", false);
    };

    const modeChangeDark = () => {
      dispatch("changeLayoutMode", true);
    };

    const modeChangeLight = () => {
      dispatch("changeLayoutMode", false);
    };

    const modeChangeTopNav = () => {
      dispatch("changeMenuMode", true);
    };

    const modeChangeSideNav = () => {
      dispatch("changeMenuMode", false);
    };

    const alarms = computed(() => state.alarm.realtimeInitData);

    const onEventChange = {
      onRtlChange,
      onLtrChange,
      modeChangeDark,
      modeChangeLight,
      modeChangeTopNav,
      modeChangeSideNav,
    };

    return {
      alarmCollapsed,
      pause,
      onPauseChanged,
      changePanel,
      alarmSummary,
      importantAlarm,
      toggleCollapsed,
      handleSearchHide,
      toggleCollapsedMobile,
      onShowHide,
      collapsed,
      hide,
      searchHide,
      toggleSearch,
      customizerAction,
      activeSearch,
      innerWidth: window.innerWidth,
      rtl,
      darkMode,
      topMenu,
      onEventChange,
      alarms,
      CCTVModal,
      changeAlarmCCTVSetting,
      currCCTV,
      closeCCTVModal,
      customerName,
      appVersion,
      buildDate,
    };
  },
});
</script>
<style>
body {
  overflow-x: hidden;
}
.ps {
  height: calc(100vh - 100px);
}
.ant-layout-sider-collapsed .ps {
  height: calc(100vh - 70px);
}
::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
}
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* CustomerName 顯示樣式 */
.ninjadash-header-content {
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}

.ninjadash-header-content__left {
  flex: 0 0 auto !important;
}

.ninjadash-header-content__center {
  flex: 1 1 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  position: absolute !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1 !important;
}

.ninjadash-header-content__right {
  flex: 0 0 auto !important;
  position: relative !important;
  z-index: 2 !important;
}

.customer-name-display {
  /* 移除所有背景和邊框 */
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.customer-name-text {
  font-size: 40px !important;
  font-weight: 600 !important;
  color: #000 !important;
  text-shadow: none !important;
  letter-spacing: 0.5px !important;
  white-space: nowrap !important;
}

/* 版本號樣式 */
.admin-footer__version {
  font-size: 12px !important;
  color: #999 !important;
  font-weight: 400 !important;
}

/* 響應式設計 */
@media only screen and (max-width: 768px) {
  .customer-name-text {
    font-size: 36px !important;
  }
}

@media only screen and (max-width: 480px) {
  .customer-name-text {
    font-size: 32px !important;
  }

  .ninjadash-header-content__center {
    display: none !important;
  }
}
</style>
