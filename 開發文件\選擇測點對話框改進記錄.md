# 選擇測點對話框改進記錄

## 問題描述
原本的選擇測點對話框寬度太小（默認 620px），導致測點名稱顯示不完整，用戶體驗不佳。

## 解決方案

### 1. 增加對話框寬度
- 將選擇測點對話框的寬度從默認 620px 增加到 900px
- 適用於以下組件：
  - `src/components/oco/form/tagFilter/Index.vue` - 選擇測點
  - `src/components/oco/form/groupFilter/Index.vue` - 選擇群組
  - `src/components/oco/form/deviceFilter/Index.vue` - 選擇裝置
  - `src/components/oco/form/cctvFilter/Index.vue` - 選擇CCTV

### 2. 添加調整大小功能
- 為對話框添加 `tag-filter-modal` CSS 類
- 實現 CSS `resize: both` 功能，允許用戶手動調整對話框大小
- 設置最小和最大尺寸限制：
  - 最小寬度：600px
  - 最小高度：400px
  - 最大寬度：90vw
  - 最大高度：90vh

### 3. 改善測點列表顯示
- 增加測點列表高度從 300px 到 400px
- 改善文字換行和顯示：
  - 啟用 `word-wrap: break-word`
  - 設置 `white-space: normal`
  - 調整行高為 1.4
- 添加視覺改進：
  - 測點項目間的分隔線
  - 懸停效果
  - 選中狀態的視覺強化

## 修改的文件

### 1. 組件文件
```
src/components/oco/form/tagFilter/Index.vue
src/components/oco/form/groupFilter/Index.vue
src/components/oco/form/deviceFilter/Index.vue
src/components/oco/form/cctvFilter/Index.vue
```

### 2. 樣式文件
```
src/components/oco/form/tagFilter/style.js
src/static/css/style.css
```

## 技術實現細節

### CSS 調整大小功能
```css
.tag-filter-modal .ant-modal-content {
  resize: both;
  overflow: auto;
  min-width: 600px;
  min-height: 400px;
  max-width: 90vw;
  max-height: 90vh;
}
```

### 測點列表改進
```css
.tag {
  word-wrap: break-word;
  white-space: normal;
  line-height: 1.4;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}
```

## 用戶體驗改進

1. **更寬的對話框**：測點名稱能夠完整顯示
2. **可調整大小**：用戶可以根據需要調整對話框大小
3. **更好的視覺效果**：清晰的分隔線和懸停效果
4. **響應式設計**：在不同屏幕尺寸下都能正常工作

## 兼容性
- 支援所有現代瀏覽器
- 保持與現有功能的完全兼容
- 不影響其他對話框的行為

## 左右佈局改進 (v2.2.7 新增)

### 問題描述
原本的選擇對話框採用上下佈局：
- 上方：已選擇的項目（小框顯示）
- 下方：可選擇的項目列表

這種佈局不夠直觀，用戶難以清楚看到已選擇和可選擇的項目。

### 解決方案
改為左右佈局設計：
- **左側**：已選擇的項目（清晰列表顯示）
- **右側**：可選擇的項目列表

### 實現細節

#### 1. 多選組件（tagFilter, groupFilter, cctvFilter）
- 左側顯示已選擇項目，每個項目都有刪除按鈕
- 右側顯示可選擇項目，點擊可添加/移除
- 標題顯示已選擇項目數量
- 空狀態提示

#### 2. 單選組件（deviceFilter）
- 左側顯示當前選擇的裝置，有清除按鈕
- 右側顯示可選擇的裝置列表
- 當前選擇項目有特殊樣式標示

#### 3. 視覺改進
- 已選擇區域使用淺藍色背景
- 添加區域標題和項目計數
- 統一的刪除/清除按鈕樣式
- 懸停效果和過渡動畫

### 用戶體驗提升
1. **更直觀的界面**：左右分佈清楚區分已選和可選
2. **便捷的操作**：直接點擊刪除按鈕移除項目
3. **清晰的狀態**：實時顯示選擇數量和狀態
4. **一致的體驗**：所有選擇對話框使用統一設計

## 測試建議
1. 測試不同長度的測點名稱顯示
2. 驗證調整大小功能在不同瀏覽器中的表現
3. 確認在不同屏幕尺寸下的響應式行為
4. 檢查選中和取消選中測點的功能正常
5. **新增**：測試左右佈局的交互功能
6. **新增**：驗證刪除按鈕和清除功能
7. **新增**：確認多選和單選模式的不同行為
