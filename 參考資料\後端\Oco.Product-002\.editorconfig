root = true
# 如果要從更高的目錄繼承 .editorconfig 設定，請移除以下行

# C# 檔案
[*.cs]

#### 核心 EditorConfig 選項 ####

# 縮排和間距
indent_size = 4
indent_style = space
tab_width = 4

# 新行喜好設定
end_of_line = crlf
insert_final_newline = false

#### .NET 編碼慣例 ####

# 組合管理 Using
dotnet_separate_import_directive_groups = false
dotnet_sort_system_directives_first = true
file_header_template = 

# this. 和 Me. 喜好設定
dotnet_style_qualification_for_event = false
dotnet_style_qualification_for_field = false
dotnet_style_qualification_for_method = false
dotnet_style_qualification_for_property = false

# 語言關鍵字與 BCL 類型喜好設定
dotnet_style_predefined_type_for_locals_parameters_members = true
dotnet_style_predefined_type_for_member_access = true

# 括號喜好設定
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary
dotnet_style_parentheses_in_other_operators = never_if_unnecessary
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary

# 修飾元喜好設定
dotnet_style_require_accessibility_modifiers = for_non_interface_members

# 運算式層級喜好設定
dotnet_style_coalesce_expression = true
dotnet_style_collection_initializer = true
dotnet_style_explicit_tuple_names = true
dotnet_style_namespace_match_folder = true
dotnet_style_null_propagation = true
dotnet_style_object_initializer = true
dotnet_style_operator_placement_when_wrapping = beginning_of_line
dotnet_style_prefer_auto_properties = true:suggestion
dotnet_style_prefer_compound_assignment = true
dotnet_style_prefer_conditional_expression_over_assignment = true:suggestion
dotnet_style_prefer_conditional_expression_over_return = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true
dotnet_style_prefer_inferred_tuple_names = true
dotnet_style_prefer_is_null_check_over_reference_equality_method = true
dotnet_style_prefer_simplified_boolean_expressions = true
dotnet_style_prefer_simplified_interpolation = true

# 欄位喜好設定
dotnet_style_readonly_field = true

# 參數喜好設定
dotnet_code_quality_unused_parameters = all

# 隱藏項目喜好設定
dotnet_remove_unnecessary_suppression_exclusions = none

# 新行喜好設定
dotnet_style_allow_multiple_blank_lines_experimental = true
dotnet_style_allow_statement_immediately_after_block_experimental = true

#### C# 編碼慣例 ####

# var 喜好設定
csharp_style_var_elsewhere = true:silent
csharp_style_var_for_built_in_types = true:silent
csharp_style_var_when_type_is_apparent = true:silent

# 運算式主體成員
csharp_style_expression_bodied_accessors = true
csharp_style_expression_bodied_constructors = true:silent
csharp_style_expression_bodied_indexers = true
csharp_style_expression_bodied_lambdas = true
csharp_style_expression_bodied_local_functions = true
csharp_style_expression_bodied_methods = true:silent
csharp_style_expression_bodied_operators = true
csharp_style_expression_bodied_properties = true

# 模式比對喜好設定
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_prefer_not_pattern = true:suggestion
csharp_style_prefer_pattern_matching = true:suggestion
csharp_style_prefer_switch_expression = true:suggestion

# Null 檢查喜好設定
csharp_style_conditional_delegate_call = true:suggestion

# 修飾元喜好設定
csharp_prefer_static_local_function = true:suggestion
csharp_preferred_modifier_order = public,private,protected,internal,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async

# 程式碼區塊喜好設定
csharp_prefer_braces = true:silent
csharp_prefer_simple_using_statement = true:suggestion

# 運算式層級喜好設定
csharp_prefer_simple_default_expression = true
csharp_style_deconstructed_variable_declaration = true
csharp_style_implicit_object_creation_when_type_is_apparent = true
csharp_style_inlined_variable_declaration = true
csharp_style_pattern_local_over_anonymous_function = true
csharp_style_prefer_index_operator = true
csharp_style_prefer_range_operator = true
csharp_style_throw_expression = true
csharp_style_unused_value_assignment_preference = discard_variable
csharp_style_unused_value_expression_statement_preference = discard_variable:suggestion

# 'using' 指示詞的喜好設定
csharp_using_directive_placement = outside_namespace:silent

# 新行喜好設定
csharp_style_allow_blank_line_after_colon_in_constructor_initializer_experimental = true:silent
csharp_style_allow_blank_lines_between_consecutive_braces_experimental = true:silent
csharp_style_allow_embedded_statements_on_same_line_experimental = true:silent

#### C# 格式化規則 ####

# 新行喜好設定
csharp_new_line_before_catch = true
csharp_new_line_before_else = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_open_brace = all
csharp_new_line_between_query_expression_clauses = true

# 縮排喜好設定
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = true
csharp_indent_labels = one_less_than_current
csharp_indent_switch_labels = true

# 空格喜好設定
csharp_space_after_cast = false
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_binary_operators = before_and_after
csharp_space_around_declaration_statements = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false

# 換行喜好設定
csharp_preserve_single_line_blocks = true
csharp_preserve_single_line_statements = true

#### 命名樣式 ####

# 命名規則

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.const_field_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.const_field_should_be_pascal_case.symbols = const_field
dotnet_naming_rule.const_field_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.private_or_internal_static_field_should_be_begins_with_underscore.severity = suggestion
dotnet_naming_rule.private_or_internal_static_field_should_be_begins_with_underscore.symbols = private_or_internal_static_field
dotnet_naming_rule.private_or_internal_static_field_should_be_begins_with_underscore.style = begins_with_underscore

dotnet_naming_rule.private_or_internal_field_should_be_begins_with_underscore_camlcase.severity = suggestion
dotnet_naming_rule.private_or_internal_field_should_be_begins_with_underscore_camlcase.symbols = private_or_internal_field
dotnet_naming_rule.private_or_internal_field_should_be_begins_with_underscore_camlcase.style = begins_with_underscore_camlcase

# 符號規格

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.private_or_internal_field.applicable_kinds = field
dotnet_naming_symbols.private_or_internal_field.applicable_accessibilities = internal, private, private_protected
dotnet_naming_symbols.private_or_internal_field.required_modifiers = 

dotnet_naming_symbols.private_or_internal_static_field.applicable_kinds = field
dotnet_naming_symbols.private_or_internal_static_field.applicable_accessibilities = internal, private, private_protected
dotnet_naming_symbols.private_or_internal_static_field.required_modifiers = static

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

dotnet_naming_symbols.const_field.applicable_kinds = field
dotnet_naming_symbols.const_field.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.const_field.required_modifiers = const

# 命名樣式

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

dotnet_naming_style.begins_with_underscore.required_prefix = _
dotnet_naming_style.begins_with_underscore.required_suffix = 
dotnet_naming_style.begins_with_underscore.word_separator = 
dotnet_naming_style.begins_with_underscore.capitalization = pascal_case

dotnet_naming_style.begins_with_underscore_camlcase.required_prefix = _
dotnet_naming_style.begins_with_underscore_camlcase.required_suffix = 
dotnet_naming_style.begins_with_underscore_camlcase.word_separator = 
dotnet_naming_style.begins_with_underscore_camlcase.capitalization = camel_case

# ReSharper properties
resharper_csharp_use_indent_from_vs = true
resharper_wrap_object_and_collection_initializer_style = chop_always
dotnet_diagnostic.VSTHRD001.severity=silent
dotnet_diagnostic.VSTHRD002.severity=silent
dotnet_diagnostic.VSTHRD003.severity=silent
dotnet_diagnostic.VSTHRD004.severity=silent
dotnet_diagnostic.VSTHRD010.severity=silent
dotnet_diagnostic.VSTHRD011.severity=silent
dotnet_diagnostic.VSTHRD012.severity=silent
dotnet_diagnostic.VSTHRD102.severity=silent
dotnet_diagnostic.VSTHRD103.severity=silent
dotnet_diagnostic.VSTHRD104.severity=silent
dotnet_diagnostic.VSTHRD105.severity=silent
dotnet_diagnostic.VSTHRD107.severity=silent
dotnet_diagnostic.VSTHRD108.severity=silent
dotnet_diagnostic.VSTHRD109.severity=silent
dotnet_diagnostic.VSTHRD110.severity=silent
csharp_style_prefer_readonly_struct = true:suggestion
csharp_style_prefer_readonly_struct_member = true:suggestion
csharp_style_allow_blank_line_after_token_in_conditional_expression_experimental = true:silent
csharp_style_allow_blank_line_after_token_in_arrow_expression_clause_experimental = true:silent
csharp_style_prefer_extended_property_pattern = true:suggestion
csharp_style_namespace_declarations = file_scoped:silent
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_style_prefer_primary_constructors = false:suggestion

[*.{cs,vb}]
dotnet_style_qualification_for_field = false:silent
dotnet_style_qualification_for_property = false:silent
dotnet_style_qualification_for_method = false:silent
dotnet_style_qualification_for_event = false:silent
dotnet_style_require_accessibility_modifiers = for_non_interface_members:silent
dotnet_code_quality_unused_parameters = all:suggestion
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:silent
dotnet_style_parentheses_in_other_binary_operators = never_if_unnecessary:silent
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:silent
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:silent
dotnet_style_allow_multiple_blank_lines_experimental = true:silent
dotnet_style_allow_statement_immediately_after_block_experimental = true:silent
end_of_line = crlf
dotnet_style_readonly_field = true:suggestion
dotnet_style_predefined_type_for_locals_parameters_members = true:silent
dotnet_style_predefined_type_for_member_access = true:silent