import { defineComponent, computed } from "vue";
import DataTables from "@/components/table/DataTable.vue";
import { useStore } from "vuex";
import { ActionSpan } from "./style.js";
import { usePermission } from "@/composable/permission";
export default defineComponent({
  props: {
    delete: {
      type: Function,
      required: true,
    },
    add: {
      type: Function,
      required: true,
    },
    edit: {
      type: Function,
      required: true,
    },
  },
  components: {
    DataTables,
  },
  setup(props) {
    const { permission } = usePermission();
    const { state, dispatch } = useStore();
    const loading = computed(() => state.uninstall.loading);
    const columns = [
      { title: "名稱", dataIndex: "name", key: "name" },
      { title: "卸載測點數量", dataIndex: "unInstall", key: "unInstall" },
      { title: "加載測點數量", dataIndex: "install", key: "install" },
      { title: "偵測測點", dataIndex: "tag", key: "tag" },
      { title: "操作", dataIndex: "action", key: "action" },
    ];
    const tableData = computed(() =>
      state.uninstall.tableData.map((el) => {
        return {
          name: el.name,
          unInstall: el.unInstall.length,
          install: el.install.length,
          tag: el.target.name,
          action: (
            <ActionSpan>
              {permission.update && (
                <span onClick={() => props.edit(el.id)}>
                  <unicon name="edit"></unicon>
                </span>
              )}
              {permission.delete && (
                <span
                  onClick={() => props.delete({ id: el.id, name: el.name })}
                >
                  <unicon name="trash"></unicon>
                </span>
              )}
            </ActionSpan>
          ),
        };
      })
    );

    const search = (e) => {
      dispatch("uninstall/filterTableData", e.target.value);
    };

    return { permission, loading, columns, tableData, search };
  },
});
