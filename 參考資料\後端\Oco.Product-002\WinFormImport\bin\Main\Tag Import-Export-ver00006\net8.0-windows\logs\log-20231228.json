2023-12-28T16:53:16.4147328+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Razor.Compilation.DefaultViewCompiler]) Initializing Razor view compiler with no compiled views.
2023-12-28T16:53:16.4847766+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.ModelBinderFactory]) Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
2023-12-28T16:53:16.5537399+08:00 [INF] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
2023-12-28T16:53:16.6616223+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.SignalR.Internal.DefaultHubProtocolResolver]) Registered SignalR Protocol: "json", implemented by "Microsoft.AspNetCore.SignalR.Protocol.JsonHubProtocol".
2023-12-28T16:53:16.7036131+08:00 [INF] (///) ([] []) Api Start:2023/12/28 16:53:16
2023-12-28T16:53:16.7149634+08:00 [DBG] (///) ([] [Microsoft.Extensions.Hosting.Internal.Host]) Hosting starting
2023-12-28T16:53:16.7199082+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-0eff3e0f-b689-4a22-9425-75a587f64f2a.xml"'.
2023-12-28T16:53:16.7244526+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-1f62bcd0-0186-4cd5-b725-5177b1571f17.xml"'.
2023-12-28T16:53:16.7263636+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-2bbb4cd3-eb7f-4e0e-a445-3ba19f990f97.xml"'.
2023-12-28T16:53:16.7282104+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-2cc9c5ef-07bb-4f43-8aac-3f81000d32e0.xml"'.
2023-12-28T16:53:16.7300057+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-4a4a2bba-8643-433c-ae86-479ad99087ba.xml"'.
2023-12-28T16:53:16.7315564+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-72f047d6-0b1e-46a9-ab9e-744616291d1d.xml"'.
2023-12-28T16:53:16.7338681+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-9c88be24-247a-4a0f-89c8-cc08e8015053.xml"'.
2023-12-28T16:53:16.7390162+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-a32467f0-a325-4622-9e48-d304b8e8f442.xml"'.
2023-12-28T16:53:16.7411523+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Repositories.FileSystemXmlRepository]) Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-e7aeb23b-8936-40dd-9b31-faa605119a93.xml"'.
2023-12-28T16:53:16.7497912+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {0eff3e0f-b689-4a22-9425-75a587f64f2a}.
2023-12-28T16:53:16.7584445+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {1f62bcd0-0186-4cd5-b725-5177b1571f17}.
2023-12-28T16:53:16.7611818+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {2bbb4cd3-eb7f-4e0e-a445-3ba19f990f97}.
2023-12-28T16:53:16.7671314+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {2cc9c5ef-07bb-4f43-8aac-3f81000d32e0}.
2023-12-28T16:53:16.7700060+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {4a4a2bba-8643-433c-ae86-479ad99087ba}.
2023-12-28T16:53:16.7712247+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {72f047d6-0b1e-46a9-ab9e-744616291d1d}.
2023-12-28T16:53:16.7724717+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {9c88be24-247a-4a0f-89c8-cc08e8015053}.
2023-12-28T16:53:16.7737096+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {a32467f0-a325-4622-9e48-d304b8e8f442}.
2023-12-28T16:53:16.7749602+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager]) Found key {e7aeb23b-8936-40dd-9b31-faa605119a93}.
2023-12-28T16:53:16.7855807+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.DefaultKeyResolver]) Considering key {72f047d6-0b1e-46a9-ab9e-744616291d1d} with expiration date 2024-01-21 05:45:30Z as default key.
2023-12-28T16:53:16.7905063+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.TypeForwardingActivator]) Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
2023-12-28T16:53:16.7939449+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor]) Decrypting secret element using Windows DPAPI.
2023-12-28T16:53:16.7962934+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.TypeForwardingActivator]) Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
2023-12-28T16:53:16.8010245+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory]) Opening CNG algorithm '"AES"' from provider 'null' with chaining mode CBC.
2023-12-28T16:53:16.8051053+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.CngCbcAuthenticatedEncryptorFactory]) Opening CNG algorithm '"SHA256"' from provider 'null' with HMAC.
2023-12-28T16:53:16.8095317+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingProvider]) Using key {72f047d6-0b1e-46a9-ab9e-744616291d1d} as the default key.
2023-12-28T16:53:16.8133382+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.DataProtection.Internal.DataProtectionHostedService]) Key ring with default key {72f047d6-0b1e-46a9-ab9e-744616291d1d} was loaded during application startup.
2023-12-28T16:53:16.9578965+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer]) Using development certificate: "CN=localhost" (Thumbprint: "0E99AF7451C9A8C8B1D97DB984E59F50AD1924E1")
2023-12-28T16:53:17.0061742+08:00 [INF] (///) ([] [Microsoft.Hosting.Lifetime]) Now listening on: "https://localhost:7014"
2023-12-28T16:53:17.0080021+08:00 [INF] (///) ([] [Microsoft.Hosting.Lifetime]) Now listening on: "http://localhost:5170"
2023-12-28T16:53:17.0098710+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Loaded hosting startup assembly "Oco.Product-002.Api"
2023-12-28T16:53:17.0121491+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Loaded hosting startup assembly "Microsoft.AspNetCore.Watch.BrowserRefresh"
2023-12-28T16:53:17.0137491+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Loaded hosting startup assembly "Microsoft.WebTools.BrowserLink.Net"
2023-12-28T16:53:17.0166359+08:00 [INF] (///) ([] [Microsoft.Hosting.Lifetime]) Application started. Press Ctrl+C to shut down.
2023-12-28T16:53:17.0184576+08:00 [INF] (///) ([] [Microsoft.Hosting.Lifetime]) Hosting environment: "Development"
2023-12-28T16:53:17.0198372+08:00 [INF] (///) ([] [Microsoft.Hosting.Lifetime]) Content root path: "D:\_Prj產品Oco.Product-002\SourceCode\BackEnd\Oco.Product-002\Oco.Product-002.Api\"
2023-12-28T16:53:17.0237589+08:00 [DBG] (///) ([] [Microsoft.Extensions.Hosting.Internal.Host]) Hosting started
2023-12-28T16:53:17.0903163+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDEB"" received FIN.
2023-12-28T16:53:17.0903158+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDEA"" received FIN.
2023-12-28T16:53:17.1027120+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEB"" accepted.
2023-12-28T16:53:17.1027120+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEA"" accepted.
2023-12-28T16:53:17.1052757+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEB"" started.
2023-12-28T16:53:17.1057608+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEA"" started.
2023-12-28T16:53:17.1253280+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware]) Failed to authenticate HTTPS connection.
System.IO.IOException:  Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveBlobAsync[TIOAdapter](TIOAdapter adapter)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](TIOAdapter adapter, Boolean receiveFirst, Byte[] reAuthenticationData, Boolean isApm)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2023-12-28T16:53:17.1253280+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware]) Failed to authenticate HTTPS connection.
System.IO.IOException:  Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveBlobAsync[TIOAdapter](TIOAdapter adapter)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](TIOAdapter adapter, Boolean receiveFirst, Byte[] reAuthenticationData, Boolean isApm)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2023-12-28T16:53:17.1691760+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEB"" stopped.
2023-12-28T16:53:17.1691795+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEA"" stopped.
2023-12-28T16:53:17.1752224+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDEB"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
2023-12-28T16:53:17.1752228+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDEA"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
2023-12-28T16:53:17.7401832+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEC"" accepted.
2023-12-28T16:53:17.7451779+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEC"" started.
2023-12-28T16:53:17.7916923+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware]) Connection "0HN07G9NHKDEC" established using the following protocol: Tls12
2023-12-28T16:53:17.8554434+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request starting HTTP/2 GET https://localhost:7014/swagger/index.html - -
2023-12-28T16:53:17.8882408+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.HostFiltering.HostFilteringMiddleware]) Wildcard detected, all requests with hosts will be allowed.
2023-12-28T16:53:18.0035865+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware]) Response markup is scheduled to include browser refresh script injection.
2023-12-28T16:53:18.1131037+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware]) Response markup was updated to include browser refresh script injection.
2023-12-28T16:53:18.1210400+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request finished HTTP/2 GET https://localhost:7014/swagger/index.html - - - 200 - text/html;charset=utf-8 269.4744ms
2023-12-28T16:53:18.1681480+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request starting HTTP/2 GET https://localhost:7014/_vs/browserLink - -
2023-12-28T16:53:18.1683062+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request starting HTTP/2 GET https://localhost:7014/_framework/aspnetcore-browser-refresh.js - -
2023-12-28T16:53:18.1784081+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request finished HTTP/2 GET https://localhost:7014/_framework/aspnetcore-browser-refresh.js - - - 200 12370 application/javascript;+charset=utf-8 10.1106ms
2023-12-28T16:53:18.1988310+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request finished HTTP/2 GET https://localhost:7014/_vs/browserLink - - - 200 - text/javascript;+charset=UTF-8 30.7236ms
2023-12-28T16:53:18.2889375+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request starting HTTP/2 GET https://localhost:7014/swagger/v1/swagger.json - -
2023-12-28T16:53:18.7343108+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request finished HTTP/2 GET https://localhost:7014/swagger/v1/swagger.json - - - 200 - application/json;charset=utf-8 445.5007ms
2023-12-28T16:53:22.9317078+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDED"" accepted.
2023-12-28T16:53:22.9321754+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDED"" received FIN.
2023-12-28T16:53:22.9337072+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDED"" started.
2023-12-28T16:53:22.9341922+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEE"" accepted.
2023-12-28T16:53:22.9377191+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware]) Failed to authenticate HTTPS connection.
System.IO.IOException:  Received an unexpected EOF or 0 bytes from the transport stream.
   at System.Net.Security.SslStream.ReceiveBlobAsync[TIOAdapter](TIOAdapter adapter)
   at System.Net.Security.SslStream.ForceAuthenticationAsync[TIOAdapter](TIOAdapter adapter, Boolean receiveFirst, Byte[] reAuthenticationData, Boolean isApm)
   at Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware.OnConnectionAsync(ConnectionContext context)
2023-12-28T16:53:22.9388292+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEE"" started.
2023-12-28T16:53:22.9428645+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDED"" stopped.
2023-12-28T16:53:22.9463116+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets]) Connection id ""0HN07G9NHKDED"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
2023-12-28T16:53:22.9479067+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware]) Connection "0HN07G9NHKDEE" established using the following protocol: Tls12
2023-12-28T16:53:22.9596123+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request starting HTTP/1.1 POST https://localhost:7014/Tag/CreateNewTag multipart/form-data;+boundary=--------------------------896971946769425134071291 3137
2023-12-28T16:53:22.9641187+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware]) "POST" requests are not supported
2023-12-28T16:53:22.9944154+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Routing.Matching.DfaMatcher]) 1 candidate(s) found for the request path '"/Tag/CreateNewTag"'
2023-12-28T16:53:23.0025289+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Routing.Matching.DfaMatcher]) Endpoint '"Oco.Product_002.Api.Controllers.TagController.CreateNewTag (Oco.Product-002.Api)"' with route pattern '"Tag/CreateNewTag"' is valid for the request path '"/Tag/CreateNewTag"'
2023-12-28T16:53:23.0073722+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware]) Request matched endpoint '"Oco.Product_002.Api.Controllers.TagController.CreateNewTag (Oco.Product-002.Api)"'
2023-12-28T16:53:23.0096494+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Routing.EndpointMiddleware]) Executing endpoint '"Oco.Product_002.Api.Controllers.TagController.CreateNewTag (Oco.Product-002.Api)"'
2023-12-28T16:53:23.0439186+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Route matched with "{action = \"CreateNewTag\", controller = \"Tag\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[Oco.Product_002.Api.Poco.Tag.CreateNewTagRespDetail]] CreateNewTag(Oco.Product_002.Api.Poco.Tag.CreateNewTagReq)" on controller "Oco.Product_002.Api.Controllers.TagController" ("Oco.Product-002.Api").
2023-12-28T16:53:23.0489761+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Execution plan of "authorization" filters (in the following order): ["None"]
2023-12-28T16:53:23.0506632+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Execution plan of "resource" filters (in the following order): ["Microsoft.AspNetCore.Mvc.ViewFeatures.Filters.SaveTempDataFilter"]
2023-12-28T16:53:23.0540136+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Execution plan of "action" filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)", "Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)", "Oco.Product_002.Api.Filter.ActionCheckAccessTokenFilter (Order: 0)", "Oco.Product_002.Api.Filter.ActionCheckPermissionFilterAttribute (Order: 0)", "Oco.Product_002.Api.Filter.SerilogAttribute (Order: 0)"]
2023-12-28T16:53:23.0581583+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Execution plan of "exception" filters (in the following order): ["None"]
2023-12-28T16:53:23.0598381+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Execution plan of "result" filters (in the following order): ["Microsoft.AspNetCore.Mvc.ViewFeatures.Filters.SaveTempDataFilter", "Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)", "Oco.Product_002.Api.Filter.ActionCheckAccessTokenFilter (Order: 0)", "Oco.Product_002.Api.Filter.ActionCheckPermissionFilterAttribute (Order: 0)", "Oco.Product_002.Api.Filter.SerilogAttribute (Order: 0)"]
2023-12-28T16:53:23.0674579+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Executing controller factory for controller "Oco.Product_002.Api.Controllers.TagController" ("Oco.Product-002.Api")
2023-12-28T16:53:23.0701497+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Executed controller factory for controller "Oco.Product_002.Api.Controllers.TagController" ("Oco.Product-002.Api")
2023-12-28T16:53:23.0873242+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel]) Connection id ""0HN07G9NHKDEE"", Request id ""0HN07G9NHKDEE:00000002"": started reading request body.
2023-12-28T16:53:23.0902466+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel]) Connection id ""0HN07G9NHKDEE"", Request id ""0HN07G9NHKDEE:00000002"": done reading request body.
2023-12-28T16:53:23.1042921+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder]) Attempting to bind parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"' ...
2023-12-28T16:53:23.1095004+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinder]) Attempting to bind parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"' using the name '""' in request data ...
2023-12-28T16:53:23.1163865+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Status"' of type '"System.Nullable`1[System.Boolean]"' using the name '"Status"' in request data ...
2023-12-28T16:53:23.1218269+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Status"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.1269402+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."RegionId"' of type '"System.String"' using the name '"RegionId"' in request data ...
2023-12-28T16:53:23.1300973+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."RegionId"' of type '"System.String"'.
2023-12-28T16:53:23.1322695+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DeviceId"' of type '"System.String"' using the name '"DeviceId"' in request data ...
2023-12-28T16:53:23.1343844+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DeviceId"' of type '"System.String"'.
2023-12-28T16:53:23.1383249+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."CCTVIdList"' of type '"System.Collections.Generic.List`1[System.String]"' using the name '"CCTVIdList"' in request data ...
2023-12-28T16:53:23.1409903+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Could not find a value in the request with name '"CCTVIdList"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."CCTVIdList"' of type '"System.Collections.Generic.List`1[System.String]"'.
2023-12-28T16:53:23.1443152+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."CCTVIdList"' of type '"System.Collections.Generic.List`1[System.String]"'.
2023-12-28T16:53:23.1465647+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."SimpleTagName"' of type '"System.String"' using the name '"SimpleTagName"' in request data ...
2023-12-28T16:53:23.1488663+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."SimpleTagName"' of type '"System.String"'.
2023-12-28T16:53:23.1533548+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Description"' of type '"System.String"' using the name '"Description"' in request data ...
2023-12-28T16:53:23.1560418+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Description"' of type '"System.String"'.
2023-12-28T16:53:23.1595046+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ValueAddress"' of type '"System.String"' using the name '"ValueAddress"' in request data ...
2023-12-28T16:53:23.1616447+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ValueAddress"' of type '"System.String"'.
2023-12-28T16:53:23.1638837+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."TagCategoryIdList"' of type '"System.Collections.Generic.List`1[System.String]"' using the name '"TagCategoryIdList"' in request data ...
2023-12-28T16:53:23.1680868+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind model of type '"System.String"' using the name '"TagCategoryIdList"' in request data ...
2023-12-28T16:53:23.1702632+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind model of type '"System.String"' using the name '"TagCategoryIdList"'.
2023-12-28T16:53:23.1743903+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."TagCategoryIdList"' of type '"System.Collections.Generic.List`1[System.String]"'.
2023-12-28T16:53:23.1764408+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."MeasurementUnit"' of type '"System.Nullable`1[System.Int32]"' using the name '"MeasurementUnit"' in request data ...
2023-12-28T16:53:23.1787812+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."MeasurementUnit"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.1808533+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DataType"' of type '"System.Nullable`1[System.Int32]"' using the name '"DataType"' in request data ...
2023-12-28T16:53:23.1843336+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DataType"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.1871489+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."InitialValue"' of type '"System.String"' using the name '"InitialValue"' in request data ...
2023-12-28T16:53:23.1911001+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."InitialValue"' of type '"System.String"'.
2023-12-28T16:53:23.1932456+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IgnoreThreshold"' of type '"System.String"' using the name '"IgnoreThreshold"' in request data ...
2023-12-28T16:53:23.1965034+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"IgnoreThreshold"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IgnoreThreshold"' of type '"System.String"'.
2023-12-28T16:53:23.1985839+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IgnoreThreshold"' of type '"System.String"'.
2023-12-28T16:53:23.2006838+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Type"' of type '"System.Nullable`1[System.Int32]"' using the name '"Type"' in request data ...
2023-12-28T16:53:23.2064508+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Type"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2087484+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Retentive"' of type '"System.Nullable`1[System.Boolean]"' using the name '"Retentive"' in request data ...
2023-12-28T16:53:23.2109394+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."Retentive"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.2132091+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."SaveType"' of type '"System.Nullable`1[System.Int32]"' using the name '"SaveType"' in request data ...
2023-12-28T16:53:23.2164881+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."SaveType"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2193736+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsLog"' of type '"System.Nullable`1[System.Boolean]"' using the name '"IsLog"' in request data ...
2023-12-28T16:53:23.2231534+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsLog"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.2254359+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DataInterval"' of type '"System.Nullable`1[System.Int32]"' using the name '"DataInterval"' in request data ...
2023-12-28T16:53:23.2276011+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DataInterval"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2297207+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LogInterval"' of type '"System.Nullable`1[System.Int32]"' using the name '"LogInterval"' in request data ...
2023-12-28T16:53:23.2328918+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"LogInterval"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LogInterval"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2379332+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LogInterval"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2407376+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LogIntervalType"' of type '"System.Nullable`1[System.Int32]"' using the name '"LogIntervalType"' in request data ...
2023-12-28T16:53:23.2430532+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LogIntervalType"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2460288+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmStatus"' of type '"System.Nullable`1[System.Int32]"' using the name '"AlarmStatus"' in request data ...
2023-12-28T16:53:23.2481339+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmStatus"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.2505819+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsAlarmAudio"' of type '"System.Nullable`1[System.Boolean]"' using the name '"IsAlarmAudio"' in request data ...
2023-12-28T16:53:23.2546914+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsAlarmAudio"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.2570736+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmNotifyGroupList"' of type '"System.Collections.Generic.List`1[System.String]"' using the name '"AlarmNotifyGroupList"' in request data ...
2023-12-28T16:53:23.2592155+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Could not find a value in the request with name '"AlarmNotifyGroupList"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmNotifyGroupList"' of type '"System.Collections.Generic.List`1[System.String]"'.
2023-12-28T16:53:23.2626057+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmNotifyGroupList"' of type '"System.Collections.Generic.List`1[System.String]"'.
2023-12-28T16:53:23.2647178+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmSOP"' of type '"System.String"' using the name '"AlarmSOP"' in request data ...
2023-12-28T16:53:23.2688527+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"AlarmSOP"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmSOP"' of type '"System.String"'.
2023-12-28T16:53:23.2721243+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmSOP"' of type '"System.String"'.
2023-12-28T16:53:23.2742474+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"AlarmExceptionStatus"' in request data ...
2023-12-28T16:53:23.2772622+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.2796381+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionStartAt"' of type '"System.Nullable`1[System.DateTime]"' using the name '"AlarmExceptionStartAt"' in request data ...
2023-12-28T16:53:23.2833781+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Could not find a value in the request with name '"AlarmExceptionStartAt"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionStartAt"' of type '"System.Nullable`1[System.DateTime]"'.
2023-12-28T16:53:23.2859414+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionStartAt"' of type '"System.Nullable`1[System.DateTime]"'.
2023-12-28T16:53:23.2883018+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionEndAt"' of type '"System.Nullable`1[System.DateTime]"' using the name '"AlarmExceptionEndAt"' in request data ...
2023-12-28T16:53:23.2906158+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Could not find a value in the request with name '"AlarmExceptionEndAt"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionEndAt"' of type '"System.Nullable`1[System.DateTime]"'.
2023-12-28T16:53:23.2932159+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionEndAt"' of type '"System.Nullable`1[System.DateTime]"'.
2023-12-28T16:53:23.2952216+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionUntil"' of type '"System.Nullable`1[System.Int32]"' using the name '"AlarmExceptionUntil"' in request data ...
2023-12-28T16:53:23.2989521+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"AlarmExceptionUntil"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionUntil"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.3014477+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionUntil"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.3041077+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionAction"' of type '"System.Nullable`1[System.Int32]"' using the name '"AlarmExceptionAction"' in request data ...
2023-12-28T16:53:23.3070328+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"AlarmExceptionAction"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionAction"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.3108993+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."AlarmExceptionAction"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.3147306+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"HHStatus"' in request data ...
2023-12-28T16:53:23.3170988+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.3200486+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHValue"' of type '"System.String"' using the name '"HHValue"' in request data ...
2023-12-28T16:53:23.3225852+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"HHValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHValue"' of type '"System.String"'.
2023-12-28T16:53:23.3249866+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHValue"' of type '"System.String"'.
2023-12-28T16:53:23.3297890+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHContent"' of type '"System.String"' using the name '"HHContent"' in request data ...
2023-12-28T16:53:23.3328566+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"HHContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHContent"' of type '"System.String"'.
2023-12-28T16:53:23.3359163+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HHContent"' of type '"System.String"'.
2023-12-28T16:53:23.3381621+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"HIStatus"' in request data ...
2023-12-28T16:53:23.3404359+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.3448374+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIValue"' of type '"System.String"' using the name '"HIValue"' in request data ...
2023-12-28T16:53:23.3470044+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"HIValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIValue"' of type '"System.String"'.
2023-12-28T16:53:23.3492962+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIValue"' of type '"System.String"'.
2023-12-28T16:53:23.3513269+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIContent"' of type '"System.String"' using the name '"HIContent"' in request data ...
2023-12-28T16:53:23.3549614+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"HIContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIContent"' of type '"System.String"'.
2023-12-28T16:53:23.3597217+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."HIContent"' of type '"System.String"'.
2023-12-28T16:53:23.3618533+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"LLStatus"' in request data ...
2023-12-28T16:53:23.3641159+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.3663978+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLValue"' of type '"System.String"' using the name '"LLValue"' in request data ...
2023-12-28T16:53:23.3687484+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"LLValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLValue"' of type '"System.String"'.
2023-12-28T16:53:23.3717987+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLValue"' of type '"System.String"'.
2023-12-28T16:53:23.3753030+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLContent"' of type '"System.String"' using the name '"LLContent"' in request data ...
2023-12-28T16:53:23.3776056+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"LLContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLContent"' of type '"System.String"'.
2023-12-28T16:53:23.3798675+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LLContent"' of type '"System.String"'.
2023-12-28T16:53:23.3819509+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"LOStatus"' in request data ...
2023-12-28T16:53:23.3842035+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.3872648+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOValue"' of type '"System.String"' using the name '"LOValue"' in request data ...
2023-12-28T16:53:23.3897842+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"LOValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOValue"' of type '"System.String"'.
2023-12-28T16:53:23.3935392+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOValue"' of type '"System.String"'.
2023-12-28T16:53:23.3962294+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOContent"' of type '"System.String"' using the name '"LOContent"' in request data ...
2023-12-28T16:53:23.3984937+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"LOContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOContent"' of type '"System.String"'.
2023-12-28T16:53:23.4007681+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."LOContent"' of type '"System.String"'.
2023-12-28T16:53:23.4040123+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"DigitalAlarmStatus"' in request data ...
2023-12-28T16:53:23.4081223+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.4102254+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmValue"' of type '"System.Nullable`1[System.Int32]"' using the name '"DigitalAlarmValue"' in request data ...
2023-12-28T16:53:23.4123557+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"DigitalAlarmValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmValue"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4146128+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmValue"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4169395+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmContent"' of type '"System.String"' using the name '"DigitalAlarmContent"' in request data ...
2023-12-28T16:53:23.4197118+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"DigitalAlarmContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmContent"' of type '"System.String"'.
2023-12-28T16:53:23.4243279+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalAlarmContent"' of type '"System.String"'.
2023-12-28T16:53:23.4264069+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalStatus"' of type '"System.Nullable`1[System.Boolean]"' using the name '"DigitalNormalStatus"' in request data ...
2023-12-28T16:53:23.4286572+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalStatus"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.4307770+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalValue"' of type '"System.Nullable`1[System.Int32]"' using the name '"DigitalNormalValue"' in request data ...
2023-12-28T16:53:23.4334278+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"DigitalNormalValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalValue"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4362735+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalValue"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4400716+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalContent"' of type '"System.String"' using the name '"DigitalNormalContent"' in request data ...
2023-12-28T16:53:23.4422924+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"DigitalNormalContent"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalContent"' of type '"System.String"'.
2023-12-28T16:53:23.4444959+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."DigitalNormalContent"' of type '"System.String"'.
2023-12-28T16:53:23.4465073+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsUseExpression"' of type '"System.Nullable`1[System.Boolean]"' using the name '"IsUseExpression"' in request data ...
2023-12-28T16:53:23.4487197+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."IsUseExpression"' of type '"System.Nullable`1[System.Boolean]"'.
2023-12-28T16:53:23.4509290+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressMode"' of type '"System.Nullable`1[System.Int32]"' using the name '"ExpressMode"' in request data ...
2023-12-28T16:53:23.4558435+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"ExpressMode"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressMode"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4582820+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressMode"' of type '"System.Nullable`1[System.Int32]"'.
2023-12-28T16:53:23.4603241+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressValue"' of type '"System.String"' using the name '"ExpressValue"' in request data ...
2023-12-28T16:53:23.4626384+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Could not find a value in the request with name '"ExpressValue"' for binding property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressValue"' of type '"System.String"'.
2023-12-28T16:53:23.4665341+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinder]) Done attempting to bind property '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"."ExpressValue"' of type '"System.String"'.
2023-12-28T16:53:23.4714528+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinder]) Done attempting to bind parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"'.
2023-12-28T16:53:23.4730276+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder]) Done attempting to bind parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"'.
2023-12-28T16:53:23.4745657+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder]) Attempting to validate the bound parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"' ...
2023-12-28T16:53:23.4861270+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder]) Done attempting to validate the bound parameter '"req"' of type '"Oco.Product_002.Api.Poco.Tag.CreateNewTagReq"'.
2023-12-28T16:53:23.8925761+08:00 [INF] (///) ([] []) {"action":"Request","staff":{"Account":"<EMAIL>","CustomerId":"fdff1878-a54a-44ee-b82c-a62bdc5cdb55"},"path":{"action":"CreateNewTag","controller":"Tag"},"param":{"Status":true,"RegionId":"d3ea88ce-585d-4bfd-a6af-0d9a8804f896","DeviceId":"88b04f48-3b1f-4f3a-8616-d76e5505406d","CCTVIdList":null,"SimpleTagName":"AAAA","Description":"測點1(類比)--運算式.","ValueAddress":"400002","TagCategoryIdList":["a8009227-74f9-42b3-a20c-cf1fa69507a5"],"MeasurementUnit":3,"DataType":8,"InitialValue":"0","IgnoreThreshold":null,"Type":0,"Retentive":false,"SaveType":1,"IsLog":false,"DataInterval":1000,"LogInterval":null,"LogIntervalType":1,"AlarmStatus":1,"IsAlarmAudio":false,"AlarmNotifyGroupList":null,"AlarmSOP":null,"AlarmExceptionStatus":false,"AlarmExceptionStartAt":null,"AlarmExceptionEndAt":null,"AlarmExceptionUntil":null,"AlarmExceptionAction":null,"HHStatus":false,"HHValue":null,"HHContent":null,"HIStatus":false,"HIValue":null,"HIContent":null,"LLStatus":false,"LLValue":null,"LLContent":null,"LOStatus":false,"LOValue":null,"LOContent":null,"DigitalAlarmStatus":false,"DigitalAlarmValue":null,"DigitalAlarmContent":null,"DigitalNormalStatus":false,"DigitalNormalValue":null,"DigitalNormalContent":null,"IsUseExpression":false,"ExpressMode":null,"ExpressValue":null},"requestTime":"2023-12-28T16:53:23.825928+08:00"}
2023-12-28T16:53:24.2921897+08:00 [INF] (///) ([] []) {"action":"Response","staff":{"Account":"<EMAIL>","CustomerId":"fdff1878-a54a-44ee-b82c-a62bdc5cdb55"},"filterException":null,"statusCode":200,"path":{"action":"CreateNewTag","controller":"Tag"},"response":{"Detail":null,"ReturnCode":14,"Message":"指定了重複的測點名稱"},"responseTime":"2023-12-28T16:53:24.2868543+08:00","message":{"Detail":null,"ReturnCode":14,"Message":"指定了重複的測點名稱"}}
2023-12-28T16:53:24.2988862+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector]) List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"]
2023-12-28T16:53:24.3062844+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector]) No information found on request to perform content negotiation.
2023-12-28T16:53:24.3080276+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector]) Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
2023-12-28T16:53:24.3095658+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector]) Attempting to select the first formatter in the output formatters list which can write the result.
2023-12-28T16:53:24.3117431+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.DefaultOutputFormatterSelector]) Selected output formatter '"Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter"' and content type '"application/json"' to write the response.
2023-12-28T16:53:24.3151135+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor]) Executing "ObjectResult", writing value of type '"<>f__AnonymousType11`3[[Oco.Product_002.Api.Poco.Tag.CreateNewTagRespDetail, Oco.Product-002.Api.Poco, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Oco.Core.ErrException.Types.EnumExceptionCode, Oco.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]"'.
2023-12-28T16:53:24.3415213+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker]) Executed action "Oco.Product_002.Api.Controllers.TagController.CreateNewTag (Oco.Product-002.Api)" in 1270.044ms
2023-12-28T16:53:24.3455451+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Routing.EndpointMiddleware]) Executed endpoint '"Oco.Product_002.Api.Controllers.TagController.CreateNewTag (Oco.Product-002.Api)"'
2023-12-28T16:53:24.3474254+08:00 [DBG] (///) ([] [Microsoft.AspNetCore.Server.Kestrel.Connections]) Connection id ""0HN07G9NHKDEE"" completed keep alive response.
2023-12-28T16:53:24.3485337+08:00 [INF] (///) ([] [Microsoft.AspNetCore.Hosting.Diagnostics]) Request finished HTTP/1.1 POST https://localhost:7014/Tag/CreateNewTag multipart/form-data;+boundary=--------------------------896971946769425134071291 3137 - 200 - application/json;+charset=utf-8 1388.9573ms
