npm v9.5.1
node v18.16.1
Node.js 16.20.2


鎖住 node / npm 版本 (Optional)
------------------------------------------------------------------------------------------
用指令建立.nvmrc 
.nvmrc 是一個純文字檔，用來告訴 Node.js 開發者或部署環境該使用哪個 Node 版本。非常適合團隊開發或多專案切換。
echo "18.16.1" > .nvmrc


如果你堅持用 esm，就要降級 Node.js 到 v14 或 v16，因為 esm 與 v18+ 相衝突。
nvm install 16.20.2
nvm use 16.20.2

#MAC 固定住版本
#------------------------------------------------------------------------------------------
#安裝指定版本的 Node.js（含 npm）
nvm install 18.16.1
nvm alias default 18.16.1

node -v   # ✅ v18.16.1
npm -v    # ✅ v9.5.1

#Project setup
#------------------------------------------------------------------------------------------
nvm install 18.16.1 #安裝專案所需的所有依賴套件。
nvm use 18.16.1		#鎖定版本
npm run serve 		#編譯專案並在開發環境中啟動伺服器，支援即時更新（hot-reload）。
npm run build 		#為正式上線環境編譯並最小化檔案（進行壓縮與優化）。
npm run lint  		#執行 Lint 工具來檢查與修正語法風格錯誤（依照 ESLint 規則）。

http://localhost:8080/auth/fdff1878-a54a-44ee-b82c-a62bdc5cdb55

localhost:8345/auth/fdff1878-a54a-44ee-b82c-a62bdc5cdb55

