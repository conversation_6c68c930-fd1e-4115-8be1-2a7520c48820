<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Oco.Product-002.Api</name>
    </assembly>
    <members>
        <member name="P:Oco.Product_002.Api.ApiConfig.ReliabilityAnalysisTagMappingFilsPath">
            <summary>
            可靠度分析時測點對照檔案的路徑
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.CustomerIdNameInRequestUrlSegmentSn">
            <summary>
            客戶ID名稱在Request Url 字節的第?段(從第0段開始)
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.SerilogPath">
            <summary>
            Serilog 路徑
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.TryDecigoCCConfig">
            <summary>
            是否測試 DesigoCC 組態正確性
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.SaveDecigoCCConfig">
            <summary>
            是否儲存 DesigoCC 組態 資料
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.DesigoCCProcessExpireTime">
            <summary>
            處理Decigo CC 逾時時間(ms)
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.ObixProcessExpireTime">
            <summary>
            處理Obix 逾時時間(ms)
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.LineNotifyApiUrl">
            <summary>
            發送Line Notify訊息Api 的URL
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.LineNotifyRegCallbackUrl">
            <summary>
            註冊LINE Notify服務的回呼的網頁URL
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.LineNotifyAuthApiUrl">
            <summary>
            Line Notify 認證的Api URL
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.LineNotifyAuthResponseMode">
            <summary>
            Line Notify 回覆申請時的回覆模式
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.LineNotifyCreateAccessTokenApiUrl">
            <summary>
            Line Notify 取得Access Token Api 的 URL
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.ProcessAlarmFromDesigoCC">
            <summary>
            要處理 DesigoCC 送來的警報?
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.ProcessRealTimeDataValueFromDesigoCC">
            <summary>
            是否回送即時資料到前端
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.SendAlarmSummaryToFront">
            <summary>
            是否將警報摘要送回前端?
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.InformMessagePattern">
            <summary>
            通知訊息 的Pattern
            </summary>        
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.CheckConnectionAlarmSummaryCodeList">
            <summary>
            需要檢查連線代碼集合;代碼參考:Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionCategory
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.ApiConfig.TagImportAndExportConfig">
            <summary>
            測點匯出/匯入 
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.TagImportAndExportConfig">
            <summary>
            測點匯出/匯入時, 匯入檔案存的位置
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.TagImportAndExportConfig.FileExtensionName">
            <summary>
            匯入檔案的副檔名
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.TagImportAndExportConfig.ImportFileDir">
            <summary>
            匯入測點檔案上傳後存放位置
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.TagImportAndExportConfig.ExportTagFileWebPath">
            <summary>
            測點匯出網站的 Path
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.ApiHelper.ApiTagHelper.GetTagChannelNameForCompare(System.String)">
            <summary>
            比較Channel Name 是不分大小來比較
            </summary>
            <param name="tagChannelName"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.ApiHelper.CustomerHelper.ISCustomerIdFormatLegal(System.String)">
            <summary>
            客戶ID格式不對
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.ApiHelper.CustomerHelper.CustomerStatus(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader,System.Guid)">
            <summary>
            客戶狀態
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.CCTVLogic.ModifyCCTVCCheck.CheckDescription">
            <summary>
            檢查說明
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.CCTVLogic.ModifyCCTVCCheck.CheckIdAsync">
            <summary>
            檢查 ID
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.CCTVLogic.ModifyCCTVCCheck.CheckCCTVNameAsync">
            <summary>
            檢查CCTV名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.CreateNewDesigoCcConfigLogicCheck.CheckConfigNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查組態名稱
            <para>1. 是否填寫名稱?</para>
            <para>2. 是否重複名稱?</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.CreateNewDesigoCcConfigLogicCheck.CheckIpAndPortAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)" -->
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.CreateNewDesigoCcConfigLogicCheck.CheckSystemItems">
            <summary>
            檢查系統項目：
            <para>WebApp Name</para>
            <para>System Name</para>
            <para>System Id</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.CreateNewDesigoCcConfigLogicCheck.CheckAccount">
            <summary>
            檢查帳戶：
            <para>User Name</para>
            <para>User Password</para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.SelectDesigoCCTagList.CheckAndGet_DefaultChannelAndDefaultRegionAsync">
            <summary>
            檢查與取得 DesigoCC 預設 Region 與預設 TagChannel 資料 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.SelectDesigoCCTagList.ExecuteAsync">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.ToLoadTagListLogicCheck.CheckDeviceIdAndSetConfigAsync">
            <summary>
            檢查 Device Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.UpdateDesigoCcConfigLogicCheck.CheckConfigNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查組態名稱
            <para>1. 是否填寫名稱?</para>
            <para>2. 是否重複名稱?</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.UpdateDesigoCcConfigLogicCheck.CheckIpAndPortAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)" -->
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.UpdateDesigoCcConfigLogicCheck.CheckSystemItems">
            <summary>
            檢查系統項目：
            <para>WebApp Name</para>
            <para>System Name</para>
            <para>System Id</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.UpdateDesigoCcConfigLogicCheck.CheckAccount">
            <summary>
            檢查帳戶：
            <para>User Name</para>
            <para>User Password</para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.UpdateDesigoCcConfigLogicCheck.CheckConfigIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
             <summary>
            是否填入ID? ID 是否存在?
             </summary>
             <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerConsumableTagForUnloadLogic.CheckTagIdListAsync(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
            檢查測點ID 集合:
            <para>是否有格式不對的測點</para>
            <para>是否有非能耗或不存在的測點</para>
            </summary>
            <param name="dbContext"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.EmptyElectricPowerConsumableTagIdForUnloadListException"></exception>
            <exception cref="T:Oco.Core.ErrException.SomePowerConsumableTagIdForUnloadNotExistException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerConsumableTagForUnloadLogic.CheckSummaryIdAsync(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
            檢查 摘要 ID:
            <para>
            未設定摘要ID
            </para>
            <para>
            摘要ID 格式不 對
            </para>
            <para>
            摘要ID 尚未建立
            </para>
            </summary>
            <param name="dbContext"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.EmptyElectricPowerUnloadLoadSummaryIdException"></exception>
            <exception cref="T:Oco.Core.ErrException.IlegalElectricPowerUnloadLoadSummaryIdFormatException"></exception>
            <exception cref="T:Oco.Core.ErrException.ElectricPowerUnloadLoadSummaryIdNotExistException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerConsumableTagForUnloadLogic.GetElectricPowerConsumableTagListForUnloadAsync(Microsoft.EntityFrameworkCore.DbContext,System.Guid)">
            <summary>
            取得計算能耗的測點
            </summary>
            <param name="dbContext"></param>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadStageGroupLogic.CreateStageModifier">
            <summary>
            新增
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadStageGroupLogic.UpdateStageModifier">
            <summary>
            修改
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadStageGroupLogic.DeleteStageModifier">
            <summary>
            刪除
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadTagLogic.CheckStageAsyncWithThrow(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
            檢查Stage, 如果 Stage 未填或不存在, 則丟出例外
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadTagLogic.CheckRequestTagIdExistAndDuplicatedAsyncWithThrow(Microsoft.EntityFrameworkCore.DbContext)">
            <summary>
            檢查Request 的 Tag Id 是否重複, 或在目前的 Tag 集合中
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.EditElectricPowerUnloadTagLogic.CheckTagDetailWithThrow">
            <summary>
            檢查 測點明細, 如有不合理者丟例外
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic.GetTotalElectricPowerConsumableTagListInSystemAsync(Microsoft.EntityFrameworkCore.DbContext,System.Guid)">
            <summary>
            取得系統內己建立計算能耗的測點
            </summary>
            <param name="dbContext"></param>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="F:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic._electricPowerConsumableTagListInSystem">
            <summary>
            系統內已建立的能耗測點集合
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic._tagChannelsListInSystem">
            <summary>
            系統內已建立的 Channel 集合
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic._devicesListInSystem">
            <summary>
            系統內已建立的 Device 集合
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic.GetConsumableTagListBySummaryIdAsync(Microsoft.EntityFrameworkCore.DbContext,System.Guid,System.Guid)">
            <summary>
            取得 Customer 與 Summary 的所有測點 
            </summary>
            <param name="customerId"></param>
            <param name="summaryId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic.IsElectricPowerConsumptionUnit(System.Int32)">
            <summary>
            是否為計算電力消耗的單位
            </summary>
            <param name="unit"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ElectricPowerUnloadLogic.GetElectricPowerConsumableTagForUnloadListLogic.GetElectricPowerUnloadSummaryListAsync(Microsoft.EntityFrameworkCore.DbContext,System.Guid)">
            <summary>
            取得 Customer 所有卸載摘要
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.BusinessLogic.IdentityLogic.RefreshTokenLogic">
            <summary>
                更新 Refresh Token 的邏輯
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.IdentityLogic.RefreshTokenLogic.#ctor(Oco.Product_002.Api.Poco.Identity.RefreshTokenReq,Oco.Product_002.Api.Controllers.Types.BaseController)">
            <summary>
                建構式
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.IdentityLogic.RefreshTokenLogic.CheckIfHasRefreshToken">
            <summary>
                檢查是否有設定 FreshToken
            </summary>
            <exception cref="T:Oco.Core.ErrException.EmptyRefreshTokenException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.IdentityLogic.RefreshTokenLogic.CheckRefreshTokenObj(Oco.Identity.Token.RefreshTokenModel)">
            <summary>
                檢查 RefreshToken Model
            </summary>
            <param name="obj"></param>
            <exception cref="T:Oco.Core.ErrException.RefreshTokenExpiredException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.IdentityLogic.RefreshTokenLogic.ExecuteAsync">
            <summary>
                執行
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.CannotParseRefreshTokenException"></exception>
            <exception cref="T:Oco.Core.ErrException.RefreshTokenNotExistException"></exception>
        </member>
        <member name="P:Oco.Product_002.Api.BusinessLogic.MessageLogic.CreateLineNotifyAccessTokenLogic.GetChatRoomInstanceByIdConditionList">
            <summary>
            取得 LINE Notify Chat Room 的條件式
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.CreateLineNotifyAccessTokenLogicCheck.CheckStateAsync">
            <summary>
            檢查 State
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.IncorrectLineNotifyRegStateException">未設定 State 或 State 不正確</exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.CreateLineNotifyAccessTokenLogicCheck.CheckCode">
            <summary>
            檢查Code
            </summary>
            <exception cref="T:Oco.Core.ErrException.EmptyLineNotifyRegCodeException">没有填入註冊碼時</exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.ModifyLineServiceCheck.CheckIdAsync">
            <summary>
            檢查Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.ModifyLineServiceCheck.CheckNameAsync">
            <summary>
            檢查名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.ModifyLineServiceCheck.CheckClientSecret">
            <summary>
            檢查 Client Secret
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.ModifyLineServiceCheck.CheckClientId">
            <summary>
            檢查 Client Id
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.#ctor(Oco.Product_002.Api.Poco.Message.Types.SmsGroupModifyRequestModel,Oco.Product_002.Model.DataBase.TableModels.Staff,System.Action{System.String},Oco.Product_002.Api.BusinessLogic.MessageLogic.ExpCallback{Oco.Core.Message.EnumMessageTimeDurationUntil},System.Boolean)">
            <summary>
            
            </summary>
            <param name="requestModel"></param>
            <param name="isUpdate">是否為更新</param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckInformMethod">
            <summary>
            檢查通知方式
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckTelAndName">
            <summary>
            檢查電話名稱
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckTimeAndDurationUntil">
            <summary>
            簡查啟始時間與動作延續
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckGroupIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            更新時檢查群組ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckLineChatRoomIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 Line 聊天室 ID
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.SmsGroupModifyRequestCheck.CheckNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查名稱
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.MessageLogic.MessageGroupMemberModifyHelper.InstallMessageAndMemberRelationAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,Oco.Product_002.Model.DataBase.TableModels.Staff,System.Collections.Generic.List{Oco.Core.ValuePair.NumberNamePair{System.String,System.String}})">
            <summary>
            建立 Message群組 與成員關係
            </summary>
            <param name="db"></param>
            <param name="messageGrpId"></param>
            <param name="opr"></param>
            <param name="members"></param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ObixLogic.BaseObixLogic`2.IsSelectableByWriteType(System.String)">
            <summary>
            這些WriteType 是否為可以被選定的測點
            </summary>
            <param name="writeType"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ObixLogic.BaseObixLogic`2.GetSaveTypeFromWriteType(System.String)">
            <summary>
            從 WriteType 取得 是否為  唯讀/可讀寫
            </summary>
            <param name="writeType"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ObixLogic.BaseObixLogic`2.GetTagTypeFromWriteType(System.String)">
            <summary>
            從 writeType 取得測點為 布林或類比 
            </summary>
            <param name="writeType"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ObixLogic.BaseObixLogic`2.GetDataTypeFromWriteType(System.String)">
            <summary>
            從 writeType 取得 測點的資型態(boolean, float,......) 
            </summary>
            <param name="writeType"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.ObixLogic.ToLoadObixTagListLogicCheck.CheckDeviceIdAndGetDeviceAsync">
            <summary>
            檢查 Device Id, 並取得 Device 資料  
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StaffLogic.StaffLoginLogic.ExecuteAsync">
            <summary>
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.LoginFailedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogic.GetStatisticResultRespDetailDetail(System.Collections.Generic.List{System.Tuple{System.DateTime,System.Nullable{System.Single}}},Oco.Core.ValuePair.IdNamePair{System.Guid,System.String},Oco.Core.Statistic.EnumStatisticalMethods,System.Nullable{System.Single})">
            <summary>
            取得某個客戶的統計結果
            </summary>
            <param name="rawList">原始資料</param>
            <param name="tag"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogic.GetCaculatedValue(System.Collections.Generic.IEnumerable{System.Tuple{System.Single,System.String}},Oco.Core.Statistic.EnumStatisticalMethods)">
            <summary>
            計算結果
            </summary>
            <param name="ie"></param>
            <param name="statisticMethod"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogic.GetReportByTagsAsync">
            <summary>
            依測點處理
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogicCheck.CheckQueryTimeDuration">
            <summary>
            檢查查尋日期時間區間
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogicCheck.CheckDetailDuration">
            <summary>
            檢查明細區間
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogicCheck.CheckStatisticMethod">
            <summary>
            檢查統計方式
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogicCheck.CheckTargetObjectListAsync">
            <summary>
            檢查測點ID 或群組 Id 列表
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.StasticLogic.GetStatisticResultLogicCheck.CheckSearchCondition">
            <summary>
            搜尋條件檢查
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.CreateNewPageItemLogicCheck.CheckAndArrangeAndGetIdListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 相關的 Tag Id List
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.CreateNewPageItemLogicCheck.CheckCategory">
            <summary>
            檢查有没有輸入分類, 要排除 首頁
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.CreateNewPageItemLogicCheck.CheckNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查名稱
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.CreateNewPageItemLogicCheck.CheckAndArrangeParentAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查並整理 ParentId
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.ResetOperationgHourLogic.ExecuteAsync">
            <summary>
            執行
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.ResetOperationgHourLogic.ResetOperationgHourLogicCheck.CheckTagIdAsync">
            <summary>
            檢查測點 ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UpdatePageItemLogicCheck.CheckAndArrangeAndGetIdListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 相關的 Tag Id List
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UpdatePageItemLogicCheck.CheckCategory(Oco.Product_002.Model.DataBase.TableModels.PageSystem)">
            <summary>
            檢查種類
            </summary>
            <param name="targetPage">要異動資料的項目</param>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UpdatePageItemLogicCheck.CheckItemIdAndGetTargetPageAsync(System.Linq.IQueryable{Oco.Product_002.Model.DataBase.TableModels.PageSystem})">
            <summary>
            檢查ID 並取得 頁面
            </summary>
            <param name="pageSystems">Customer 的頁面系統</param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UpdatePageItemLogicCheck.CheckNameAsync(System.Linq.IQueryable{Oco.Product_002.Model.DataBase.TableModels.PageSystem},Oco.Product_002.Model.DataBase.TableModels.PageSystem)">
            <summary>
            檢查名稱
            </summary>
            <param name="customerPageSystems">Customer 的頁面系統</param>
            <param name="targetpage">要修改的頁面</param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UploadImportTagFileLogic.UploadImportTagFileLogicCheck.CheckFileExtensionName">
            <summary>
            檢查副檔名
            </summary>
            <exception cref="T:Oco.Core.ErrException.IllegalExtensionNameException">
            上傳的檔案副檔名如果不是預定的副檔名, 就會拋出這個例外
            </exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.SystemLogic.UploadImportTagFileLogic.UploadImportTagFileLogicCheck.CheckFileSize">
            <summary>
            檢查檔案大小
            </summary>
            <exception cref="T:Oco.Core.ErrException.EmptyImportTagFileException">
            上傳的檔案名如果為空內容, 就會拋出這個例外
            </exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewDeviceCategoryLogicCheck.CheckParentIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查父層ID
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewDeviceCategoryLogicCheck.CheckNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 Region Name
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.CheckAsync">
            <summary>
            檢查
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.GetNonExistIdListList">
            <summary>
            <para>取到尚未建立ID清單</para>
            <para>item1:群組分類ID, item2:測點ID,item3:群組ID</para>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.CheckGroupNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查名稱 
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.CheckDescription">
            <summary>
            檢查說明
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.CheckRegionIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            <para>Region ID 是否已經建立</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.GetErrGroupCategoryIdListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            <para>檢查是否設定群組分類ID 集合</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.GetErrTagIdListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            <para>檢查設定的測點ID 清單, 是否每個 ID 都已建立</para>
            <para>如没設定, 就不檢查</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewGroupLogicCheck.GetErrGroupIdListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            <para>檢查設定的群組ID 清單, 是否每個 ID 都已建立</para>
            <para>如没設定, 就不檢查</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateRegionLogicCheck.CheckParentIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查父層ID
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateRegionLogicCheck.CheckNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 Region Name
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagCategoryLogicCheck.CheckParentAsync">
            <summary>
            檢查父層
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagCategoryLogicCheck.CheckNameAsync">
            <summary>
            檢查名稱
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagChannelLogic.CreateTagChannelInstance(System.DateTime)">
            <summary>
            產生一個 TagChannel 的實體
            </summary>
            <param name="thisTime"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagChannelLogicCheck.CheckTagChannelNameWithThrowAsync(Microsoft.EntityFrameworkCore.DbSet{Oco.Product_002.Model.DataBase.TableModels.TagChannel})">
            <summary>
            <para>檢查Tag Name 是否重複已經填寫?</para>
            <para>檢查Tag Name 是否在同一個 Customer 下重複</para>
            </summary>
            <param name="dbSet">TagChannel資料表的 DbSet</param>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagChannelLogicCheck.ArrangeSettingProperties">
            <summary>
            整理設定的 屬性
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagChannelLogicCheck.CheckStatus">
            <summary>
            檢查使用狀態
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagLogic.InstallTagAndMessageGroupRelationAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid)">
            <summary>
            建立Tag 與通知群組關係
            </summary>
            <param name="db"></param>
            <param name="newTagId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagLogic.InstallTagAndCCTVRelationAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,System.Collections.Generic.List{System.Guid})">
            <summary>
            建立 Tag 與 CCTV 關
            </summary>
            <param name="db"></param>
            <param name="tagId"></param>
            <param name="cctvList"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.CreateNewTagLogicCheck.CheckAllowedTagNumbersAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查允許使用的測點數量
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteDeviceLogicCheck.CheckAsync">
            <summary>
            檢查
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupCategoryLogicCheck.CheckIdAsync">
            <summary>
            檢查群組分類格式
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupCategoryLogicCheck.CheckGroupRelationAsync">
            <summary>
            群組分類仍有與群組的關係
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupCategoryLogicCheck.CheckIfHasChildAsync">
            <summary>
            群組分類下是否還有群組分類?
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupLogicCheck.CheckIdAsync">
            <summary>
            檢查 Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupLogicCheck.CheckIfHasTagAsync">
            <summary>
            檢查 是否還有Tag
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteGroupLogicCheck.CheckIfHasChildAsync">
            <summary>
            是否仍有子節點
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteTagLogic.UninstallTagCCtvRelationAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,System.Guid)">
            <summary>
            移除 Tag 與CCTV 關係
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <param name="tagId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.DeleteTagLogic.UnselectedDesigoccTagFromTableAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,System.Guid)">
            <summary>
            將 Desigocc Tag 設為非選定的狀態
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <param name="tagId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.GetGroupHierarchyListLogic.GetRegionHierarchyListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            取得地區架構列表 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.GetGroupHierarchyListLogic.GetGroupCategoryHierarchyListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            取得群組分類架構
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="F:Oco.Product_002.Api.BusinessLogic.TagLogic.GetTagChannelListLogic.IsGetAllChannel">
            <summary>
            是否取得所有Customer的 TagChannel?
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.GetTagChannelListLogic.GetSetting">
            <summary>
            取得 設定資料的物件
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.GetTagListLogic.PredictOfThisTagAlarmRule(Oco.Product_002.Model.DataBase.TableModels.ViewTagChannelDeviceTagAlarmRule,System.Guid)">
            <summary>
             判斷某個警報規則是否屬於某個測點的規則 
            </summary>
            <param name="rule">警報規則</param>
            <param name="tagId">測點ID</param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback`1">
            <summary>
            檢查列舉(相對整數)的 Call Back
            <para>有没有輸入?</para>
            <para>代碼是否在範圍之內</para>
            </summary>
            <typeparam name="Tenum">列舉型別</typeparam>
            <param name="eV">列舉的整數值</param>
            <param name="nullText">未設定時的列外文字</param>
            <param name="outOfRangeText">設定值超出範圍時的列外文字</param>
        </member>
        <member name="F:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.targetDevice">
            <summary>
            依新的DeviceId 找出的 Device
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckRelatedPageIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查關連頁面 ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAlarmExceptionStatus(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Alarm.EnumAlarmExceptionUntil},Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Alarm.EnumAlarmExceptionAction})">
            <summary>
            警報例外
            <para>適用對象: 類比測點及數位測點</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckDigitalNormalStatus(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Alarm.EnumDigitalAlarmValue})">
            <summary>
            檢查是否啟用數位正常警報
            <para>適用對象: 數位測點</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckDigitalAlarmStatus(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Alarm.EnumDigitalAlarmValue})">
            <summary>
            檢查是否啟用數位警報
            適用對象: 數位測點
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAnalogLLAlarmStatus">
            <summary>
             檢查是否啟用LL警報
             <para>
             適用測點:類比測點
             </para>
             </summary>
             <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
             <remarks>LL說明不檢查 </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAnalogLOAlarmStatus">
            <summary>
            檢查是否啟用LO警報
            <para>適用測點:類比測點</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
            <remarks>LO說明不檢查 </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAnalogHIAlarmStatus">
            <summary>
            檢查是否啟用HI警報
            <para>
            適用測點: 類比測點
            </para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
            <remarks>HI警報值及HI說明不檢查</remarks>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAnalogHHAlarmStatus">
            <summary>
            檢查是否啟用HH警報
            <para>適用測點:類比測點</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
            <remarks>HH說明不檢查 </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckIsAlarmAudio">
            <summary>
            檢查是否播放警報語音
            <para>適用狀態: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAlarmStatus(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Alarm.EnumAlarmStatus})">
            <summary>
            檢查警報狀態種類 (不用/一般/重要)
            <para>
            適用狀態: 全部測點(數位/類比/Desigocc 匯入,......)
            </para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.ArrangeAlarmNotifyGroupList">
            <summary>
            整埋警報通知群組(格式不合者移除)
            <para>
            適用對象: 全部測點(數位/類比/Desigocc 匯入,......)
            </para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckDataInterval">
            <summary>
            檢查取值間隔時間
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckSaveHistory(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Tag.EnumTagValueLogIntervalType})">
            <summary>
            檢查儲存或不儲存歷史資料
            <para>
            要儲存歷史資料時，請設定歷史資料儲存間隔模式
            <para>
            以固定分鐘環方式記錄歷史資料時，檢查儲存間隔時間必須小於60分鐘
            </para>
            </para>
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckTagSaveType(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Tag.EnumTagValueSaveType})">
            <summary>
            測點存取權限
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="callBack"></param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckTagType(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Tag.EnumTagType},System.Boolean)">
            <summary>
            檢查測點種類(類比, 數位)
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)  </para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckRetentive">
            <summary>
            檢查保持值
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckIgnoreThreshold">
            <summary>
            檢查忽略閾值
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckIntialValue">
            <summary>
            檢查初始值
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckTagValueDataType(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Tag.EnumTagValueDataType})">
            <summary>
            檢查資料型態(default, string, word,......)
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="callBack"></param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckMeasureUnit(Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckEnumCallback{Oco.Core.Tag.EnumTagValueUnit})">
            <summary>
            檢查測量單位
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="callBack"></param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckAndGetTagCategoryIdList(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 測點分類 回傳整理後的 測點分類Id 集合 
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckValueAddress">
            <summary>
            檢查PLC 位址
            <para>是否有設定位址</para>
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckDescription">
            <summary>
            檢查Description
            <para>是否有輸入說明</para>
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckSimpleTagNameAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 Simple Tag Name
            <para>是否有輸入名稱?</para>
            <para>和Device 組合的名稱是否已經建立了 </para>
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckDeviceIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查 Device Id
            <para>Device Id 格式是否正確?</para>
            <para>Device Id 是否已經建立?</para>
            <para>適用對象: 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.ArrangeCCTVListAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            整理 Request 的 CCTV 清單
            <para>適用對象： 全部測點(數位/類比/Desigocc 匯入,......)</para>
            </summary>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckRegionIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            檢查Region Id 
            <para>Region Id 格式是否正確?</para>
            <para>Region Id 是否已經建立?</para>
            <para>
            檢查對象:類比測點, 數位測點, DesigoCC Import
            </para>
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckTagIdAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader)">
            <summary>
            1. 檢查否設定測點ID
            2. 檢查測點Id是否已建立
            <para>檢查對象: 所有測點(類比/數位/Desigocc 匯入,......) </para>
            </summary>
            <param name="db"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.ModifyTagUtil.CheckStatus">
            <summary>
            檢查啟用狀態
            </summary>
            <param name="status"></param>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateDeviceCategoryLogicCheck.CheckDeviceCategoryId">
            <summary>
            檢查設定的 Device 類別Id
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupCategoryLogicCheck.CheckId">
            <summary>
            檢查ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.GetNonExistIdList">
            <summary>
            取得未建立的 ID 清單
            <para>Item1: 群組分類ID 集合, Item2:測點Id 集合, Item3:群組ID 集合 </para>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.CheckIdAsync">
            <summary>
            檢查 Group ID
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.CheckNameAsync">
            <summary>
            檢查群組名稱
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.CheckRegionIdAsync">
            <summary>
            檢查Region ID 格式是否正常？ 
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.GetNonExistGroupCategoryIdListAsync">
            <summary>
            取得尚未建立的群組分類清單
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>        
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.GetNonExistTagIdListAsync">
            <summary>
            取得尚未建立的 Tag Id 集合 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.GetNonExistGroupIdListAsync">
            <summary>
            取得尚未建立的群組ID 集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.ParseGroupLinkPathAsync">
            <summary>
            剖析群組的路徑路否造成無窮
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateGroupLogicCheck.ParseGroupLinkPathExecute(System.Collections.Generic.List{Oco.Product_002.Model.DataBase.TableModels.ViewGroup},System.Guid,System.Guid,System.Guid)">
            <summary>
            開始剖析
            </summary>
            <param name="dataSource">被剖析的關連資料</param>
            <param name="childId">子群組ID</param>
            <param name="targetId">目標群組ID</param>
            <param name="testId">要測試的 ID</param>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateTagCategoryLogicCheck.CheckIdAsync">
            <summary>
            檢查ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateTagChannelLogicCheck.CheckChannel(System.Collections.Generic.List{Oco.Product_002.Model.DataBase.TableModels.TagChannel})">
            <summary>
            檢查 Channdl Id 在不在?
            檢查Channel Name 是否重複
            </summary>
            <param name="channelList">同一各戶的 CHANNEL 清單</param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateTagChannelLogicCheck.CheckTagChannelDriver(System.Collections.Generic.List{Oco.Product_002.Model.DataBase.TableModels.TagChannel})">
            <summary> 
            檢查 Tag Channel Driver
            </summary>
            <param name="tagChannelList"></param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.TagLogic.UpdateTagChannelLogicCheck.ArrangeSettingProperties">
            <summary>
            整理設定的 屬性
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.Types.BaseLogic`2.CheckEnumAndThrow``1(System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            檢查整數是否有值、可否轉成為指定的列舉型態, 如果無法轉換, 會丟出例外
            </summary>
            <typeparam name="TEnum">指定的列舉型態</typeparam>
            <param name="eV">整數</param>
            <param name="nullText">未輸入指定整數時的列外訊息</param>
            <param name="outOfRangeText">指定的整數未在列舉範圍內的列外訊息</param>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.Types.BaseLogic`2.ArrangeIPV4AndThrow(System.String,System.String)">
            <summary>
            整理 並檢查 Ip 位址 (V4),
            如IP 格式不合法會先丟出例外
            </summary>
            <param name="ipv4"></param>
            <param name="errMessage"></param>
            <returns></returns>
            <exception cref="T:Oco.Core.ErrException.RequestParameterErrorException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.Types.BaseLogic`2.PredictByTagId(System.Guid,System.Guid)">
            <summary>
            
            </summary>
            <param name="pTagId"></param>
            <param name="originalTagId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.Types.BaseLogic`2.UpdateTagModifyFlagTimeAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.DateTime,System.Boolean,System.Boolean)">
            <summary>
            更新測點異動管制資
            </summary>
            <param name="dbContext">資料庫</param>
            <param name="time">異動時間</param>
            <param name="onlyTag">更新了測點</param>
            <param name="onlyRelated">更新了相關資料</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.BusinessLogic.Types.BaseLogic`2.ArrangeChgeckIdListAndGetExpectIdList(System.Collections.Generic.List{System.String}@,System.Collections.Generic.List{System.Guid})">
            <summary>
            整理 ID, 回傳差集(A-B)
            </summary>
            <param name="rawIdList_A">集合 Ａ</param>
            <param name="baseIdList_B">集合B</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.AlarmController.GetAlarmHistoryParameter(Oco.Product_002.Api.Poco.Alarm.GetAlarmHistoryParameterReq)">
            <summary>
            取得查詢警報資料時的參數
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.AlarmController.AcknowledgeAlarm(Oco.Product_002.Api.Poco.Alarm.AcknowledgeAlarmReq)">
            <summary>
            確認警報Summary
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.AppController.GetTagValueListAsync(Oco.Product_002.Api.Poco.App.GetTagValueListReq)">
            <summary>
            由APPLICATION 呼叫, 取得所有測點現值
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.AppController.SetModbusTargetValue(Oco.Product_002.Api.Poco.App.SetModbusTargetValueReq)">
            <summary>
            由Application 呼叫, 設定點值目標
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.DesigoCcController.ToLoadTagList(Oco.Product_002.Api.Poco.DesigoCc.ToLoadTagListReq)">
            <summary>
            下載 測點資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.DesigoCcController.UpdateDesigoCcConfig(Oco.Product_002.Api.Poco.DesigoCc.UpdateDesigoCcConfigReq)">
            <summary>
            變更CcConfig 資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.DesigoCcController.CreateNewDesigoCcConfig(Oco.Product_002.Api.Poco.DesigoCc.CreateNewDesigoCcConfigReq)">
            <summary>
            產生新的CcConfig
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.DesigoCcController.DeleteDesigoCcConfig(Oco.Product_002.Api.Poco.DesigoCc.DeleteDesigoCcConfigReq)">
            <summary>
            刪除指定的 Config 檔案
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.DesigoCcController.GetDesigoCcConfigList(Oco.Product_002.Api.Poco.DesigoCc.GetDesigoCcConfigListReq)">
            <summary>
            取得 DecigoCC 組態清單列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityContractSettingController.GetElectricityContractSettingsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetElectricityContractSettingsQuery,Oco.Core.Repository.ElectricityContractSettingInfo})">
            <summary>
                取得電力契約設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityContractSettingController.CreateElectricityContractSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateElectricityContractSettingCommand},Oco.Product_002.Api.Poco.ElectricityContractSetting.CreateElectricityContractSettingReq)">
            <summary>
                新增電力契約設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityContractSettingController.UpdateElectricityContractSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateElectricityContractSettingCommand},System.Guid,Oco.Product_002.Api.Poco.ElectricityContractSetting.UpdateElectricityContractSettingReq)">
            <summary>
                修改電力契約設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityContractSettingController.DeleteElectricityContractSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteElectricityContractSettingCommand},System.Guid)">
            <summary>
            刪除電力契約設定
            </summary>
            <param name="commandService"></param>
            <param name="contractSettingId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.QueryElectricityRateBasicSettingsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetElectricityRateBasicSettingsQuery,Oco.Core.Repository.ElectricityRateBasicSettingInfo})">
            <summary>
                查詢電費費率基本設定
            </summary>
            <param name="queryService">summary query service</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.QueryElectricityTypeTimeSettingsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetElectricityTypeTimeSettingsQuery,Oco.Core.Repository.ElectricityTypeTimeSettingInfo},Oco.Core.ElectricityRate.EnumPowerSupplyType,Oco.Core.ElectricityRate.EnumTimeOfUseSegment)">
            <summary>
                查詢電費費率年月設定 (依供電型式 / 時間電價種類)
            </summary>
            <param name="queryService">summary query service</param>
            <param name="powerSupplyType">power supply type</param>
            <param name="segment">time of use segment</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.QueryElectricityRateDetailsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetElectricityRateDetailsQuery,Oco.Core.Repository.ElectricityRateDetailInfo},System.Guid)">
            <summary>
                查詢電費費率明細
            </summary>
            <param name="queryService">summary query service</param>
            <param name="rateId">electricity rate id</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.QueryElectricityRateDetailsWithSpecificTypeAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.GetElectricityRateDetailsWithSpecificTypeQuery,Oco.Core.Services.ElectricityRateDetailWithTimeSettingInfo},Oco.Core.ElectricityRate.EnumPowerSupplyType,Oco.Core.ElectricityRate.EnumTimeOfUseSegment,Oco.Product_002.Api.Poco.ElectricityRate.QueryElectricityRateDetailsWithSpecificTypeReq)">
            <summary>
                查詢電費費率明細 (依供電型式 / 時間電價種類)
            </summary>
            <param name="queryService">summary query service</param>
            <param name="powerSupplyType">power supply type</param>
            <param name="segment">time of use segment</param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.CreateElectricityRateDetailAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateElectricityRateDetailCommand},System.Guid,Oco.Product_002.Api.Poco.ElectricityRate.CreateElectricityRateDetailReq)">
            <summary>
                新增電費費率明細
            </summary>
            <param name="commandService">create detail command service</param>
            <param name="rateId">electricity rate id</param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.CreateElectricityRateDetailsWithSpecificTypeAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateElectricityRateDetailsWithSpecificTypeCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.ElectricityRate.CreateElectricityRateDetailsWithSpecificTypeReq},Oco.Core.ElectricityRate.EnumPowerSupplyType,Oco.Core.ElectricityRate.EnumTimeOfUseSegment,Oco.Product_002.Api.Poco.ElectricityRate.CreateElectricityRateDetailsWithSpecificTypeReq)">
            <summary>
                新增電費費率明細 (依供電型式 / 時間電價種類)
            </summary>
            <param name="commandService">create details command service</param>
            <param name="validator">validator</param>
            <param name="powerSupplyType">power supply type</param>
            <param name="segment">time of use segment</param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.UpdateElectricityRateDetailAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateElectricityRateDetailCommand},System.Guid,System.Int16,System.Int16,Oco.Product_002.Api.Poco.ElectricityRate.UpdateElectricityRateDetailReq)">
            <summary>
                修改電費費率明細
            </summary>
            <param name="commandService">update detail command service</param>
            <param name="rateId">electricity rate id</param>
            <param name="year"></param>
            <param name="month"></param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.UpdateElectricityRateDetailsWithSpecificTypeAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateElectricityRateDetailsWithSpecificTypeCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.ElectricityRate.UpdateElectricityRateDetailsWithSpecificTypeReq},Oco.Core.ElectricityRate.EnumPowerSupplyType,Oco.Core.ElectricityRate.EnumTimeOfUseSegment,Oco.Product_002.Api.Poco.ElectricityRate.UpdateElectricityRateDetailsWithSpecificTypeReq)">
            <summary>
                修改電費費率明細 (依供電型式 / 時間電價種類)
            </summary>
            <param name="commandService">update details command service</param>
            <param name="validator">validator</param>
            <param name="powerSupplyType">power supply type</param>
            <param name="segment">time of use segment</param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.DeleteElectricityRateDetailAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteElectricityRateDetailCommand},System.Guid,System.Int16,System.Int16)">
            <summary>
                刪除電費費率明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricityRateController.DeleteElectricityRateDetailsWithSpecificTypeAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteElectricityRateDetailsWithSpecificTypeCommand},Oco.Core.ElectricityRate.EnumPowerSupplyType,Oco.Core.ElectricityRate.EnumTimeOfUseSegment,Oco.Product_002.Api.Poco.ElectricityRate.DeleteElectricityRateDetailsWithSpecificTypeReq)">
            <summary>
                刪除電費費率明細 (依供電型式 / 時間電價種類)
            </summary>
            <param name="commandService">delete details command service</param>
            <param name="powerSupplyType">power supply type</param>
            <param name="segment">time of use segment</param>
            <param name="request">request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.CreateElectricPowerUnloadSummary(Oco.Product_002.Api.Poco.ElectricPowerUnload.CreateElectricPowerUnloadSummaryReq)">
            <summary>
            產生一個 電力加/卸載摘要
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.EditElectricPowerUnloadSummary(Oco.Product_002.Api.Poco.ElectricPowerUnload.EditElectricPowerUnloadSummaryReq)">
            <summary>
            編輯電力卸載/加載 摘要
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.DeleteElectricPowerUnloadSummary(Oco.Product_002.Api.Poco.ElectricPowerUnload.DeleteElectricPowerUnloadSummaryReq)">
            <summary>
            移除一個 電力卸載 摘要
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.GetElectricPowerUnloadSummaryList">
            <summary>
            取得電力卸載/加載摘要及附加資料
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.EditElectricPowerConsumableTagForUnload(Oco.Product_002.Api.Poco.ElectricPowerUnload.EditElectricPowerConsumableTagForUnloadReq)">
            <summary>
            編輯每個電力卸載/加載摘要 計算電力消耗的測點
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.GetElectricPowerConsumableTagForUnloadList(Oco.Product_002.Api.Poco.ElectricPowerUnload.GetElectricPowerConsumableTagForUnloadListReq)">
            <summary>
            取得可以作為電力卸載/加載 計算電力消耗的測點集合
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.EditElectricPowerUnloadStageGroup(Oco.Product_002.Api.Poco.ElectricPowerUnload.EditElectricPowerUnloadStageGroupReq)">
            <summary>
            電力卸載/加載 編輯電力卸載/加載 群組(新增/刪除/修改)
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.GetElectricPowerUnloadStageGroupDetailList(Oco.Product_002.Api.Poco.ElectricPowerUnload.GetElectricPowerUnloadStageGroupDetailListReq)">
            <summary>
            取得各階段電力卸載/加載 群組明細列表(含測點)
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ElectricPowerUnloadController.EditElectricPowerUnloadTag(Oco.Product_002.Api.Poco.ElectricPowerUnload.EditElectricPowerUnloadTagReq)">
            <summary>
            編輯電力 卸載/加載 各階段測點
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.FrontendSettingController.GetFrontendSettingsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetFrontendSettingsQuery,Oco.Core.Repository.FrontendSettingInfo},System.String)">
            <summary>
                取得前端設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.FrontendSettingController.CreateFrontendSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateFrontendSettingCommand},System.String,Oco.Product_002.Api.Poco.FrontendSetting.CreateFrontendSettingsReq)">
            <summary>
                新增前端設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.FrontendSettingController.UpdateFrontendSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateFrontendSettingCommand},System.String,System.Guid,Oco.Product_002.Api.Poco.FrontendSetting.UpdateFrontendSettingsReq)">
            <summary>
                更新前端設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.FrontendSettingController.DeleteFrontendSettingAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteFrontendSettingCommand},System.String,System.Guid)">
            <summary>
                刪除前端設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportCombineStatisticSummariesAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetTagStatisticSummariesByReportTypeQuery,Oco.Core.Services.TagStatisticSummaryDetailItem},Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterElectricityFeeSummariesQuery,Oco.Core.Services.ElectricityFeeSummaryInfo},Oco.Core.Services.IDataExporter,Oco.Core.HistoryReport.EnumHistoryReportType,Oco.Product_002.Api.Poco.HistoryReport.QueryCombineStatisticSummariesReq)">
            <summary>
                匯出歷史/電費合併報表-報表類型(年月日)
            </summary>
            <param name="tagStatisticSummariesQueryService"></param>
            <param name="meterElectricityFeeSummariesQueryService"></param>
            <param name="exporter">data exporter</param>
            <param name="reportType">report type</param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.QueryTagStatisticSummariesWithReportTypeAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetTagStatisticSummariesByReportTypeQuery,Oco.Core.Services.TagStatisticSummaryDetailItem},Oco.Core.HistoryReport.EnumHistoryReportType,Oco.Product_002.Api.Poco.HistoryReport.QueryTagStatisticSummariesWithReportTypeReq)">
            <summary>
                查詢歷史報表-報表類型(年月日)
            </summary>
            <param name="queryService">summary query service</param>
            <param name="reportType">history report type</param>
            <param name="request">input request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportTagStatisticSummariesWithReportTypeAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetTagStatisticSummariesByReportTypeQuery,Oco.Core.Services.TagStatisticSummaryDetailItem},Oco.Core.Services.IDataExporter,Oco.Core.HistoryReport.EnumHistoryReportType,Oco.Product_002.Api.Poco.HistoryReport.ExportTagStatisticSummariesWithReportTypeReq)">
            <summary>
                匯出歷史報表-報表類型(年月日)
            </summary>
            <param name="queryService">summary query service</param>
            <param name="exporter">data exporter</param>
            <param name="reportType">history report type</param>
            <param name="request">input request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.QueryTagStatisticSummariesWithDetailAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetTagStatisticSummariesWithDetailTypeQuery,Oco.Core.Services.TagStatisticSummaryDetailItem},Oco.Product_002.Api.Poco.HistoryReport.QueryTagStatisticSummariesWithDetailReq)">
            <summary>
                查詢歷史報表-明細
            </summary>
            <param name="queryService">histories query service</param>
            <param name="request">input request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportTagStatisticSummariesWithDetailAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetTagStatisticSummariesWithDetailTypeQuery,Oco.Core.Services.TagStatisticSummaryDetailItem},Oco.Core.Services.IDataExporter,Oco.Product_002.Api.Poco.HistoryReport.ExportTagStatisticSummariesWithDetailReq)">
            <summary>
                匯出歷史報表-明細
            </summary>
            <param name="queryService">histories query service</param>
            <param name="exporter"></param>
            <param name="request">input request</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.QueryMeterStatisticVariationSummariesAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterStatisticVariationSummariesQuery,Oco.Core.Services.MeterStatisticVariationSummaryInfo},Oco.Core.Meter.EnumMeterType,Oco.Product_002.Api.Poco.HistoryReport.QueryMeterStatisticVariationSummariesReq)">
            <summary>
                查詢儀表變化量報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportMeterStatisticVariationSummariesAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterStatisticVariationSummariesQuery,Oco.Core.Services.MeterStatisticVariationSummaryInfo},Oco.Core.Services.IDataExporter,Oco.Core.Meter.EnumMeterType,Oco.Product_002.Api.Poco.HistoryReport.ExportMeterStatisticVariationSummariesReq)">
            <summary>
                匯出儀表變化量報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.QueryMeterElectricityFeeSummariesAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterElectricityFeeSummariesQuery,Oco.Core.Services.ElectricityFeeSummaryInfo},Oco.Product_002.Api.Poco.HistoryReport.QueryMeterElectricityFeeSummariesReq)">
            <summary>
                查詢電費報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportMeterElectricityFeeSummariesAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterElectricityFeeSummariesQuery,Oco.Core.Services.ElectricityFeeSummaryInfo},Microsoft.Extensions.Options.IOptions{Oco.Core.Services.TaxOptions},Oco.Core.Services.IDataExporter,Oco.Product_002.Api.Poco.HistoryReport.ExportMeterElectricityFeeSummariesReq)">
            <summary>
                匯出電費報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.QueryMeterWaterFeeSummariesAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterWaterFeeSummariesQuery,Oco.Core.Services.WaterFeeSummaryInfo},Oco.Product_002.Api.Poco.HistoryReport.QueryMeterWaterFeeSummariesReq)">
            <summary>
                查詢水費報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HistoryReportController.ExportMeterWaterFeeSummariesAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.HistoryReport.GetMeterWaterFeeSummariesQuery,Oco.Core.Services.WaterFeeSummaryInfo},Oco.Core.Services.IDataExporter,Oco.Product_002.Api.Poco.HistoryReport.ExportMeterWaterFeeSummariesReq)">
            <summary>
                匯出水費報表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HolidayController.GetHolidaysAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetHolidaysQuery,Oco.Core.Repository.HolidayInfo})">
            <summary>
                取得所有假日列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HolidayController.ImportHolidaysAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.HolidayFileReaderQuery,System.DateOnly},Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpsertHolidaysCommand},System.Int32,Oco.Product_002.Api.Poco.Holiday.ImportHolidaysReq)">
            <summary>
                匯入假日列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.HolidayController.ExportHolidaysAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetHolidaysQuery,Oco.Core.Repository.HolidayInfo},Oco.Core.Services.IDataExporter,System.Int32)">
            <summary>
                匯出假日列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.IdentityController.RefreshToken(Oco.Product_002.Api.Poco.Identity.RefreshTokenReq)">
            <summary>
            更新 Fresh Token
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MediaController.GetScadaIconList(Oco.Product_002.Api.Poco.Media.GetScadaIconListReq)">
            <summary>
            取得所有圖檔列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MediaController.CreateNewScadaIcon(Oco.Product_002.Api.Poco.Media.CreateNewScadaIconReq)">
            <summary>
            產生一個新的圖檔
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.DeleteLineService(Oco.Product_002.Api.Poco.Message.DeleteLineServiceReq)">
             <summary>
            刪除 Line 聊天室資料
             </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.UpdateLineService(Oco.Product_002.Api.Poco.Message.UpdateLineServiceReq)">
             <summary>
            更新(編輯) Line 聊天室資料
             </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.GetLineServiceList(Oco.Product_002.Api.Poco.Message.GetLineServiceListReq)">
            <summary>
            取得Line 聊天室清單
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.CreateNewLineService(Oco.Product_002.Api.Poco.Message.CreateNewLineServiceReq)">
            <summary>
            新增一組聊天室
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.CreateLineNotifyAccessToken(Oco.Product_002.Api.Poco.Message.CreateLineNotifyAccessTokenReq)">
            <summary>
            取得 Line Notify Accesstoken
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.GetMessageHitoryList(Oco.Product_002.Api.Poco.Message.GetMessageHitoryListReq)">
            <summary>
            取得訊息歷史資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.GetMessageGroupDetailList(Oco.Product_002.Api.Poco.Message.GetMessageGroupDetailListReq)">
            <summary>
            取得訊息群組明細資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.DeleteSmsGroup(Oco.Product_002.Api.Poco.Message.DeleteSmsGroupReq)">
            <summary>
            更新SMS 群組
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.UpdateSmsGroup(Oco.Product_002.Api.Poco.Message.UpdateSmsGroupReq)">
            <summary>
            更新SMS 群組
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MessageController.CreateNewSmsGroup(Oco.Product_002.Api.Poco.Message.CreateNewSmsGroupReq)">
            <summary>
            新增 SMS 及LINE群組
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MeterController.GetMetersAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetMetersQuery,Oco.Core.Repository.MeterInfo},Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetRegionsQuery,Oco.Core.Repository.RegionInfo},Oco.Core.Meter.EnumMeterType)">
            <summary>
                取得儀表設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MeterController.CreateMeterAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateMeterCommand},Oco.Product_002.Api.Poco.Meter.CreateMeterReq)">
            <summary>
                新增儀表設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MeterController.UpdateMeterAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateMeterCommand},System.Guid,Oco.Product_002.Api.Poco.Meter.UpdateMeterReq)">
            <summary>
                修改儀表設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.MeterController.DeleteMeterAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteMeterCommand},System.Guid)">
            <summary>
            刪除儀表設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ObixController.GetObixDeviceListAsync(Oco.Product_002.Api.Poco.Obix.GetObixDeviceListReq)">
            <summary>
                選用Obix 測點
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ObixController.SelectObixTagListAsync(Oco.Product_002.Api.Poco.Obix.SelectObixTagListReq)">
            <summary>
                選用Obix 測點
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ObixController.GetObixTagListAsync(Oco.Product_002.Api.Poco.Obix.GetObixTagListReq)">
            <summary>
                取得 Obix 測點資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ObixController.ToLoadObixTagListAsync(Oco.Product_002.Api.Poco.Obix.ToLoadObixTagListReq)">
            <summary>
                下載 測點資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ObixController.GetTagChannelDriverAsync(System.Guid)">
            <summary>
            依據DeviceId取得Channel 的驅動
            </summary>
            <param name="deviceId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.GetReliabilityAnalysisGroupFaultDetailAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.GetReliabilityAnalysisGroupFaultDetailQuery,Oco.Core.Services.TagFaultHistoryInfo},System.Guid)">
            <summary>
                取得特定可靠度群組的測點錯誤發生明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.GetReliabilityAnalysisGroupsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetReliabilityAnalysisGroupsQuery,Oco.Core.Services.ReliabilityAnalysisGroupItem})">
            <summary>
                取得可靠度分析群組列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.CreateReliabilityAnalysisGroupAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.CreateReliabilityAnalysisGroupCommand},Oco.Product_002.Api.Poco.ReliabilityAnalysis.CreateReliabilityAnalysisGroupReq)">
            <summary>
                新增分析群組
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.DeleteReliabilityAnalysisGroupAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.DeleteReliabilityAnalysisGroupCommand},System.Guid)">
            <summary>
                刪除指定的分析群組
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.UpdateReliabilityAnalysisGroupAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.UpdateReliabilityAnalysisGroupCommand},System.Guid,Oco.Product_002.Api.Poco.ReliabilityAnalysis.UpdateReliabilityAnalysisGroupReq)">
            <summary>
                編輯指定的系統
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ReliabilityAnalysisController.ResetReliabilityAnalysisGroupFaultRecordAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ResetReliabilityAnalysisGroupFaultHistoryCommand},System.Guid)">
            <summary>
                重置指定系統故障次數
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.RoleController.GetRolesAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.Role.GetRolesQuery,Oco.Core.Repository.RoleInfo})">
            <summary>
                取得角色列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.RoleController.GetRoleAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.Role.GetRoleQuery,Oco.Core.Services.RoleDetailInfo},System.Guid)">
            <summary>
                取得角色
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.RoleController.CreateRoleAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Role.CreateRoleCommand},Oco.Product_002.Api.Poco.Role.CreateRoleReq)">
            <summary>
                建立角色
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.RoleController.UpdateRoleAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Role.UpdateRoleCommand},System.Guid,Oco.Product_002.Api.Poco.Role.UpdateRoleReq)">
            <summary>
                更新角色
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.RoleController.DeleteRoleAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Role.DeleteRoleCommand},System.Guid)">
            <summary>
                刪除角色
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ScheduleJobController.GetScheduleJobsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.ScheduleJob.GetScheduleJobsQuery,Oco.Core.Repository.ScheduleJobInfo})">
            <summary>
                取得排程工作設定列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ScheduleJobController.GetScheduleJobAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.ScheduleJob.GetScheduleJobQuery,Oco.Core.Repository.ScheduleJobDetailInfo},System.Guid)">
            <summary>
                取得特定排程工作設定
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ScheduleJobController.CreateScheduleJobAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.ScheduleJob.CreateScheduleJobQuery,System.Guid},Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ScheduleJob.AddHangfireJobCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.ScheduleJob.CreateScheduleJobReq},Oco.Product_002.Api.Poco.ScheduleJob.CreateScheduleJobReq)">
            <summary>
                建立工作排程
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ScheduleJobController.UpdateScheduleJobAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ScheduleJob.UpdateScheduleJobCommand},Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ScheduleJob.UpdateHangfireJobCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.ScheduleJob.UpdateScheduleJobReq},System.Guid,Oco.Product_002.Api.Poco.ScheduleJob.UpdateScheduleJobReq)">
            <summary>
                更新工作排程
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ScheduleJobController.DeleteScheduleJobAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ScheduleJob.DeleteScheduleJobCommand},Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.ScheduleJob.DeleteHangfireJobCommand},System.Guid)">
            <summary>
                刪除工作排程
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.ServiceConnectionController.GetServiceSendRealtimeDataTimeAsync(Oco.Product_002.Api.Poco.ServiceConnection.GetServiceSendRealtimeDataTimeReq)">
            <summary>
            依 Request 傳來的Customer ID 與 Service Code, 回覆 對應的 Service 最後一次傳送即時資料的時間   
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.StaffLogin(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.Role.GetRoleQuery,Oco.Core.Services.RoleDetailInfo},Oco.Product_002.Api.Poco.Staff.StaffLoginReq)">
            <summary>
            操作人員 Login
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.GetStaffMembersAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.Staff.GetStaffMembersQuery,Oco.Core.Services.ApplicationServices.StaffMemberInfo})">
            <summary>
                取得人員列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.GetStaffAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.Staff.GetStaffQuery,Oco.Core.Repository.StaffInfo},System.Int32)">
            <summary>
                取得人員列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.CreateStaffAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Staff.CreateStaffCommand},Oco.Product_002.Api.Poco.Staff.CreateStaffReq)">
            <summary>
                建立人員
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.UpdateStaffAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Staff.UpdateStaffCommand},System.Int32,Oco.Product_002.Api.Poco.Staff.UpdateStaffReq)">
            <summary>
                更新人員
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.UpdateStaffStatusAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Staff.UpdateStaffStatusCommand},System.Int32,Oco.Core.Staff.EnumStaffState)">
            <summary>
                變更人員狀態
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.UpdateStaffPasswordAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Staff.UpdateStaffPasswordCommand},Oco.Product_002.Api.Poco.Staff.UpdateStaffPasswordRequest)">
            <summary>
                變更人員密碼 (User自行變更用)
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StaffController.ResetStaffPasswordAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.Staff.ResetStaffPasswordCommand},System.Int32,Oco.Product_002.Api.Poco.Staff.ResetStaffPasswordRequest)">
            <summary>
                重設人員密碼 (管理者變更用)
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StatisticController.GetRealTimeStaisticSummaryAsync(Oco.Product_002.Api.Poco.Stastic.GetRealTimeStaisticSummaryReq)">
            <summary>
            取得統計計算的即時值
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StatisticController.GetStatisticResultAsync(Oco.Product_002.Api.Poco.Stastic.GetStatisticResultReq)">
            <summary>
            取得統計報表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.StatisticController.GetStatisticParameterAsync(Oco.Product_002.Api.Poco.Stastic.GetStatisticParameterReq)">
            <summary>
            取得統計計算的參數 
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.UpdatePageItem(Oco.Product_002.Api.Poco.System.UpdatePageItemReq)">
            <summary>
                系統~更新頁面資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.DeletePageItem(Oco.Product_002.Api.Poco.System.DeletePageItemReq)">
            <summary>
                刪除頁面項目
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.CreateNewPageItem(Oco.Product_002.Api.Poco.System.CreateNewPageItemReq)">
            <summary>
                新增目錄或頁面
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetPageItem(Oco.Product_002.Api.Poco.System.GetPageItemReq)">
            <summary>
                取得指定頁面內容
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.ModifyFirstPage(Oco.Product_002.Api.Poco.System.ModifyFirstPageReq)">
            <summary>
                更新首頁資料
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetFirstPageItemContent(Oco.Product_002.Api.Poco.System.GetFirstPageItemContentReq)">
            <summary>
                取得指定頁面內容
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetPageItemHierarchicalList(Oco.Product_002.Api.Poco.System.GetPageItemHierarchicalListReq)">
            <summary>
                取得頁面架構
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetOperatingHourListAsync(FluentValidation.IValidator{Oco.Product_002.Api.Validators.EquipmentOperatingHour.GetEquipmentOperatingHourListReqValidatorModel},Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.EquipmentOperatingHour.GetEquipmentOperatingHourListQuery,Oco.Core.Services.EquipmentOperatingHourInfo},Oco.Product_002.Api.Poco.System.GetEquipmentOperatingHourListReq)">
            <summary>
                取得系統運轉時數 集合
            </summary>
            <returns>
            </returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.ResetOperationgHour(Oco.Product_002.Api.Poco.System.ResetOperationgHourReq)">
            <summary>
                重置指定測點運轉時數
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.UploadImportTagFile(Oco.Product_002.Api.Poco.System.UploadImportTagFileReq)">
            <summary>
            上傳匯入測點檔案 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetImportTagFileState(Oco.Product_002.Api.Poco.System.GetImportTagFileStateReq)">
            <summary>
            查詢匯入檔案處理狀態
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.RequestTagExport(Oco.Product_002.Api.Poco.System.RequestTagExportReq)">
            <summary>
            提出匯出測點需求
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.SystemController.GetRequestExportTagState(Oco.Product_002.Api.Poco.System.GetRequestExportTagStateReq)">
            <summary>
            列出匯出測點清
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewGroupCategoryAsync(Oco.Product_002.Api.Poco.Tag.CreateNewGroupCategoryReq)">
            <summary>
                新增群組分類
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateGroupCategoryAsync(Oco.Product_002.Api.Poco.Tag.UpdateGroupCategoryReq)">
            <summary>
                更新群組分類
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetGroupCategoryHierarchyListAsync(Oco.Product_002.Api.Poco.Tag.GetGroupCategoryHierarchyListReq)">
            <summary>
                取得群組分類架構列表集合
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteGroupCategoryAsync(Oco.Product_002.Api.Poco.Tag.DeleteGroupCategoryReq)">
            <summary>
                刪除指定的群組
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewGroupAsync(Oco.Product_002.Api.Poco.Tag.CreateNewGroupReq)">
            <summary>
                新增新群組
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateGroupAsync(Oco.Product_002.Api.Poco.Tag.UpdateGroupReq)">
            <summary>
                修改群組名稱
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteGroupAsync(Oco.Product_002.Api.Poco.Tag.DeleteGroupReq)">
            <summary>
                刪除一個 Group
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Oco.Product_002.Api.Controllers.TagController.DeleteTagCategoryAsync(Oco.Product_002.Api.Poco.Tag.DeleteTagCategoryReq)" -->
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewDeviceCategoryAsync(Oco.Product_002.Api.Poco.Tag.CreateNewDeviceCategoryReq)">
            <summary>
                依Customer 新增Device 分類
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetDeviceCategoryHierarchyListAsync(Oco.Product_002.Api.Poco.Tag.GetDeviceCategoryHierarchyListReq)">
            <summary>
                取得Device 分類架構集合
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateDeviceCategoryAsync(Oco.Product_002.Api.Poco.Tag.UpdateDeviceCategoryReq)">
            <summary>
                依Customer 更新 Device Category資料
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteDeviceCategoryAsync(Oco.Product_002.Api.Poco.Tag.DeleteDeviceCategoryReq)">
            <summary>
                刪除  DeviceCategory
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewRegionAsync(Oco.Product_002.Api.Poco.Tag.CreateNewRegionReq)">
            <summary>
                依Customer 新增地區(Region)
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetRegionHierarchyListAsync(Oco.Product_002.Api.Poco.Tag.GetRegionHierarchyListReq)">
            <summary>
                依Customer 取出Region 的架構
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateRegionAsync(Oco.Product_002.Api.Poco.Tag.UpdateRegionReq)">
            <summary>
                依Customer 更新 Region
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteRegionAsync(Oco.Product_002.Api.Poco.Tag.DeleteRegionReq)">
            <summary>
                依Customer 刪除 Region
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteTagChannelAsync(Oco.Product_002.Api.Poco.Tag.DeleteTagChannelReq)">
            <summary>
                依Customer 刪除 Channel
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.SetTagChannelAbilityAsync(Oco.Product_002.Api.Poco.Tag.SetTagChannelAbilityReq)">
            <summary>
                依Customer 設定 TagCgannel 啟用/不用
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateTagChannelAsync(Oco.Product_002.Api.Poco.Tag.UpdateTagChannelReq)">
            <summary>
                依Customer, 更新 TagChannel
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewTagChannelAsync(Oco.Product_002.Api.Poco.Tag.CreateNewTagChannelReq)">
            <summary>
                依Customer 新增一個Channel
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetTagChannelListByCustomerAsync(Oco.Product_002.Api.Poco.Tag.GetTagChannelListReq)">
            <summary>
                取出指定 Customer 的 TagChannel列表
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetTagChannelListAsync(Oco.Product_002.Api.Poco.Tag.GetTagChannelListReq)">
            <summary>
                取出所有的 TagChannelList
                SystemAdmin:所有的
                CustomerAdmin:Customer 的
                User:Customer 的
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.DeleteDeviceAsync(Oco.Product_002.Api.Poco.Tag.DeleteDeviceReq)">
            <summary>
                在指定的 Customer/ TagChannel 下, 特定的 Device
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.UpdateDeviceAsync(Oco.Product_002.Api.Poco.Tag.UpdateDeviceReq)">
            <summary>
                指定的 Customer/TagChannel 更新 Device
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.SetTagDeviceAbilityAsync(Oco.Product_002.Api.Poco.Tag.SetTagDeviceAbilityReq)">
            <summary>
                於 指定的Customer/Channel  設定 Device 啟用/停用
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.GetDeviceListAsync(Oco.Product_002.Api.Poco.Tag.GetDeviceListReq)">
            <summary>
                在指定的 Customer/TagChannel 取得 Device 列表
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.TagController.CreateNewDeviceAsync(Oco.Product_002.Api.Poco.Tag.CreateNewDeviceReq)">
            <summary>
                在指定的 Customer/TagChannel 新增一 Device
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(``1,Oco.Core.ErrException.Types.EnumExceptionCode)">
            <summary>
                Status Code=200
            </summary>
            <typeparam name="TResp"></typeparam>
            <typeparam name="TRespDetail"></typeparam>
            <param name="detail"></param>
            <param name="exceptionCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(Oco.Core.ErrException.Types.BaseAuthorizeException)">
            <summary>
                Status Code=401
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(Oco.Core.ErrException.Types.BaseException)">
            <summary>
                Status Code=200
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(Oco.Core.ErrException.NormalException)">
            <summary>
                Status Code=200
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(Oco.Core.ErrException.RequestParameterErrorException,``1)">
            <summary>
                Status Code=200
            </summary>
            <typeparam name="TResp"></typeparam>
            <typeparam name="TRespDetail"></typeparam>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.Types.BaseController.GetResponse``2(System.Exception)">
            <summary>
                伺服端問題(執行時例外)
                Status Code=500
            </summary>
            <typeparam name="TResp"></typeparam>
            <typeparam name="TRespDetail"></typeparam>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.WaterRateController.QueryWaterRateTimeSettingsAsync(Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.WaterFeeRate.GetWaterFeeRateTimeSettingsQuery,Oco.Core.Repository.GenericTimeSettingInfo})">
            <summary>
                查詢水費費率年月設定
            </summary>
            <param name="queryService">summary query service</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.WaterRateController.QueryWaterRateTimeDetailsAsync(Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.WaterFeeRate.GetWaterFeeRateDetailsQuery,Oco.Core.Repository.WaterFeeRateDetailInfo},Oco.Product_002.Api.Poco.WaterRate.QueryWaterRateDetailsReq)">
            <summary>
                查詢水費費率明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.WaterRateController.CreateWaterRateDetailsAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.WaterFeeRate.CreateWaterFeeRateDetailsCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.WaterRate.CreateWaterRateDetailsReq},Oco.Product_002.Api.Poco.WaterRate.CreateWaterRateDetailsReq)">
            <summary>
                新增水費費率明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.WaterRateController.UpdateWaterRateDetailsAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.WaterFeeRate.UpdateWaterFeeRateDetailsCommand},FluentValidation.IValidator{Oco.Product_002.Api.Poco.WaterRate.UpdateWaterRateDetailsReq},Oco.Product_002.Api.Poco.WaterRate.UpdateWaterRateDetailsReq)">
            <summary>
                更新水費費率明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Controllers.WaterRateController.DeleteWaterRateDetailsAsync(Oco.ServiceUtility.ICommandService{Oco.Core.Services.ApplicationServices.WaterFeeRate.DeleteWaterFeeRateDetailsCommand},Oco.Product_002.Api.Poco.WaterRate.DeleteWaterRateDetailsReq)">
            <summary>
                刪除水費費率明細
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Filter.ActionCheckAccessTokenFilter.OnActionExecuting(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            
            </summary>
            <param name="actionContext"></param>
        </member>
        <member name="T:Oco.Product_002.Api.Filter.ActionCheckPermissionFilterAttribute">
            <summary>
            檢查使用權限 的Attribute,
            這必須掛在ActionCheckAccessTokenFilter之後 
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.Properties.Resources">
            <summary>
              用於查詢當地語系化字串等的強類型資源類別。
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.Properties.Resources.ResourceManager">
            <summary>
              傳回這個類別使用的快取的 ResourceManager 執行個體。
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.Properties.Resources.Culture">
            <summary>
              覆寫目前執行緒的 CurrentUICulture 屬性，對象是所有
              使用這個強類型資源類別的資源查閱。
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.AlarmSummaryTimeInfo.CustomerId">
            <summary>
            Customer Id 
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.AlarmSummaryTimeInfo.UpdateTime">
            <summary>
            警報摘要更新時間
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection">
            <summary>
            Connection 物件
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.CustomerId">
            <summary>
            用戶 Id
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.ServiceConnectionList">
            <summary>
            用戶的連線集合
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.GetConnection(Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            取得用戶特定連線的狀態
            </summary>
            <param name="connectionCategory">持定的連線</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.#ctor">
            <summary>
            建構式
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.#ctor(System.Guid)">
            <summary>
            建構式
            </summary>
            <param name="customerId">
            
            </param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.SetStatusToConnected(Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            將狀態設為連線
            </summary>
            <param name="category"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.SetStatusToDisconnected(Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            將狀態設為 斷線
            </summary>
            <param name="category"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.SetStatusToInitialing(Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            狀態設為初始化中
            </summary>
            <param name="category"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection.SetConnectStatus(Oco.Core.SignalR.EnumServiceConnectionCategory,Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionStatus)">
            <summary>
            設定連線狀態:
            <para>1. 原本沒有連線: 加入指定狀態的辦連線</para>
            <para>2. 原本有連線:
             <para>a. 連線狀態相同: 不做任何事</para>
             <para>b. 連線狀態不同: 更新狀態、設定事件時間 </para>
            </para>
            </summary>
            <param name="category">連線種類</param>
            <param name="status">連線狀態</param>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection">
            <summary>
            一個連線
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.#ctor(Oco.Core.SignalR.EnumServiceConnectionCategory,Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionStatus)">
            <summary>
            建構式
            </summary>
            <param name="connectionCategory">連線種類</param>
            <param name="connectionStatus">連線狀態</param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.#ctor(Oco.Core.SignalR.EnumServiceConnectionCategory,Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionStatus,System.DateTime)">
            <summary>
            建構式
            </summary>
            <param name="connectionCategory">連線種類</param>
            <param name="connectionStatus">連線狀態</param>
            <param name="eventTime">事件時間</param>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.ServiceConnectionCategory">
            <summary>
            連線種類
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.ServiceConnectionStatus">
            <summary>
            連線狀態
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.ConnectionCategoryText">
            <summary>
            連線種類的描述文字
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.ConnectionStatusText">
            <summary>
            連線狀態的描述文字
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnection.EventTime">
            <summary>
            事件時間
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer">
            <summary>
            每個用戶的連線集合
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.ServiceConnectionList">
            <summary>
            每一個用戶的連線集合
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.IsServiceConnectionListEmpty">
            <summary>
            連線集合是否為空集合?
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.GetDisconnectedSummary(System.Guid)">
            <summary>
            取得要送出斷線警報摘要的連線
            </summary>
            <param name="customerId">用戶 Id</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.AddConnection(System.Guid,Oco.Core.SignalR.EnumServiceConnectionCategory,Oco.Product_002.Api.SignalR.ConnectionCheck.EnumServiceConnectionStatus,System.Collections.Generic.List{Oco.Core.SignalR.EnumServiceConnectionCategory})">
            <summary>
            新增一個Connection
            </summary>
            <param name="customerId">用戶ID</param>
            <param name="serviceConnectionCategory">連線種類</param>
            <param name="status">連線狀態</param>
            <param name="serviceConnectionCategoryList">可以使用的連線種類集合</param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.GetCustomerServiceConnection(System.Guid)">
            <summary>
            取得指定 CustomerId 的用戶狀態
            </summary>
            <param name="customerId">用戶ID</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.AddCustomerIfNotExist(System.Guid,System.Collections.Generic.List{Oco.Core.SignalR.EnumServiceConnectionCategory})">
            <summary>
            加入一個用戶
            </summary>
            <param name="customerId">指定的Customer Id</param>
            <param name="connectionCodeList">可用的連線</param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.RemoveCustomer(System.Guid)">
            <summary>
            移除一個用戶的連線資料
            </summary>
            <param name="customerId"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.SetConnnectionToConnected(System.Guid,Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            設定一個連線的狀態
            </summary>
            <param name="customerId"></param>
            <param name="category"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.SetConnectionToDisconnected(System.Guid,Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            設定為斷線狀態
            </summary>
            <param name="customerId"></param>
            <param name="category"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ConnectionCheck.ServiceConnectionContainer.SetConnectionToInitialing(System.Guid,Oco.Core.SignalR.EnumServiceConnectionCategory)">
            <summary>
            將狀態設為初始化
            </summary>
            <param name="customerId"></param>
            <param name="category"></param>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.CustomerRegister">
            <summary>
            
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.AlarmSummaryThreadTarget">
            <summary>
            送Alarm Summary 到前端
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub._customerAlarmSummaryTimeInfos">
            <summary>
            每個 Customer 的 警報摘要, Key: Customer Id 
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub._customerServiceAlarmSummaryInfos">
            <summary>
            每個 Customer 的斷線訊息 
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub._customerClients">
            <summary>
            每個 Customer 的 連線ID, Key: Customer Id   
            </summary>        
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub._customerRegisters">
            <summary>
            註冊資料(Customer ID - 連線ID)
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.AddNewClientId(System.Guid,System.String)">
            <summary>
            新加入Client Id
            </summary>
            <param name="customerId"></param>
            <param name="clientId"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.RemoveClientId(System.String)">
            <summary>
            移除一個連線t ID
            </summary>
            <param name="clientId"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.OnConnectedAsync">
            <summary>
            用戶連線事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            用戶離線事件
            </summary>
            <param name="exception"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.AlarmSummaryHub.FromClientSendConnectedIdAndCustomerIdAsync(System.String)">
            <summary>
            收到Client 送來的 CustomerId 及 連線ID 物件的 Json
            
            </summary>
            <param name="Json"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.BaseHub.ToRespondGetAllCctvs(System.Guid,System.String,Microsoft.AspNetCore.SignalR.ISingleClientProxy)">
            <summary>
                以這個接口回覆所有CCTV 列表到 DataProvider (可能是 Modebus , DesigoCC, obix,......)
            </summary>
            <param name="customerId"></param>
            <param name="requestId"></param>
            <param name="client"></param>
            <param name="log"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.BaseHub.ToRespondGetAllTagCctvs(System.Guid,System.String,Microsoft.AspNetCore.SignalR.ISingleClientProxy)">
            <summary>
            以這個接口回覆各種協定與所有CCTV 列表 到 Client
            </summary>
            <param name="customerId"></param>
            <param name="requestId"></param>
            <param name="client"></param>
            <param name="log"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.CctvHub.RegisterClientAsync(System.Guid,System.Nullable{System.Int32},Oco.ServiceUtility.IGetService{Oco.Core.Services.ApplicationServices.GetCustomerInfoQuery,Oco.Core.Repository.CustomerInfo},Oco.Product_002.Api.SignalR.ISignalrConnectionManager,Oco.Product_002.Api.SignalR.ISignalrServiceManager{Oco.Core.SignalR.EnumCctvService},Oco.ServiceUtility.IQueryService{Oco.Core.Services.ApplicationServices.GetAllInUseCctvInfosQuery,Oco.Core.Repository.CctvInfo})">
            <summary>
            連線註冊
            </summary>
            <param name="customerId">用戶 id</param>
            <param name="serviceCode">service code</param>
            <param name="customerQueryService">customer query service</param>
            <param name="serviceManager">signalr service manager</param>
            <param name="connectionManager">signalr connection manager</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.CctvHub.ReceiveImageChunkAsync(System.String,System.Byte[],System.Int32,System.Int32,Oco.Core.Services.IImageChunkHandler,Oco.Core.Services.ICctvImageRequesterStore,Oco.Product_002.Api.Handlers.ICctvMessageHandler)">
            <summary>
            接收傳入的影像串流
            </summary>
            <param name="deviceId">device ip address</param>
            <param name="chunk">chunk</param>
            <param name="chunkIndex">chunk index</param>
            <param name="totalChunks">total chunk count</param>
            <param name="chunkHandler">chunk handler service</param>
            <param name="store">cctv image requester store</param>
            <param name="messageHandler">message handler</param>
            <returns></returns>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub._HubCallerClients">
            <summary>
                API 專用的整體 Client
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub._DicResponseInvoke">
            <summary>
                <para>回傳 Key: Request Id 與 執行結果,Value執行結束後要呼叫委派 的字典</para>
                <para>
                    委派的參數:requestId, serverIp, isSuccess, cause
                    <para>
                        1. Request Id - string, RequestId
                    </para>
                    <para>
                        2. server Ip - string, ServerIP
                    </para>
                    <para>
                        3. Is Success - bool, 是否成功
                    </para>
                    <para>
                        4. Cause - string, 失敗訊息~如果成功, 為空字串
                    </para>
                </para>
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.BgAppConnectedId">
            <summary>
                處理 Desigo CC 各種服務的 背景的連線 ID
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RemoveResponseInvoke(System.String)">
            <summary>
                移除回復執行結果回呼委派 的方法
            </summary>
            <param name="requestId">
                Request Id
            </param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.OnConnectedAsync">
            <summary>
                連線 事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.OnDisconnectedAsync0(System.Exception)">
            <summary>
                離線事件
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.Respond(System.String,System.String,System.Boolean,System.String)">
            <summary>
                回覆執行結果
            </summary>
            <param name="requestId">Request Id</param>
            <param name="serverIp">Server IP</param>
            <param name="isSuccess">是否成功</param>
            <param name="cause"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RespondSaveConfigParam(System.String,System.String,System.Boolean,System.String)">
            <summary>
                送出 「儲存Desigocc組態資料」後, Desigocc 以這個接口送回它的回應(成功或失敗)
            </summary>
            <param name="requestId">Request Id</param>
            <param name="serverIp">伺服器 ID</param>
            <param name="isSuccess">是否成功</param>
            <param name="cause">失敗的訊息</param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RespondTryConfigParam(System.String,System.String,System.Boolean,System.String)" -->
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.ToRespondGetAllCctvs0(System.Guid,System.String,Microsoft.AspNetCore.SignalR.ISingleClientProxy)">
            <summary>
                以這個接口回覆CCTV 列表 到 DesigoCC
            </summary>
            <param name="customerId"></param>
            <param name="requestId"></param>
            <param name="client"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.ToRespondGetAllTagCctvs0(System.Guid,System.String,Microsoft.AspNetCore.SignalR.ISingleClientProxy)">
            <summary>
                以這個接口回覆Tag-CCTV 列表到 DesigoCC
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RequestGetAllCctvs(System.String)">
            <summary>
                DesigoCC 以這個接口 要求回覆 CCTV 列表
            </summary>
            <param name="requestId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RequestGetAllTagCctvs(System.String)">
            <summary>
                DesigoCC 以這個接口來要求取得 Tag-CCTV 列表
            </summary>
            <param name="requestId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.BgAppRegister">
            <summary>
                背景程式透過這個接口來註冊
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.RegisterClientAsync0(System.String,System.Nullable{System.Int32})">
            <summary>
                客戶端透過這個接口送來 CustomerId 及 服務代碼
            </summary>
            <param name="customerId">客戶 ID</param>
            <param name="serviceCode">服務代碼</param>
            <returns></returns>
            <remarks>
                Customer Id 必須是 Guid 格式,否則斷線
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendDesigoCCModelToDesigoccServerAsync(System.Guid)">
            <summary>
            將 DesigoCC Model 送到 Desigocc Server
            </summary>
            <param name="customerId"></param>
            <returns></returns>    
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendDesigoccTagDto(System.String,System.Collections.Generic.List{Oco.Desigocc.Share.Model.DesigoccTagDto})">
            <summary>
                接到 DesigoCC 測點集合的處理函式, Desigocc 由這個接口送來 測點集合
            </summary>
            <param name="requestId">Request Id</param>
            <param name="tagList">測點函式集合</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendRealTimeData(System.String,System.Collections.Generic.List{Oco.Core.SignalR.DesigoCCRealTimeValueDto})">
            <summary>
                Desigo CC 以這個接口來測點即時值
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendAlarmMessage(System.Guid,System.String,System.Int32,System.DateTime,System.Guid)">
            <summary>
            由 Worker 送過來的警報訊息
            </summary>
            <param name="tagId"></param>
            <param name="value"></param>
            <param name="priority"></param>
            <param name="isAlarm"></param>
            <param name="alarmTime"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.GetSelectedTagAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,Oco.Core.SignalR.DesigoCCAlarmValueDto)">
            <summary>
            取得使用中測點資料, 如果没有, 就回傳null
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <param name="alarm"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.GetAlarmMessageAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,Oco.Product_002.Model.DataBase.TableModels.Tag,Oco.Core.SignalR.DesigoCCAlarmValueDto)">
            <summary>
            取得警報訊息
            </summary>
            <param name="db"></param>
            <param name="tag"></param>
            <param name="alarmDto"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SetMessageToDecigoAlarmStatusAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,System.Guid,System.Guid,System.String)">
            <summary>
            將訊息送回 DesigoCC Alarm 資料表
            </summary>
            <param name="db"></param>
            <param name="customerId"></param>
            <param name="tagId"></param>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.IfMustSendAlarmMessageAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,Oco.Product_002.Model.DataBase.TableModels.Tag,System.Boolean)">
            <summary>
            是否要送出警報簡訊訊息
            </summary>
            <param name="db"></param>
            <param name="tag"></param>
            <param name="isAlarm"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendAlarmData(System.String,Oco.Core.SignalR.DesigoCCAlarmValueDto)">
            <summary>
               由 Desigocc Provider 送過來的警報
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendAllEvents(System.Collections.Generic.List{Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco.DesigoccNewEventMessage})">
            <summary>
            收到 DesigoCC 送來的 事件列表
            </summary>
            <param name="events"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendCctvDevices(System.String,System.Collections.Generic.List{Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco})">
            <summary>
                Desigo CC 送回 CCTV 設備清單
            </summary>
            <param name="customerIdAsRequestId"></param>
            <param name="o"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.TryConfigParamsAsync(System.String,Oco.Product_002.Api.BusinessLogic.DesigoCcLogic.DesigoCCDeviceConfiguration,System.Int32)">
            <summary>
                呼叫這個方法, 請Desigocc 檢查組態資料的正確性
            </summary>
            <param name="requestId">RequestId</param>
            <param name="config">組態資料</param>
            <param name="expire">過期時間</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SaveConfigParamsAsync(System.String,Oco.Product_002.Model.DataBase.TableModels.DesigoCcConfig,System.Int32)">
            <summary>
                呼叫這個方法, 請Desigocc 將組態資料存起來
            </summary>
            <param name="requestId">Request Id</param>
            <param name="config">組態資料</param>
            <param name="expire">過期時間</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.ReloadTagsAsync(System.Guid,System.String,System.String,System.Guid,System.Func{System.Guid,System.Guid,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
                要求DesigoCC重載測點
            </summary>
            <param name="customerId">Customer Id</param>
            <param name="requestId">Request Id</param>
            <param name="ip">Server Ip</param>
            <param name="deviceId">device id</param>
            <param name="deleteOriginalTagListFunc">
                刪除原始測點的函式委派
                <para>
                    CustomerId, Ip, 是否成功
                </para>
            </param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.SendUsedTagName(System.Guid,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.Collections.Generic.List{System.String}})">
            <summary>
                Api 透過這個接口, 將選用的測點名稱傳送給 Desigocc
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.FakeSendUsedTagNameAsync(System.Guid,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.Collections.Generic.List{System.String}})">
            <summary>
                Api 透過這個接口, 將選用的測點名稱傳送給 Desigocc(測試時, 由網頁端呼叫)
            </summary>
            <param name="customerId"></param>
            <param name="tagNames"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.DesigoCCHub.ReloadCCtvsAsync(System.Guid)">
            <summary>
                Api 透過這個接口, 要求DesigoCC 下載 CCTV 清單
            </summary>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Hubs.BaseAlarmMessage">
            <summary>
                警報內容的基礎類別
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Hubs.SmsTargetMessage">
            <summary>
                簡訊警報內容
            </summary>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Hubs.TelVoiceTargetMessage">
            <summary>
                電話語音警報內容
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco.ProfileUrl">
            <summary>
                Arrar Profile Urls
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco.StreamUri">
            <summary>
                預設的 stream uri
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco.DesigoccNewEventMessage.Name">
            <summary>
            測點名種(這種格式:"System1:ManagementView_FieldNetworks_Modbus_Interface_1_Sim502_Y2")
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.CctvDevicePoco.DesigoccNewEventMessage.Cause">
            <summary>
            原因描述
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ICctvHub.StartRequestCctvImage(System.String,System.String)">
            <summary>
            開始請求影像串流
            </summary>
            <param name="requestId">請求Id</param>
            <param name="ipAddress">Cctv的IpAddress</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ICctvHub.StopRequestCctvImages(System.String,System.String)">
            <summary>
            停止請求影像串流
            </summary>
            <param name="requestId">請求Id</param>
            <param name="ipAddress">Cctv的IpAddress</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ICctvHub.ReceiveCctvImage(System.Guid,System.Collections.Generic.IEnumerable{System.Byte})">
            <summary>
            通知 cctv 影像
            </summary>
            <param name="cctvId"></param>
            <param name="imageBytes"></param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Hubs.IWithBackGround">
            <summary>
            背景程式註冊
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithBackGround.BgAppRegister">
            <summary>
            背景程式透過這個接口來向Api 註冊
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithBackGroundExtends.BackGroundAppDisconnect(Oco.Product_002.Api.SignalR.Hubs.IWithBackGround,System.Collections.Generic.List{System.Nullable{Oco.Core.SignalR.EnumServiceCode}},Oco.Core.SignalR.DicCustomerSignalHubContext,Oco.Core.SignalR.EnumServiceConnectionCategory,Oco.Core.SignalR.EnumServiceConnectionCategory,System.Action{System.Guid,Oco.Core.SignalR.EnumServiceConnectionCategory,System.Collections.Generic.List{Oco.Core.SignalR.EnumServiceConnectionCategory}},System.Collections.Generic.List{Oco.Product_002.Model.DataBase.TableModels.ProviderConnection},System.Collections.Generic.List{Oco.Product_002.Api.SignalR.ConnectionCheck.CustomerServiceConnection})">
            <summary>
            
            </summary>
            <param name="inst"></param>
            <param name="serviceList">服務</param>
            <param name="hubContextListOfAllCustomers"></param>
            <param name="backGroundAppConnectionCategory">背景程式連線代碼</param>
            <param name="providerConnectionCategory">data Provider 連線代碼</param>
            <param name="callBackToSetDisconnectStatus">將Service連線狀態設為斷線的回呼函式</param>
            <param name="providerConnectionList"></param>
            <param name="serviceConnectionListOfAllCustomers">所有用戶的所有Service 連線</param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithProvider.RegisterClientAsync(System.String,System.Nullable{System.Int32})">
            <summary>
            Provider 以這個方法送來 Customer Id, 及 Service Code, 向 Api 註冊 
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithProviderExtends.SetReceivedRealtimeDataDtoFromDataProvider(Oco.Product_002.Api.SignalR.Hubs.IWithProvider,System.String,System.Int32)">
            <summary>
            設定指定Customer Id, 及 Service Code 的 取得即時資料Dto時間資料
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithProviderExtends.RemoveReceivedRealtimeDataDtoFromDataProvider(Oco.Product_002.Api.SignalR.Hubs.IWithProvider,System.String,System.Int32)">
            <summary>
            移除指定Customer Id, 及 Service Code 的 取得即時資料Dto時間資料
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.IWithProviderExtends.MustForceOffLineWhenRegisterServiceCode(Oco.Product_002.Api.SignalR.Hubs.IWithProvider,System.Nullable{System.Int32},Oco.Core.SignalR.EnumServiceCode[],System.Boolean)">
            <summary>
            註冊Service Code 時, 是否要強制斷線?
            <para>
            未設定服務代碼、没有對應的預設服務代碼、代碼超出範圍、背景服務程式尚未登入等均需強制斷線
            </para>
            </summary>
            <param name="provider">Data Provider</param>
            <param name="registerSviceCode">將註冊的Service Code</param>
            <param name="defaultServiceCodeList">預設的Service Code 集合</param>
            <param name="isBackGroundAppConnected">背景服務程式是否已連線</param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult">
            <summary>
            檢查 Provider 是否強制離線的結果
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult.KeepOnLine">
            <summary>
            保持連線
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult.EmptyServiceCode">
            <summary>
            未設定 Service Code, 需斷線
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult.NoCorrespondingService">
            <summary>
            没有對應的 Service, 需斷線
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult.BackGroundAppNotLogin">
            <summary>
            背景程式尚未登入 , 需斷線
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.EnumCheckIsProviderForceOffLineResult.OtherReason">
            <summary>
            其他原因需斷線
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.ObixHub.DicResponseInvoke">
            <summary>
                <para>回傳 Key: Request Id 與 執行結果,Value執行結束後要呼叫委派 的字典</para>
                <para>
                    委派的參數:requestId, serverIp, isSuccess, cause
                    <para>
                        1. Request Id - string, RequestId
                    </para>
                    <para>
                        2. server Ip - string, ServerIP
                    </para>
                    <para>
                        3. Is Success - bool, 是否成功
                    </para>
                    <para>
                        4. Cause - string, 失敗訊息~如果成功, 為空字串
                    </para>
                </para>
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.ObixHub.BgAppConnectedId">
            <summary>
                處理 Obix 各種服務的 背景的連線 ID
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.RemoveResponseInvoke(System.String)">
            <summary>
                移除回復執行結果回呼委派 的方法
            </summary>
            <param name="requestId">
                Request Id
            </param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.RegisterClientAsync(System.String,System.Nullable{System.Int32})">
            <summary>
                客戶端透過這個接口送來 CustomerId 及 服務代碼
            </summary>
            <param name="customerId">指定客戶 ID</param>
            <param name="serviceCode">
                服務代碼:
                <para>
                    Tag 等服務
                </para>
            </param>
            <returns></returns>
            <remarks>
                Customer Id 必須是 Guid 格式,否則斷線
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendAllUseTagsForUpdateAsync(System.Guid)">
            <summary>
            由Worker Client 傳過來將 Obix Model 送到 Obix Data Provider  更新 TagModel 到最新
            </summary>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendAllUseTagsAsync(System.Guid,Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter)">
            <summary>
            </summary>
            <param name="customerId"></param>
            <param name="db"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.BgAppRegister">
            <summary>
                背景程式透過這個接口來註冊
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.OnConnectedAsync">
            <summary>
                連線 事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.BackGroundAppDisconnectAsync">
            <summary>
            背景程式離線
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.OnDisconnectedAsync(System.Exception)">
            <summary>
                離線事件
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.Respond(System.String,System.Boolean,System.String)">
            <summary>
                回覆執行結果
            </summary>
            <param name="requestId">Request Id</param>
            <param name="isSuccess">是否成功</param>
            <param name="cause"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendRealTimeData(System.String,System.Collections.Generic.List{Oco.Core.Tag.ObixTagRealTimeValueDto},System.Int32,System.Int32)">
            <summary>
                送達即時資料
            </summary>
            <param name="serverIp"></param>
            <param name="realTimeData"></param>
            <param name="batchNumber"></param>
            <param name="totalBatches"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendObixTagDtos(System.String,System.Boolean,System.String,System.Collections.Generic.List{Oco.Product_002.Api.SignalR.Hubs.ObixPoint},System.String,System.Int32,System.Int32)">
            <summary>
                Obix Server 回送 Obix 測點資料列表
            </summary>
            <param name="requestWithDeviceId">
                requestId 與 DeviceId 的組合(兩個以||連接, 如1111-1111||222-222, 1111-1111 為 RequestId, 222-222 為 DeviceId)
            </param>
            <param name="success"></param>
            <param name="serverIp"></param>
            <param name="tags"></param>
            <param name="message"></param>
            <param name="batchNumber"></param>
            <param name="totalBatches"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendUseTags(System.Collections.Generic.HashSet{System.String},System.Guid,System.String,System.String,System.String)">
            <summary>
                將OBix 使用
            </summary>
            <param name="tags"></param>
            <param name="customerId"></param>
            <param name="requestId"></param>
            <param name="obixConnectionJson"></param>
            <param name="scheam"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.SendUseTagsByBatchAsync(System.Collections.Generic.HashSet{System.String},System.Guid,System.String,System.String,System.String)">
            <summary>
            </summary>
            <param name="tags">測點集合(HashSet)</param>
            <param name="customerId"></param>
            <param name="requestId"></param>
            <param name="obixConnectionJson">
                Obix 連接字串
            </param>
            <param name="scheam">
            </param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.TryConfigParamsAsync(System.String,System.String,System.Guid,System.Int32)">
            <summary>
                呼叫這個方法, 請 Obix 檢查組態資料的正確性
            </summary>
            <param name="requestId">Request Id</param>
            <param name="jsonConnetionString">Obix 連線字串</param>
            <param name="expire">逾時時間(亳秒)</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.ObixHub.ReloadTagsAsync(System.Guid,System.String,System.String,System.String,System.Func{System.Guid,System.Guid,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
                自Obix Server 重載標籤
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.PageTagHub._PageClients">
            <summary>
                每個頁面的連線ID
                <para>
                    Key : Page Id
                </para>
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.PageTagHub._PageRegisters">
            <summary>
                註冊資料(Page ID - 連線ID)
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Hubs.PageTagHub._PageTagTimeInfos">
            <summary>
                警報摘要, Key: PageId
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.PageTagHub.OnConnectedAsync">
            <summary>
                用戶連線事件
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.PageTagHub.RemoveClientId(System.String)">
            <summary>
                移除一個連線ID
            </summary>
            <param name="clientId"></param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.PageTagHub.OnDisconnectedAsync(System.Exception)">
            <summary>
                用戶離線事件
            </summary>
            <param name="exception"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.PageTagHub.FromClientSendConnectedIdAndPageIdAsync(System.String)">
            <summary>
                收到 Client 送來的 Page Id 及 連線ID 物件的 Json
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.PageTagHub.AddNewClientIdAsync(System.Guid,System.String)">
            <summary>
                新加入Client Id
            </summary>
            <param name="pageId"></param>
            <param name="clientId"></param>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.BgAppConnectedId">
            <summary>
                處理 ModBus 各種服務的 背景的連線 ID
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.OnConnectedAsync">
            <summary>
                Provider , BackGround 連線
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.BackGroundAppDisconnectAsync">
            <summary>
            背景程式離線
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            離線
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.OnDisconnectedAsync0(System.Exception)">
            <summary>
                Provider , BackGround 離線
            </summary>
            <param name="e"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.SendCustomerId0(System.String)">
            <summary>
            Modbus Provider 連線後, 送回 Customer Id 
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.FromClientTagModelExecutId">
            <summary>
               Modbus  背景程式透過這個接口來註冊
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.BgAppRegister">
            <summary>
            Modbus  背景程式透過這個接口來註冊
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.SendRealTimeData(System.Collections.Generic.List{Oco.Core.Modbus.Model.TagModelDto})">
            <summary>
                接收即時資料轉傳給 Worker Service
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Hubs.TagModelHub.ToClientToSetBoolTrueAsync(System.Collections.Generic.List{System.String},System.Guid)">
            <summary>
                更新 數位測點值
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ISignalrConnectionManager.RemoveConnectionIdAsync(System.Guid,System.String,System.Threading.CancellationToken)">
            <summary>
            移除特定 ConnectionId
            </summary>
            <param name="customerId">CustomerId</param>
            <param name="connectionId">ConnectionId</param>
            <param name="cancellationToken"></param>
            <returns>是否該 ConnectionId 被移除</returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ISignalrConnectionManager.RemoveConnectionIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            移除特定 ConnectionId
            </summary>
            <param name="connectionId"></param>
            <param name="cancellationToken"></param>
            <returns>ConnectionId 被移除的 CustomerId</returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.BaseInform.SaveMessageGroupHistory(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBWriter,Oco.Product_002.Model.DataBase.TableModels.InformMessageGroupHistory)">
            <summary>
                設定訊息訊息記錄
            </summary>
            <param name="db"></param>
            <param name="h"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.BaseInform.GetAlarmMessageAsync(Oco.Product_002.Model.DataBase.DbFactoreyMethod.DBReader,System.Guid,System.Guid)">
            <summary>
                取得警報訊息
            </summary>
            <param name="db"></param>
            <param name="tagId"></param>
            <param name="customerId"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.BaseInform.IsInDuration(System.DateTime,System.DateTime,System.DateTime,System.DateTime,System.Int32)">
            <summary>
                檢查現在是否在訊息時間區間內
            </summary>
            <param name="s">預設起始時間</param>
            <param name="e">預設結束時間</param>
            <param name="thisday">本日日期</param>
            <param name="thisTime">現在時間</param>
            <param name="duration">工作區段(本日/跨日)</param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.BaseInform.TelNoMessageSendAsync(System.DateTime,System.Guid,System.Guid,System.Guid,System.String)">
            <summary>
                電話撥號送出訊息
            </summary>
            <param name="alarmDto"></param>
            <param name="customerId"></param>
            <param name="tagId"></param>
            <param name="_messageId"></param>
            <param name="_message"></param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern">
            <summary>
            通知訊息的模板
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern.MesseTaoyuan">
            <summary>
            桃園會展中心的訊息格式
            </summary>
        </member>
        <member name="F:Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern.LJTechN_Nangang">
            <summary>
            利健南港生技中心的訊息格式 
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.EnumMassagePatternExtends.GetContentTextAry(Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern,Oco.Product_002.Model.DataBase.TableModels.Tag,Oco.Product_002.Model.DataBase.TableModels.TagAlarmRule,System.DateTime)">
            <summary>
            
            </summary>
            <param name="inst"></param>
            <param name="tag"></param>
            <param name="alarmRule"></param>
            <param name="alarmTime"></param>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.Messages.MessageCreate.GetMessageFormAlarmFromWorker(Oco.Product_002.Api.SignalR.Messages.EnumMassagePattern,Oco.Product_002.Model.DataBase.TableModels.Tag,Oco.Product_002.Model.DataBase.TableModels.TagAlarmRule,System.DateTime)">
            <summary>
            取得, 從 Worker 送過來的 警報訊息
            </summary>
            <param name="pattern"></param>
            <param name="tag"></param>
            <param name="alarmRule"></param>
            <param name="alarmTime"></param>
            <returns></returns>
        </member>
        <member name="T:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime">
            <summary>
            從DatatProvider  取得即時資料Dto 的時間
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.CustomerId">
            <summary>
            Customer Id
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.ServiceCode">
            <summary>
            Service Code
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.GetRealTimeDataDtoTime">
            <summary>
            取得即時資料Dto 的時間(就是 Data Provider 透過 SignalR 傳送Dto到 Signla HUB 的時間)
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime._dtoTimeList">
            <summary>
            存放最近一筆 RealTime Data Dto 的時間集合
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.DtoTimeList">
            <summary>
            存放最近一筆 RealTime Data Dto 的時間集合()
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.SetGetRealtimeDataDtoTime(System.Guid,Oco.Core.SignalR.EnumServiceCode)">
            <summary>
            設定最近一筆 RealTime Data Dto 的時間
            </summary>
            <param name="customerId">Customer Id</param>
            <param name="serviceCode">服務代碼</param>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.GetRealTimeDataTime(System.Guid,Oco.Core.SignalR.EnumServiceCode)">
            <summary>
            取得指定最近服務的即時資料Dto 的時間
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.IsGetRealtimeDataDtoTimeExist(System.Guid,Oco.Core.SignalR.EnumServiceCode)">
            <summary>
            指定的 Customer Id 和 Service Code 的Provider Hub 是否有即時資料存取記錄?
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.SignalR.ReceivedRealtimeDataDtoFromDataProviderTime.RemoveRealTimeDataTime(System.Guid,Oco.Core.SignalR.EnumServiceCode)">
            <summary>
            移除指定的 Customer Id 和 Service Code 的傳送即時資料的時間組
            </summary>
            <param name="customerId"></param>
            <param name="serviceCode"></param>
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.BaseSetting.GetJson">
            <summary>
            將物件轉為Json
            </summary>
            <returns></returns>
        </member>
        <member name="P:Oco.Product_002.Api.TagSetting.DesigoCCDeviceSetting.Uri">
            <summary>
            Uri(http://Host:port)  like: http://127.0.0.1:8080
            如果是不合法者
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.UserName">
            <summary>
            使用者名稱
            </summary>
        </member>
        <member name="P:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.Uri">
            <summary>
            Uri(http://Host:port)  like: http://127.0.0.1:8080
            如果是不合法者
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.GetUriHeadWithPort">
            <summary>
            取得 http://host:port
            </summary>
            <returns></returns>        
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.GetUriHead">
            <summary>
            取得 http://host 或 https://host 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.GetUriHost">
            <summary>
            取得 Host 名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.GetUriScheme">
            <summary>
            取得 http 或 https
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.TagSetting.ObixDeviceSetting.GetUriPort">
            <summary>
            取得 Port 號
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidSummaryIdAsync">
            <summary>
            檢查摘要ID
            </summary>
            <returns></returns>
            <remarks>
            <para>沒有設定時,摘要ID</para>
            <para>檢查摘要ID是否存在</para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidateStageIdAsync">
            <summary>
            檢查Stage Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidStageCodeAsync">
            <summary>
            檢查階段代碼
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidStageNameAsync">
            <summary>
            檢查階段名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.IsEmptyStageName">
            <summary>
            是否設定卸載階名稱
            </summary>
            <returns></returns>
            <remarks>
            沒有設定時, 回傳錯誤訊息, 有設定時回傳空字串
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidLimitValueAsync">
            <summary>
            檢查卸載上限及加載下限值
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.BaseModifyElectricPowerUnloadStageGroupRule.ValidStageAlarmTagAsync">
            <summary>
            檢查警報測點
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.CreateElectricPowerUnloadStageGroupRule.ValidStageCodeAsync">
            <summary>
            新增 Stage, 檢查StageCode
            </summary>
            <returns></returns>
            <remarks>
            <para>是否設定 Stage</para>
            <para>Customer Id + Summary  Id+ stage 是否存在? </para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.CreateElectricPowerUnloadStageGroupRule.ValidateStageIdAsync">
            <summary>
            新增 Stage, Nothing
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.CreateElectricPowerUnloadStageGroupRule.ValidStageNameAsync">
            <summary>
            新增卸載階段, 檢查階段名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.CreateElectricPowerUnloadStageGroupRule.ValidLimitValueAsync">
            <summary>
            新增加/卸載階段, 檢查加卸載上下限值
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.CreateElectricPowerUnloadStageGroupRule.ValidStageAlarmTagAsync">
            <summary>
            新增加/卸載階段, 檢查警報測點
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.DeleteElectricPowerUnloadStageGroupRule.ValidStageCodeAsync">
            <summary>
            刪除一個階段, 不處理
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.DeleteElectricPowerUnloadStageGroupRule.ValidateStageIdAsync">
            <summary>
            刪除 Stage, 要檢查 StageId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.DeleteElectricPowerUnloadStageGroupRule.ValidStageNameAsync">
            <summary>
            刪除一個階段, 不處理
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.DeleteElectricPowerUnloadStageGroupRule.ValidLimitValueAsync">
            <summary>
            刪除一個階段, 不處理
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.DeleteElectricPowerUnloadStageGroupRule.ValidStageAlarmTagAsync">
            <summary>
            刪除一個階段, 不處理
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.UpdateElectricPowerUnloadStageGroupRule.ValidStageCodeAsync">
            <summary>
            更新 Stage, 檢查StageCode
            </summary>
            <returns></returns>
            <remarks>
            <para>是否設定 Stage?</para>
            <para>
            Customer Id + Summary  Id+ stage 是否存在? 
            如果存在,Stage ID 是否一樣?
            </para>
            <para>如果一樣  是同一個加,卸載階段, 没事</para>
            <para>如果不一樣, 不同個加/卸載階段, 重複階段代碼, 不可以</para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.UpdateElectricPowerUnloadStageGroupRule.ValidateStageIdAsync">
            <summary>
            更新 Stage, 要檢查 StageId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.UpdateElectricPowerUnloadStageGroupRule.ValidStageNameAsync">
            <summary>
            更新 Stage, 檢查StageName
            </summary>
            <returns></returns>
            <remarks>
            <para>是否設定 StageName?</para>
            <para>
             StageName 是否重複, 以 CustomerId, SummaryId, StageName 等是否可以找到一個 Stage 實體?
             <para>1. 找不到, 沒事</para>
             <para>2. 找到, 如果, StageId 一樣->沒事; StageId 不一樣->重複</para>
            </para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.UpdateElectricPowerUnloadStageGroupRule.ValidLimitValueAsync">
            <summary>
            更新 加/卸載階段, 檢查加卸載上下限值
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifyElectricPowerUnloadStageGroupRequest.ValidateRules.UpdateElectricPowerUnloadStageGroupRule.ValidStageAlarmTagAsync">
            <summary>
            更新 加/卸載階段, 檢查警報測點
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.BaseModifyLoadAndUnloadSummaryRule.ValidateSummaryIdAsync">
            <summary>
            檢查 SummaryId 
            </summary>
            <returns>
            有没有指定SummaryId?<para>
            </para>
            <para>SUmmaryId是否存在?</para>
            </returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.BaseModifyLoadAndUnloadSummaryRule.ValidateNameAsyncAsync">
            <summary>
            檢查 SUMMARY 名稱
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.BaseModifyLoadAndUnloadSummaryRule.ValidateUnloadModeAsync">
            <summary>
            檢查卸載模式(停用, 實際, 模擬)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.BaseModifyLoadAndUnloadSummaryRule.ValidateContinuedSecondAsync">
            <summary>
            檢查確認狀態時續秒數
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.BaseModifyLoadAndUnloadSummaryRule.ValidateIsLoadAsync">
            <summary>
            確認是否要加載
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.CreateLoadAndUnloadSummaryRule.ValidateNameAsyncAsync">
            <summary>
            新增 Summary, 檢查 Summary Name 是否重複
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.CreateLoadAndUnloadSummaryRule.ValidateContinuedSecondAsync">
            <summary>
            新增 Summary, 檢查確認狀態時續秒數
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.CreateLoadAndUnloadSummaryRule.ValidateIsLoadAsync">
            <summary>
            新增 Summary, 檢查有没有設定是否要加載
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.CreateLoadAndUnloadSummaryRule.ValidateSummaryIdAsync">
            <summary>
            新增 Summary , 不需檢查SummaryId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.CreateLoadAndUnloadSummaryRule.ValidateUnloadModeAsync">
            <summary>
            新增 Summary, 檢查卸載模式
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.DeleteLoadAndUnloadSummaryRule.ValidateContinuedSecondAsync">
            <summary>
            刪除 Summary , 不需檢查SummaryId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.DeleteLoadAndUnloadSummaryRule.ValidateIsLoadAsync">
            <summary>
            刪除 Summary, 不需檢查有没有設定是否要加載
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.DeleteLoadAndUnloadSummaryRule.ValidateNameAsyncAsync">
            <summary>
            刪除 Summary, 不需檢查 Summary Name
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.DeleteLoadAndUnloadSummaryRule.ValidateUnloadModeAsync">
            <summary>
            刪除 Summary, 不需檢查卸載模式
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.DeleteLoadAndUnloadSummaryRule.ValidateSummaryIdAsync">
            <summary>
            刪除 Summary, 檢查要刪除的 Summary 是否存在?
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.UpdateLoadAndUnloadSummaryRule.ValidateContinuedSecondAsync">
            <summary>
            更新 Summary, 檢查確認狀態時續秒數
            </summary>
            <returns></returns>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.UpdateLoadAndUnloadSummaryRule.ValidateIsLoadAsync">
            <summary>
            更新 Summary, 檢查有没有設定是否要加載
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.UpdateLoadAndUnloadSummaryRule.ValidateNameAsyncAsync">
            <summary>
            更新Summary 
            </summary>
            <returns></returns>
            <remarks>
            <para>1. 檢查有没有設定 Summary Name?</para>
            <para>2. 檢查 Summary Name 是否重複?</para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.UpdateLoadAndUnloadSummaryRule.ValidateSummaryIdAsync">
            <summary>
            更新 Summary, 檢查要更新的 Summary Id 是否存在 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.ElectricLoadAndUnload.ModifySummaryRequest.ValidateRules.UpdateLoadAndUnloadSummaryRule.ValidateUnloadModeAsync">
            <summary>
            更新Summary, 檢查卸載模式設定(實際, 模擬, 停止)
            </summary>
            <remarks>
            <para>1. 有没有設定 模式</para>
            <para>2. 卸載模式設定有没有超過範圍?</para>
            </remarks>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateDeviceNameAsync">
            <summary>
            <para>1. 檢查 DeviceName 有没有設定?</para>
            <para>2. 檢查 DeviceName 是否重複?</para>
            </summary>
            <returns></returns>    
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateDescriptionAsync">
            <summary>
            <para>1. 檢查 DeviceDescription 有没有設定?</para>
            </summary>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateRegionAsync">
            <summary>
            檢查 Region
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateDeviceDataAddressFromAndArrangeAsync">
            <summary>
            檢查 資料起起始位址代碼
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateDeviceDataFormatArrangeAsync">
            <summary>
            檢查 DeviceDataFormat 並重新整理
            <para>1.有没有設定</para>
            <para>2.是否在範圍內</para>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateTcpIpPortStationNoAndArrangeAsync">
            <summary>
            檢查 TCPIP、Port、站號 並重新整理
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            檢查鮑率與RtuPort
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateEndPointAndArrangeAsync">
            <summary>
            檢查 ObixProtocalPrefix
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.BaseCreateNewDeviceValidateRule.ValidateSettingJsonAndArrangeAsync">
            <summary>
            檢查、重整 SettingJson
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewModbusDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            檢查、整理 BaudRate與RtuPort
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewModbusDeviceValidateRule.ValidateDeviceDataAddressFromAndArrangeAsync">
            <summary>
            檢查資料起始位址
            </summary>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewModbusDeviceValidateRule.ValidateDeviceDataFormatArrangeAsync">
            <summary>
            檢查 DeviceDataFormat 並重新整理
            </summary>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewModbusDeviceValidateRule.ValidateTcpIpPortStationNoAndArrangeAsync">
            <summary>
            檢查 IP、Port及站號
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewVirtualDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            整理BaudRate與RtuPort
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.CreateNewDeviceRequest.ValidateRules.CreateNewVirtualDeviceValidateRule.ValidateDeviceDataAddressFromAndArrangeAsync">
            <summary>
            整理 DeviceDataAddressFrom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateDeviceNameAsync">
            <summary>
            檢查 DeviceName 
            </summary>
            <para>是否設定</para>
            <para>是否重複</para>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateDescriptionAsync">
            <summary>
            檢查 Description
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateRegionAsync">
            <summary>
            檢查 Region
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateDeviceCategoryAsync">
            <summary>
            檢查 DeviceCategory
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateStatusAsync">
            <summary>
            檢查 Status
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateDeviceDataAddressFromAndArrangeAsync">
            <summary>
            檢查 資料起起始位址代碼
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateDeviceDataFormatArrangeAsync">
            <summary>
            檢查 DeviceDataFormat 並重新整理
            <para>1.有没有設定</para>
            <para>2.是否在範圍內</para>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateTcpIpPortStationNoAndArrangeAsync">
            <summary>
            檢查 TCPIP、Port、站號 並重新整理
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            檢查鮑率與RtuPort
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateEndPointAndArrangeAsync">
            <summary>
            檢查 ObixProtocalPrefix
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.BaseUpdateDeviceValidateRule.ValidateSettingJsonAndArrangeAsync">
            <summary>
            檢查、重整 SettingJson
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.UpdateModbusDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            檢查、整理 BaudRate與RtuPort
            </summary>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.UpdateModbusDeviceValidateRule.ValidateDeviceDataAddressFromAndArrangeAsync">
            <summary>
            檢查資料起始位址
            </summary>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.UpdateModbusDeviceValidateRule.ValidateDeviceDataFormatArrangeAsync">
            <summary>
            檢查 DeviceDataFormat 並重新整理
            </summary>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.UpdateModbusDeviceValidateRule.ValidateTcpIpPortStationNoAndArrangeAsync">
            <summary>
            檢查 IP、Port及站號
            </summary>
            <returns></returns>
            <exception cref="T:Oco.Product_002.Api.Validators.ValidateException"></exception>
        </member>
        <member name="M:Oco.Product_002.Api.Validators.Tag.UpdateDeviceRequest.ValidateRules.UpdateVirtualDeviceValidateRule.ValidateBaudRateAndRtuPortAndArrangeAsync">
            <summary>
            整理BaudRate與RtuPort
            </summary>
            <returns></returns>
        </member>
    </members>
</doc>
