{"runtimeOptions": {"tfm": "net6.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "6.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "6.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configProperties": {"Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}