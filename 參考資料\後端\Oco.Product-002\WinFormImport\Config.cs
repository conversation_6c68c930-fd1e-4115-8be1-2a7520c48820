﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TagImportAndExport;

namespace WinFormImport
{
    public class Config
    {
        public string? DbWriterText { get; set; }
        public string? DbReaderText { get; set; }
        public string? LogDir { get; set; }
        public string? LogFileFormat { get; set; }
        public string? ExportDir { get; set; }
        public string? ExportFilePostFixFormat { get; set; }

        public string GetExportFileName(Guid customerId)
        {
            var idText = customerId;
            var rtnV = $"{ExportDir}[{idText}]-{DateTime.Now.ToString(ExportFilePostFixFormat)}.csv";
            return rtnV;
        }

        public string FullLogFileName
        {
            get
            {
                return LogDir + DateTime.Now.ToString(LogFileFormat) + ".log";
            }
        }
        public Config()
        {
            var dbFile = "";
#if DEBUG
            dbFile = "IGN_DatabaseText.Task.json";
#elif MAIN
             dbFile = "IGN_DatabaseText.Main.json";
#endif
            var path = AppDomain.CurrentDomain.BaseDirectory + dbFile;
            var sr = new StreamReader(path, System.Text.Encoding.Default);
            var Json = sr.ReadToEnd();
            sr.Close();
            var dbSetting = Newtonsoft.Json.JsonConvert.DeserializeObject<DbSetting>(Json);
            var configSetting = Newtonsoft.Json.JsonConvert.DeserializeObject<ConfigSetting>(Json);
            this.DbReaderText = dbSetting!.DbReaderText;
            this.DbWriterText = dbSetting!.DbWriterText;

            this.LogDir = configSetting!.LogDir;
            this.LogFileFormat = configSetting!.LogFileFormat;
            this.ExportDir = configSetting!.ExportDir;
            this.ExportFilePostFixFormat = configSetting.ExportFilePostFixFormat;
        }
    }
    public class ConfigSetting
    {
        public string? LogDir { get; set; }
        public string? LogFileFormat { get; set; }
        public string? ExportDir { get; set; }
        public string? ExportFilePostFixFormat { get; set; }
    }
}
