import axios from "axios";
import { getItem } from "../../utility/localStorageControl";
import qs from "qs";
import store from "@/vuex/store";
import router from "@/routes/protectedRoute";

const authHeader = () => ({
  Authorization: `Bearer ${getItem("access_token")}`,
});

// 注意：baseURL 使用環境變數，不要硬編碼！
// 各個 API 調用會自己加上正確的路徑前綴
const client = axios.create({
  baseURL: process.env.VUE_APP_API_ENDPOINT, // 使用環境變數
  headers: {
    Authorization: `Bearer ${getItem("access_token")}`,
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

class DataService {
  static get(path = "", data = {}, optionalHeader = {}) {
    let url = path;
    if (data && !optionalHeader["Content-Type"]) {
      url += `?${new URLSearchParams(data)}`;
    }
    return client({
      method: "GET",
      url,
      headers: { ...authHeader(), ...optionalHeader },
    });
  }

  static post(path = "", data = {}, optionalHeader = {}, responseType) {
    if (data && !optionalHeader["Content-Type"]) {
      data = qs.stringify(data);
    } else if (optionalHeader["Content-Type"] === "application/json") {
      data = JSON.stringify(data);
    }
    return client({
      method: "POST",
      url: path,
      data,
      headers: { ...authHeader(), ...optionalHeader },
      responseType: responseType,
    });
  }

  static patch(path = "", data = {}) {
    return client({
      method: "PATCH",
      url: path,
      data: JSON.stringify(data),
      headers: { ...authHeader() },
    });
  }

  static delete(path = "", data = {}, optionalHeader = {}) {
    return client({
      method: "DELETE",
      url: path,
      data: data,
      headers: { ...authHeader(), ...optionalHeader },
    });
  }

  static put(path = "", data = {}, optionalHeader = {}) {
    if (optionalHeader["Content-Type"] === "application/json") {
      data = JSON.stringify(data);
    } else if (data && !optionalHeader["Content-Type"]) {
      data = qs.stringify(data);
    }
    return client({
      method: "PUT",
      url: path,
      data: data,
      headers: { ...authHeader(), ...optionalHeader },
    });
  }
}

/**
 * axios interceptors runs before and after a request, letting the developer modify req,req more
 * For more details on axios interceptor see https://github.com/axios/axios#interceptors
 */
client.interceptors.request.use((config) => {
  // do something before executing the request
  // For example tag along the bearer access token to request header or set a cookie
  const requestConfig = config;
  const { headers } = config;
  requestConfig.headers = {
    ...headers,
    Authorization: `Bearer ${getItem("access_token")}`,
  };
  return requestConfig;
});

client.interceptors.response.use(
  (response) => {
    const contentType = response.headers["content-type"];
    if (
      contentType &&
      contentType.includes(
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      )
    ) {
      return response;
    }
    console.log("API 回應:", response.data);
    if (Number(response.data.ReturnCode) !== 1) {
      console.log("登入失敗，ReturnCode:", response.data.ReturnCode, "Message:", response.data.Message);
      throw new Error(response.data.Message);
    } else {
      return response;
    }
  },
  (error) => {
    /**
     * Do something in case the response returns an error code [3**, 4**, 5**] etc
     * For example, on token expiration retrieve a new access token, retry a failed request etc
     */
    const { response } = error;
    // const originalRequest = error.config;
    if (response) {
      if (response.status === 401) {
        store.dispatch("auth/logOut");
        const brandId = getItem("brand_id");
        router.push({ name: "login", params: { id: brandId || "1" } });
      }
    }
    return Promise.reject(error);
  }
);
export { DataService };
