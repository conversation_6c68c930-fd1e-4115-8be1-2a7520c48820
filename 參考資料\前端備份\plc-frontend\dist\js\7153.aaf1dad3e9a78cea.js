(self["webpackChunkplc2_0"]=self["webpackChunkplc2_0"]||[]).push([[7153],{7153:function(t,e,n){"use strict";n.d(e,{A:function(){return Qt}});var o=n(20641),i=n(72644);const r=["id","height"];function a(t,e,n,a,s,c){return(0,o.uX)(),(0,o.CE)("canvas",{class:(0,i.C4)(t.className),id:t.id,style:(0,i.Tr)(t.style),height:t.height},null,14,r)}var s=n(57237),c=n(19732),u=n(79841),l=n(27615),f=n(47168),h=n.n(f),d=n(32998),p=n(96763);
/*!
* chartjs-plugin-zoom v2.2.0
* https://www.chartjs.org/chartjs-plugin-zoom/2.2.0/
 * (c) 2016-2024 chartjs-plugin-zoom Contributors
 * Released under the MIT License
 */
const m=t=>t&&t.enabled&&t.modifierKey,g=(t,e)=>t&&e[t+"Key"],y=(t,e)=>t&&!e[t+"Key"];function b(t,e,n){return void 0===t||("string"===typeof t?-1!==t.indexOf(e):"function"===typeof t&&-1!==t({chart:n}).indexOf(e))}function v(t,e){return"function"===typeof t&&(t=t({chart:e})),"string"===typeof t?{x:-1!==t.indexOf("x"),y:-1!==t.indexOf("y")}:{x:!1,y:!1}}function x(t,e){let n;return function(){return clearTimeout(n),n=setTimeout(t,e),e}}function w({x:t,y:e},n){const o=n.scales,i=Object.keys(o);for(let r=0;r<i.length;r++){const n=o[i[r]];if(e>=n.top&&e<=n.bottom&&t>=n.left&&t<=n.right)return n}return null}function T(t,e,n){const{mode:o="xy",scaleMode:i,overScaleMode:r}=t||{},a=w(e,n),s=v(o,n),c=v(i,n);if(r){const t=v(r,n);for(const e of["x","y"])t[e]&&(c[e]=s[e],s[e]=!1)}if(a&&c[a.axis])return[a];const u=[];return(0,d.Q)(n.scales,(function(t){s[t.axis]&&u.push(t)})),u}const M=new WeakMap;function S(t){let e=M.get(t);return e||(e={originalScaleLimits:{},updatedScaleLimits:{},handlers:{},panDelta:{},dragging:!1,panning:!1},M.set(t,e)),e}function C(t){M.delete(t)}function _(t,e,n,o){const i=Math.max(0,Math.min(1,(t-e)/n||0)),r=1-i;return{min:o*i,max:o*r}}function k(t,e){const n=t.isHorizontal()?e.x:e.y;return t.getValueForPixel(n)}function E(t,e,n){const o=t.max-t.min,i=o*(e-1),r=k(t,n);return _(r,t.min,o,i)}function O(t,e,n){const o=k(t,n);if(void 0===o)return{min:t.min,max:t.max};const i=Math.log10(t.min),r=Math.log10(t.max),a=Math.log10(o),s=r-i,c=s*(e-1),u=_(a,i,s,c);return{min:Math.pow(10,i+u.min),max:Math.pow(10,r-u.max)}}function P(t,e){return e&&(e[t.id]||e[t.axis])||{}}function A(t,e,n,o,i){let r=n[o];if("original"===r){const n=t.originalScaleLimits[e.id][o];r=(0,d.v)(n.options,n.scale)}return(0,d.v)(r,i)}function z(t,e,n){const o=t.getValueForPixel(e),i=t.getValueForPixel(n);return{min:Math.min(o,i),max:Math.max(o,i)}}function R(t,{min:e,max:n,minLimit:o,maxLimit:i},r){const a=(t-n+e)/2;e-=a,n+=a;const s=r.min.options??r.min.scale,c=r.max.options??r.max.scale,u=t/1e6;return(0,d.aK)(e,s,u)&&(e=s),(0,d.aK)(n,c,u)&&(n=c),e<o?(e=o,n=Math.min(o+t,i)):n>i&&(n=i,e=Math.max(i-t,o)),{min:e,max:n}}function I(t,{min:e,max:n},o,i=!1){const r=S(t.chart),{options:a}=t,s=P(t,o),{minRange:c=0}=s,u=A(r,t,s,"min",-1/0),l=A(r,t,s,"max",1/0);if("pan"===i&&(e<u||n>l))return!0;const f=t.max-t.min,h=i?Math.max(n-e,c):f;if(i&&h===c&&f<=c)return!0;const d=R(h,{min:e,max:n,minLimit:u,maxLimit:l},r.originalScaleLimits[t.id]);return a.min=d.min,a.max=d.max,r.updatedScaleLimits[t.id]=d,t.parse(d.min)!==t.min||t.parse(d.max)!==t.max}function D(t,e,n,o){const i=E(t,e,n),r={min:t.min+i.min,max:t.max-i.max};return I(t,r,o,!0)}function j(t,e,n,o){const i=O(t,e,n);return I(t,i,o,!0)}function F(t,e,n,o){I(t,z(t,e,n),o,!0)}const Y=t=>0===t||isNaN(t)?0:t<0?Math.min(Math.round(t),-1):Math.max(Math.round(t),1);function N(t){const e=t.getLabels(),n=e.length-1;t.min>0&&(t.min-=1),t.max<n&&(t.max+=1)}function L(t,e,n,o){const i=E(t,e,n);t.min===t.max&&e<1&&N(t);const r={min:t.min+Y(i.min),max:t.max-Y(i.max)};return I(t,r,o,!0)}function X(t){return t.isHorizontal()?t.width:t.height}function W(t,e,n){const o=t.getLabels(),i=o.length-1;let{min:r,max:a}=t;const s=Math.max(a-r,1),c=Math.round(X(t)/Math.max(s,10)),u=Math.round(Math.abs(e/c));let l;return e<-c?(a=Math.min(a+u,i),r=1===s?a:a-s,l=a===i):e>c&&(r=Math.max(0,r-u),a=1===s?r:r+s,l=0===r),I(t,{min:r,max:a},n)||l}const B={second:500,minute:3e4,hour:18e5,day:432e5,week:3024e5,month:1296e6,quarter:5184e6,year:157248e5};function H(t,e,n,o=!1){const{min:i,max:r,options:a}=t,s=a.time&&a.time.round,c=B[s]||0,u=t.getValueForPixel(t.getPixelForValue(i+c)-e),l=t.getValueForPixel(t.getPixelForValue(r+c)-e);return!(!isNaN(u)&&!isNaN(l))||I(t,{min:u,max:l},n,!!o&&"pan")}function q(t,e,n){return H(t,e,n,!0)}const $={category:L,default:D,logarithmic:j},Z={default:F},V={category:W,default:H,logarithmic:q,timeseries:q};function Q(t,e,n){const{id:o,options:{min:i,max:r}}=t;if(!e[o]||!n[o])return!0;const a=n[o];return a.min!==i||a.max!==r}function U(t,e){(0,d.Q)(t,((n,o)=>{e[o]||delete t[o]}))}function K(t,e){const{scales:n}=t,{originalScaleLimits:o,updatedScaleLimits:i}=e;return(0,d.Q)(n,(function(t){Q(t,o,i)&&(o[t.id]={min:{scale:t.min,options:t.options.min},max:{scale:t.max,options:t.options.max}})})),U(o,n),U(i,n),o}function J(t,e,n,o){const i=$[t.type]||$.default;(0,d.C)(i,[t,e,n,o])}function G(t,e,n,o){const i=Z[t.type]||Z.default;(0,d.C)(i,[t,e,n,o])}function tt(t){const e=t.chartArea;return{x:(e.left+e.right)/2,y:(e.top+e.bottom)/2}}function et(t,e,n="none",o="api"){const{x:i=1,y:r=1,focalPoint:a=tt(t)}="number"===typeof e?{x:e,y:e}:e,s=S(t),{options:{limits:c,zoom:u}}=s;K(t,s);const l=1!==i,f=1!==r,h=T(u,a,t);(0,d.Q)(h||t.scales,(function(t){t.isHorizontal()&&l?J(t,i,a,c):!t.isHorizontal()&&f&&J(t,r,a,c)})),t.update(n),(0,d.C)(u.onZoom,[{chart:t,trigger:o}])}function nt(t,e,n,o="none",i="api"){const r=S(t),{options:{limits:a,zoom:s}}=r,{mode:c="xy"}=s;K(t,r);const u=b(c,"x",t),l=b(c,"y",t);(0,d.Q)(t.scales,(function(t){t.isHorizontal()&&u?G(t,e.x,n.x,a):!t.isHorizontal()&&l&&G(t,e.y,n.y,a)})),t.update(o),(0,d.C)(s.onZoom,[{chart:t,trigger:i}])}function ot(t,e,n,o="none",i="api"){const r=S(t);K(t,r);const a=t.scales[e];I(a,n,void 0,!0),t.update(o),(0,d.C)(r.options.zoom?.onZoom,[{chart:t,trigger:i}])}function it(t,e="default"){const n=S(t),o=K(t,n);(0,d.Q)(t.scales,(function(t){const e=t.options;o[t.id]?(e.min=o[t.id].min.options,e.max=o[t.id].max.options):(delete e.min,delete e.max),delete n.updatedScaleLimits[t.id]})),t.update(e),(0,d.C)(n.options.zoom.onZoomComplete,[{chart:t}])}function rt(t,e){const n=t.originalScaleLimits[e];if(!n)return;const{min:o,max:i}=n;return(0,d.v)(i.options,i.scale)-(0,d.v)(o.options,o.scale)}function at(t){const e=S(t);let n=1,o=1;return(0,d.Q)(t.scales,(function(t){const i=rt(e,t.id);if(i){const e=Math.round(i/(t.max-t.min)*100)/100;n=Math.min(n,e),o=Math.max(o,e)}})),n<1?n:o}function st(t,e,n,o){const{panDelta:i}=o,r=i[t.id]||0;(0,d.s)(r)===(0,d.s)(e)&&(e+=r);const a=V[t.type]||V.default;(0,d.C)(a,[t,e,n])?i[t.id]=0:i[t.id]=e}function ct(t,e,n,o="none"){const{x:i=0,y:r=0}="number"===typeof e?{x:e,y:e}:e,a=S(t),{options:{pan:s,limits:c}}=a,{onPan:u}=s||{};K(t,a);const l=0!==i,f=0!==r;(0,d.Q)(n||t.scales,(function(t){t.isHorizontal()&&l?st(t,i,c,a):!t.isHorizontal()&&f&&st(t,r,c,a)})),t.update(o),(0,d.C)(u,[{chart:t}])}function ut(t){const e=S(t);K(t,e);const n={};for(const o of Object.keys(t.scales)){const{min:t,max:i}=e.originalScaleLimits[o]||{min:{},max:{}};n[o]={min:t.scale,max:i.scale}}return n}function lt(t){const e=S(t),n={};for(const o of Object.keys(t.scales))n[o]=e.updatedScaleLimits[o];return n}function ft(t){const e=ut(t);for(const n of Object.keys(t.scales)){const{min:o,max:i}=e[n];if(void 0!==o&&t.scales[n].min!==o)return!0;if(void 0!==i&&t.scales[n].max!==i)return!0}return!1}function ht(t){const e=S(t);return e.panning||e.dragging}const dt=(t,e,n)=>Math.min(n,Math.max(e,t));function pt(t,e){const{handlers:n}=S(t),o=n[e];o&&o.target&&(o.target.removeEventListener(e,o),delete n[e])}function mt(t,e,n,o){const{handlers:i,options:r}=S(t),a=i[n];if(a&&a.target===e)return;pt(t,n),i[n]=e=>o(t,e,r),i[n].target=e;const s="wheel"!==n&&void 0;e.addEventListener(n,i[n],{passive:s})}function gt(t,e){const n=S(t);n.dragStart&&(n.dragging=!0,n.dragEnd=e,t.update("none"))}function yt(t,e){const n=S(t);n.dragStart&&"Escape"===e.key&&(pt(t,"keydown"),n.dragging=!1,n.dragStart=n.dragEnd=null,t.update("none"))}function bt(t,e){if(t.target!==e.canvas){const n=e.canvas.getBoundingClientRect();return{x:t.clientX-n.left,y:t.clientY-n.top}}return(0,d.X)(t,e)}function vt(t,e,n){const{onZoomStart:o,onZoomRejected:i}=n;if(o){const n=bt(e,t);if(!1===(0,d.C)(o,[{chart:t,event:e,point:n}]))return(0,d.C)(i,[{chart:t,event:e}]),!1}}function xt(t,e){if(t.legend){const n=(0,d.X)(e,t);if((0,d.$)(n,t.legend))return}const n=S(t),{pan:o,zoom:i={}}=n.options;if(0!==e.button||g(m(o),e)||y(m(i.drag),e))return(0,d.C)(i.onZoomRejected,[{chart:t,event:e}]);!1!==vt(t,e,i)&&(n.dragStart=e,mt(t,t.canvas.ownerDocument,"mousemove",gt),mt(t,window.document,"keydown",yt))}function wt({begin:t,end:e},n){let o=e.x-t.x,i=e.y-t.y;const r=Math.abs(o/i);r>n?o=Math.sign(o)*Math.abs(i*n):r<n&&(i=Math.sign(i)*Math.abs(o/n)),e.x=t.x+o,e.y=t.y+i}function Tt(t,e,n,{min:o,max:i,prop:r}){t[o]=dt(Math.min(n.begin[r],n.end[r]),e[o],e[i]),t[i]=dt(Math.max(n.begin[r],n.end[r]),e[o],e[i])}function Mt(t,e,n){const o={begin:bt(e.dragStart,t),end:bt(e.dragEnd,t)};if(n){const e=t.chartArea.width/t.chartArea.height;wt(o,e)}return o}function St(t,e,n,o){const i=b(e,"x",t),r=b(e,"y",t),{top:a,left:s,right:c,bottom:u,width:l,height:f}=t.chartArea,h={top:a,left:s,right:c,bottom:u},d=Mt(t,n,o&&i&&r);i&&Tt(h,t.chartArea,d,{min:"left",max:"right",prop:"x"}),r&&Tt(h,t.chartArea,d,{min:"top",max:"bottom",prop:"y"});const p=h.right-h.left,m=h.bottom-h.top;return{...h,width:p,height:m,zoomX:i&&p?1+(l-p)/l:1,zoomY:r&&m?1+(f-m)/f:1}}function Ct(t,e){const n=S(t);if(!n.dragStart)return;pt(t,"mousemove");const{mode:o,onZoomComplete:i,drag:{threshold:r=0,maintainAspectRatio:a}}=n.options.zoom,s=St(t,o,{dragStart:n.dragStart,dragEnd:e},a),c=b(o,"x",t)?s.width:0,u=b(o,"y",t)?s.height:0,l=Math.sqrt(c*c+u*u);if(n.dragStart=n.dragEnd=null,l<=r)return n.dragging=!1,void t.update("none");nt(t,{x:s.left,y:s.top},{x:s.right,y:s.bottom},"zoom","drag"),n.dragging=!1,n.filterNextClick=!0,(0,d.C)(i,[{chart:t}])}function _t(t,e,n){if(y(m(n.wheel),e))(0,d.C)(n.onZoomRejected,[{chart:t,event:e}]);else if(!1!==vt(t,e,n)&&(e.cancelable&&e.preventDefault(),void 0!==e.deltaY))return!0}function kt(t,e){const{handlers:{onZoomComplete:n},options:{zoom:o}}=S(t);if(!_t(t,e,o))return;const i=e.target.getBoundingClientRect(),r=o.wheel.speed,a=e.deltaY>=0?2-1/(1-r):1+r,s={x:a,y:a,focalPoint:{x:e.clientX-i.left,y:e.clientY-i.top}};et(t,s,"zoom","wheel"),(0,d.C)(n,[{chart:t}])}function Et(t,e,n,o){n&&(S(t).handlers[e]=x((()=>(0,d.C)(n,[{chart:t}])),o))}function Ot(t,e){const n=t.canvas,{wheel:o,drag:i,onZoomComplete:r}=e.zoom;o.enabled?(mt(t,n,"wheel",kt),Et(t,"onZoomComplete",r,250)):pt(t,"wheel"),i.enabled?(mt(t,n,"mousedown",xt),mt(t,n.ownerDocument,"mouseup",Ct)):(pt(t,"mousedown"),pt(t,"mousemove"),pt(t,"mouseup"),pt(t,"keydown"))}function Pt(t){pt(t,"mousedown"),pt(t,"mousemove"),pt(t,"mouseup"),pt(t,"wheel"),pt(t,"click"),pt(t,"keydown")}function At(t,e){return function(n,o){const{pan:i,zoom:r={}}=e.options;if(!i||!i.enabled)return!1;const a=o&&o.srcEvent;return!a||(!(!e.panning&&"mouse"===o.pointerType&&(y(m(i),a)||g(m(r.drag),a)))||((0,d.C)(i.onPanRejected,[{chart:t,event:o}]),!1))}}function zt(t,e){const n=Math.abs(t.clientX-e.clientX),o=Math.abs(t.clientY-e.clientY),i=n/o;let r,a;return i>.3&&i<1.7?r=a=!0:n>o?r=!0:a=!0,{x:r,y:a}}function Rt(t,e,n){if(e.scale){const{center:o,pointers:i}=n,r=1/e.scale*n.scale,a=n.target.getBoundingClientRect(),s=zt(i[0],i[1]),c=e.options.zoom.mode,u={x:s.x&&b(c,"x",t)?r:1,y:s.y&&b(c,"y",t)?r:1,focalPoint:{x:o.x-a.left,y:o.y-a.top}};et(t,u,"zoom","pinch"),e.scale=n.scale}}function It(t,e,n){if(e.options.zoom.pinch.enabled){const o=(0,d.X)(n,t);!1===(0,d.C)(e.options.zoom.onZoomStart,[{chart:t,event:n,point:o}])?(e.scale=null,(0,d.C)(e.options.zoom.onZoomRejected,[{chart:t,event:n}])):e.scale=1}}function Dt(t,e,n){e.scale&&(Rt(t,e,n),e.scale=null,(0,d.C)(e.options.zoom.onZoomComplete,[{chart:t}]))}function jt(t,e,n){const o=e.delta;o&&(e.panning=!0,ct(t,{x:n.deltaX-o.x,y:n.deltaY-o.y},e.panScales),e.delta={x:n.deltaX,y:n.deltaY})}function Ft(t,e,n){const{enabled:o,onPanStart:i,onPanRejected:r}=e.options.pan;if(!o)return;const a=n.target.getBoundingClientRect(),s={x:n.center.x-a.left,y:n.center.y-a.top};if(!1===(0,d.C)(i,[{chart:t,event:n,point:s}]))return(0,d.C)(r,[{chart:t,event:n}]);e.panScales=T(e.options.pan,s,t),e.delta={x:0,y:0},jt(t,e,n)}function Yt(t,e){e.delta=null,e.panning&&(e.panning=!1,e.filterNextClick=!0,(0,d.C)(e.options.pan.onPanComplete,[{chart:t}]))}const Nt=new WeakMap;function Lt(t,e){const n=S(t),o=t.canvas,{pan:i,zoom:r}=e,a=new(h().Manager)(o);r&&r.pinch.enabled&&(a.add(new(h().Pinch)),a.on("pinchstart",(e=>It(t,n,e))),a.on("pinch",(e=>Rt(t,n,e))),a.on("pinchend",(e=>Dt(t,n,e)))),i&&i.enabled&&(a.add(new(h().Pan)({threshold:i.threshold,enable:At(t,n)})),a.on("panstart",(e=>Ft(t,n,e))),a.on("panmove",(e=>jt(t,n,e))),a.on("panend",(()=>Yt(t,n)))),Nt.set(t,a)}function Xt(t){const e=Nt.get(t);e&&(e.remove("pinchstart"),e.remove("pinch"),e.remove("pinchend"),e.remove("panstart"),e.remove("pan"),e.remove("panend"),e.destroy(),Nt.delete(t))}function Wt(t,e){const{pan:n,zoom:o}=t,{pan:i,zoom:r}=e;return o?.zoom?.pinch?.enabled!==r?.zoom?.pinch?.enabled||(n?.enabled!==i?.enabled||n?.threshold!==i?.threshold)}var Bt="2.2.0";function Ht(t,e,n){const o=n.zoom.drag,{dragStart:i,dragEnd:r}=S(t);if(o.drawTime!==e||!r)return;const{left:a,top:s,width:c,height:u}=St(t,n.zoom.mode,{dragStart:i,dragEnd:r},o.maintainAspectRatio),l=t.ctx;l.save(),l.beginPath(),l.fillStyle=o.backgroundColor||"rgba(225,225,225,0.3)",l.fillRect(a,s,c,u),o.borderWidth>0&&(l.lineWidth=o.borderWidth,l.strokeStyle=o.borderColor||"rgba(225,225,225)",l.strokeRect(a,s,c,u)),l.restore()}var qt={id:"zoom",version:Bt,defaults:{pan:{enabled:!1,mode:"xy",threshold:10,modifierKey:null},zoom:{wheel:{enabled:!1,speed:.1,modifierKey:null},drag:{enabled:!1,drawTime:"beforeDatasetsDraw",modifierKey:null},pinch:{enabled:!1},mode:"xy"}},start:function(t,e,n){const o=S(t);o.options=n,Object.prototype.hasOwnProperty.call(n.zoom,"enabled")&&p.warn("The option `zoom.enabled` is no longer supported. Please use `zoom.wheel.enabled`, `zoom.drag.enabled`, or `zoom.pinch.enabled`."),(Object.prototype.hasOwnProperty.call(n.zoom,"overScaleMode")||Object.prototype.hasOwnProperty.call(n.pan,"overScaleMode"))&&p.warn("The option `overScaleMode` is deprecated. Please use `scaleMode` instead (and update `mode` as desired)."),h()&&Lt(t,n),t.pan=(e,n,o)=>ct(t,e,n,o),t.zoom=(e,n)=>et(t,e,n),t.zoomRect=(e,n,o)=>nt(t,e,n,o),t.zoomScale=(e,n,o)=>ot(t,e,n,o),t.resetZoom=e=>it(t,e),t.getZoomLevel=()=>at(t),t.getInitialScaleBounds=()=>ut(t),t.getZoomedScaleBounds=()=>lt(t),t.isZoomedOrPanned=()=>ft(t),t.isZoomingOrPanning=()=>ht(t)},beforeEvent(t,{event:e}){if(ht(t))return!1;if("click"===e.type||"mouseup"===e.type){const e=S(t);if(e.filterNextClick)return e.filterNextClick=!1,!1}},beforeUpdate:function(t,e,n){const o=S(t),i=o.options;o.options=n,Wt(i,n)&&(Xt(t),Lt(t,n)),Ot(t,n)},beforeDatasetsDraw(t,e,n){Ht(t,"beforeDatasetsDraw",n)},afterDatasetsDraw(t,e,n){Ht(t,"afterDatasetsDraw",n)},beforeDraw(t,e,n){Ht(t,"beforeDraw",n)},afterDraw(t,e,n){Ht(t,"afterDraw",n)},stop:function(t){Pt(t),h()&&Xt(t),C(t)},panFunctions:V,zoomFunctions:$,zoomRectFunctions:Z};s.t1.register(s.Bs,s.No,s.E8,s.FN,s.A6,s.Jb,s.ju,s.ZT,s.P$,s.G5,s.h9,s.Pz,s.PP,s.kc,s.OJ,s.pr,s.UA,s.iw,s.gO,s.dN,s.s$,s.hE,s.m_,s.tK,qt);var $t=(0,o.pM)({name:"ChartJs",props:{type:c.Ay.string.def("line"),className:c.Ay.string.isRequired.def("bar"),id:c.Ay.string.isRequired.def("bar"),style:c.Ay.object.def({marginBottom:"20px"}),labels:c.Ay.arrayOf(c.Ay.string).def(["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]),height:c.Ay.oneOfType([String,Number]).def(479),scales:c.Ay.object.def({y:{beginAtZero:!0,grid:{color:"#485e9029",borderDash:[3,3],zeroLineColor:"#485e9029",zeroLineWidth:1},ticks:{beginAtZero:!0,fontSize:14,fontFamily:"Jost",color:"#8C90A4",max:80,stepStartValue:5,stepSize:20,padding:10,callback(t){return`${t}k`}}},x:{grid:{display:!1,drawBorder:!1,zeroLineWidth:0,color:"transparent",z:1},ticks:{beginAtZero:!0,fontSize:14,fontFamily:"Jost",color:"#8C90A4"}}}),datasets:c.Ay.arrayOf(c.Ay.object).def([{data:[20,60,50,45,50,60,70,40,45,35,25,30],backgroundColor:"#001737",barPercentage:.6,label:"Profit"},{data:[10,40,30,40,60,55,45,35,30,20,15,20],backgroundColor:"#1ce1ac",barPercentage:.6,label:"Lose"}]),layout:c.Ay.object.def({}),legend:c.Ay.object.def({display:!1,labels:{display:!1,position:"center"}}),elements:c.Ay.object.def({line:{tension:.6,borderCapStyle:"round",borderJoinStyle:"round",capBezierPoints:!0},point:{radius:0,z:5}}),options:c.Ay.object.def({}),tooltip:c.Ay.object.def({callbacks:{label(t){const e=t.dataset.label,{formattedValue:n}=t;return`${e}:  ${n} `},labelColor(t){return{backgroundColor:t.dataset.hoverBackgroundColor,borderColor:"transparent"}}}})},setup(t){const{type:e,datasets:n,options:i,labels:r,id:a,tooltip:c,scales:f,elements:h,legend:d,layout:p}=(0,u.QW)(t);let m=null;(0,o.wB)((()=>n.value),(()=>{const t=document.getElementById(`${a.value}`),e=s.t1.getChart(t);e&&(e.data.labels=r.value,e.data.datasets=n.value,e.update())})),(0,o.nT)((()=>{(0,o.dY)((()=>{const t=document.getElementById(`${a.value}`);m=new s.t1(t,{type:e.value,data:{labels:r.value,datasets:n.value},options:{responsive:!0,maintainAspectRatio:!0,layout:p,hover:{mode:"index",intersect:!1},plugins:{legend:d.value,tooltip:{yAlign:"bottom",mode:"index",intersect:!1,backgroundColor:"#ffffff",boxShadow:"0 8px 5px #ADB5D915",position:"average",titleColor:"#ADB5D9",color:"#ADB5D9",titleFontSize:12,titleSpacing:10,bodyColor:"#404040",bodyFontSize:11,bodyFontStyle:"normal",bodyFontFamily:"'Jost', sans-serif",borderColor:"#F1F2F6",usePointStyle:!0,borderWidth:1,bodySpacing:10,padding:{x:10,y:8},z:999999,enabled:!1,external:l.so,...c.value}},elements:h.value,scales:f.value,...i.value}})})),(0,o.xo)((()=>{m&&m.destroy()}))}))}}),Zt=n(66262);const Vt=(0,Zt.A)($t,[["render",a]]);var Qt=Vt},27615:function(t,e,n){"use strict";n.d(e,{so:function(){return o}});n(18111),n(7588),n(61701);const o=function(t){let e=document.querySelector(".chartjs-tooltip");this._chart.canvas.closest(".ninjadash-chart-container").contains(e)||(e=document.createElement("div"),e.className="chartjs-tooltip",e.innerHTML="<table></table>",document.querySelectorAll(".ninjadash-chart-container").forEach((t=>{t.contains(document.querySelector(".chartjs-tooltip"))&&document.querySelector(".chartjs-tooltip").remove()})),this._chart.canvas.closest(".ninjadash-chart-container").appendChild(e));const n=t.tooltip;if(0===n.opacity)return void(e.style.opacity=0);function o(t){return t.lines}if(e.classList.remove("above","below","no-transform"),n.yAlign?e.classList.add(n.yAlign):e.classList.add("no-transform"),n.body){const t=n.title||[],i=n.body.map(o);let r="<thead>";t.forEach((function(t){r+=`<div class='tooltip-title'>${t}</div>`})),r+="</thead><tbody>",i.forEach((function(t,e){const o=n.labelColors[e];let i=`background:${o.backgroundColor}`;i+=`; border-color:${o.borderColor}`,i+="; border-width: 2px",i+="; border-radius: 30px";const a=`<span class="chartjs-tooltip-key" style="${i}"></span>`;r+=`<tr><td>${a}${t}</td></tr>`})),r+="</tbody>";const a=e.querySelector("table");a.innerHTML=r}const i=this._chart.canvas.offsetTop,r=this._chart.canvas.offsetLeft,a=document.querySelector(".chartjs-tooltip"),s=a.clientHeight;e.style.opacity=1,e.style.left=`${r+n.caretX}px`,e.style.top=i+n.caretY-(n.caretY>10?s>100?s+5:s+15:70)+"px",e.style.fontFamily=n.options.bodyFontFamily,e.style.fontSize=`${n.options.bodyFontSize}px`,e.style.fontStyle=n.options.bodyFontStyle,e.style.padding=`${n.yPadding}px ${n.xPadding}px`}},32998:function(t,e,n){"use strict";n.d(e,{$:function(){return Re},A:function(){return B},B:function(){return l},C:function(){return p},D:function(){return Je},E:function(){return nt},F:function(){return Q},G:function(){return ke},H:function(){return F},I:function(){return ot},J:function(){return Oe},K:function(){return Qe},L:function(){return Ie},M:function(){return Ye},N:function(){return De},O:function(){return Ue},P:function(){return z},Q:function(){return m},R:function(){return gt},S:function(){return yt},T:function(){return R},U:function(){return xe},V:function(){return x},W:function(){return k},X:function(){return Ln},Y:function(){return st},Z:function(){return at},_:function(){return ht},a:function(){return Ke},a0:function(){return K},a1:function(){return Bn},a2:function(){return zn},a3:function(){return $n},a4:function(){return pt},a5:function(){return qn},a6:function(){return An},a7:function(){return we},a8:function(){return O},a9:function(){return en},aA:function(){return eo},aB:function(){return no},aC:function(){return bt},aD:function(){return oo},aE:function(){return ze},aF:function(){return i},aG:function(){return J},aH:function(){return Z},aI:function(){return W},aJ:function(){return $},aK:function(){return q},aL:function(){return U},aM:function(){return Ee},aN:function(){return ct},aO:function(){return rt},aa:function(){return tn},ab:function(){return nn},ac:function(){return w},ad:function(){return r},ae:function(){return mt},af:function(){return Hn},ag:function(){return Pe},ah:function(){return P},ai:function(){return g},aj:function(){return A},ak:function(){return it},al:function(){return $e},am:function(){return Pn},an:function(){return fo},ao:function(){return co},ap:function(){return Vn},aq:function(){return Qn},ar:function(){return Zn},as:function(){return je},at:function(){return Fe},au:function(){return Ae},av:function(){return Xe},aw:function(){return Ze},ax:function(){return Ve},ay:function(){return so},az:function(){return tt},b:function(){return s},c:function(){return be},d:function(){return Ce},e:function(){return St},f:function(){return S},g:function(){return u},h:function(){return Ge},i:function(){return c},j:function(){return E},k:function(){return a},l:function(){return lt},m:function(){return h},n:function(){return d},o:function(){return Jn},p:function(){return et},q:function(){return vt},r:function(){return dt},s:function(){return X},t:function(){return V},u:function(){return ft},v:function(){return f},w:function(){return xt},x:function(){return H},y:function(){return xn},z:function(){return L}});var o=n(96763);
/*!
 * Chart.js v3.9.1
 * https://www.chartjs.org
 * (c) 2022 Chart.js Contributors
 * Released under the MIT License
 */function i(){}const r=function(){let t=0;return function(){return t++}}();function a(t){return null===t||"undefined"===typeof t}function s(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function c(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}const u=t=>("number"===typeof t||t instanceof Number)&&isFinite(+t);function l(t,e){return u(t)?t:e}function f(t,e){return"undefined"===typeof t?e:t}const h=(t,e)=>"string"===typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,d=(t,e)=>"string"===typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function p(t,e,n){if(t&&"function"===typeof t.call)return t.apply(n,e)}function m(t,e,n,o){let i,r,a;if(s(t))if(r=t.length,o)for(i=r-1;i>=0;i--)e.call(n,t[i],i);else for(i=0;i<r;i++)e.call(n,t[i],i);else if(c(t))for(a=Object.keys(t),r=a.length,i=0;i<r;i++)e.call(n,t[a[i]],a[i])}function g(t,e){let n,o,i,r;if(!t||!e||t.length!==e.length)return!1;for(n=0,o=t.length;n<o;++n)if(i=t[n],r=e[n],i.datasetIndex!==r.datasetIndex||i.index!==r.index)return!1;return!0}function y(t){if(s(t))return t.map(y);if(c(t)){const e=Object.create(null),n=Object.keys(t),o=n.length;let i=0;for(;i<o;++i)e[n[i]]=y(t[n[i]]);return e}return t}function b(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function v(t,e,n,o){if(!b(t))return;const i=e[t],r=n[t];c(i)&&c(r)?x(i,r,o):e[t]=y(r)}function x(t,e,n){const o=s(e)?e:[e],i=o.length;if(!c(t))return t;n=n||{};const r=n.merger||v;for(let a=0;a<i;++a){if(e=o[a],!c(e))continue;const i=Object.keys(e);for(let o=0,a=i.length;o<a;++o)r(i[o],t,e,n)}return t}function w(t,e){return x(t,e,{merger:T})}function T(t,e,n){if(!b(t))return;const o=e[t],i=n[t];c(o)&&c(i)?w(o,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=y(i))}const M={"":t=>t,x:t=>t.x,y:t=>t.y};function S(t,e){const n=M[e]||(M[e]=C(e));return n(t)}function C(t){const e=_(t);return t=>{for(const n of e){if(""===n)break;t=t&&t[n]}return t}}function _(t){const e=t.split("."),n=[];let o="";for(const i of e)o+=i,o.endsWith("\\")?o=o.slice(0,-1)+".":(n.push(o),o="");return n}function k(t){return t.charAt(0).toUpperCase()+t.slice(1)}const E=t=>"undefined"!==typeof t,O=t=>"function"===typeof t,P=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function A(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}const z=Math.PI,R=2*z,I=R+z,D=Number.POSITIVE_INFINITY,j=z/180,F=z/2,Y=z/4,N=2*z/3,L=Math.log10,X=Math.sign;function W(t){const e=Math.round(t);t=q(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(L(t))),o=t/n,i=o<=1?1:o<=2?2:o<=5?5:10;return i*n}function B(t){const e=[],n=Math.sqrt(t);let o;for(o=1;o<n;o++)t%o===0&&(e.push(o),e.push(t/o));return n===(0|n)&&e.push(n),e.sort(((t,e)=>t-e)).pop(),e}function H(t){return!isNaN(parseFloat(t))&&isFinite(t)}function q(t,e,n){return Math.abs(t-e)<n}function $(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function Z(t,e,n){let o,i,r;for(o=0,i=t.length;o<i;o++)r=t[o][n],isNaN(r)||(e.min=Math.min(e.min,r),e.max=Math.max(e.max,r))}function V(t){return t*(z/180)}function Q(t){return t*(180/z)}function U(t){if(!u(t))return;let e=1,n=0;while(Math.round(t*e)/e!==t)e*=10,n++;return n}function K(t,e){const n=e.x-t.x,o=e.y-t.y,i=Math.sqrt(n*n+o*o);let r=Math.atan2(o,n);return r<-.5*z&&(r+=R),{angle:r,distance:i}}function J(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function G(t,e){return(t-e+I)%R-z}function tt(t){return(t%R+R)%R}function et(t,e,n,o){const i=tt(t),r=tt(e),a=tt(n),s=tt(r-i),c=tt(a-i),u=tt(i-r),l=tt(i-a);return i===r||i===a||o&&r===a||s>c&&u<l}function nt(t,e,n){return Math.max(e,Math.min(n,t))}function ot(t){return nt(t,-32768,32767)}function it(t,e,n,o=1e-6){return t>=Math.min(e,n)-o&&t<=Math.max(e,n)+o}function rt(t,e,n){n=n||(n=>t[n]<e);let o,i=t.length-1,r=0;while(i-r>1)o=r+i>>1,n(o)?r=o:i=o;return{lo:r,hi:i}}const at=(t,e,n,o)=>rt(t,n,o?o=>t[o][e]<=n:o=>t[o][e]<n),st=(t,e,n)=>rt(t,n,(o=>t[o][e]>=n));function ct(t,e,n){let o=0,i=t.length;while(o<i&&t[o]<e)o++;while(i>o&&t[i-1]>n)i--;return o>0||i<t.length?t.slice(o,i):t}const ut=["push","pop","shift","splice","unshift"];function lt(t,e){t._chartjs?t._chartjs.listeners.push(e):(Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),ut.forEach((e=>{const n="_onData"+k(e),o=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){const i=o.apply(this,e);return t._chartjs.listeners.forEach((t=>{"function"===typeof t[n]&&t[n](...e)})),i}})})))}function ft(t,e){const n=t._chartjs;if(!n)return;const o=n.listeners,i=o.indexOf(e);-1!==i&&o.splice(i,1),o.length>0||(ut.forEach((e=>{delete t[e]})),delete t._chartjs)}function ht(t){const e=new Set;let n,o;for(n=0,o=t.length;n<o;++n)e.add(t[n]);return e.size===o?t:Array.from(e)}const dt=function(){return"undefined"===typeof window?function(t){return t()}:window.requestAnimationFrame}();function pt(t,e,n){const o=n||(t=>Array.prototype.slice.call(t));let i=!1,r=[];return function(...n){r=o(n),i||(i=!0,dt.call(window,(()=>{i=!1,t.apply(e,r)})))}}function mt(t,e){let n;return function(...o){return e?(clearTimeout(n),n=setTimeout(t,e,o)):t.apply(this,o),e}}const gt=t=>"start"===t?"left":"end"===t?"right":"center",yt=(t,e,n)=>"start"===t?e:"end"===t?n:(e+n)/2,bt=(t,e,n,o)=>{const i=o?"left":"right";return t===i?n:"center"===t?(e+n)/2:e};function vt(t,e,n){const o=e.length;let i=0,r=o;if(t._sorted){const{iScale:a,_parsed:s}=t,c=a.axis,{min:u,max:l,minDefined:f,maxDefined:h}=a.getUserBounds();f&&(i=nt(Math.min(at(s,a.axis,u).lo,n?o:at(e,c,a.getPixelForValue(u)).lo),0,o-1)),r=h?nt(Math.max(at(s,a.axis,l,!0).hi+1,n?0:at(e,c,a.getPixelForValue(l),!0).hi+1),i,o)-i:o-i}return{start:i,count:r}}function xt(t){const{xScale:e,yScale:n,_scaleRanges:o}=t,i={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!o)return t._scaleRanges=i,!0;const r=o.xmin!==e.min||o.xmax!==e.max||o.ymin!==n.min||o.ymax!==n.max;return Object.assign(o,i),r}const wt=t=>0===t||1===t,Tt=(t,e,n)=>-Math.pow(2,10*(t-=1))*Math.sin((t-e)*R/n),Mt=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*R/n)+1,St={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*F),easeOutSine:t=>Math.sin(t*F),easeInOutSine:t=>-.5*(Math.cos(z*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>wt(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>wt(t)?t:Tt(t,.075,.3),easeOutElastic:t=>wt(t)?t:Mt(t,.075,.3),easeInOutElastic(t){const e=.1125,n=.45;return wt(t)?t:t<.5?.5*Tt(2*t,e,n):.5+.5*Mt(2*t-1,e,n)},easeInBack(t){const e=1.70158;return t*t*((e+1)*t-e)},easeOutBack(t){const e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-St.easeOutBounce(1-t),easeOutBounce(t){const e=7.5625,n=2.75;return t<1/n?e*t*t:t<2/n?e*(t-=1.5/n)*t+.75:t<2.5/n?e*(t-=2.25/n)*t+.9375:e*(t-=2.625/n)*t+.984375},easeInOutBounce:t=>t<.5?.5*St.easeInBounce(2*t):.5*St.easeOutBounce(2*t-1)+.5};
/*!
 * @kurkle/color v0.2.1
 * https://github.com/kurkle/color#readme
 * (c) 2022 Jukka Kurkela
 * Released under the MIT License
 */
function Ct(t){return t+.5|0}const _t=(t,e,n)=>Math.max(Math.min(t,n),e);function kt(t){return _t(Ct(2.55*t),0,255)}function Et(t){return _t(Ct(255*t),0,255)}function Ot(t){return _t(Ct(t/2.55)/100,0,1)}function Pt(t){return _t(Ct(100*t),0,100)}const At={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},zt=[..."0123456789ABCDEF"],Rt=t=>zt[15&t],It=t=>zt[(240&t)>>4]+zt[15&t],Dt=t=>(240&t)>>4===(15&t),jt=t=>Dt(t.r)&&Dt(t.g)&&Dt(t.b)&&Dt(t.a);function Ft(t){var e,n=t.length;return"#"===t[0]&&(4===n||5===n?e={r:255&17*At[t[1]],g:255&17*At[t[2]],b:255&17*At[t[3]],a:5===n?17*At[t[4]]:255}:7!==n&&9!==n||(e={r:At[t[1]]<<4|At[t[2]],g:At[t[3]]<<4|At[t[4]],b:At[t[5]]<<4|At[t[6]],a:9===n?At[t[7]]<<4|At[t[8]]:255})),e}const Yt=(t,e)=>t<255?e(t):"";function Nt(t){var e=jt(t)?Rt:It;return t?"#"+e(t.r)+e(t.g)+e(t.b)+Yt(t.a,e):void 0}const Lt=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Xt(t,e,n){const o=e*Math.min(n,1-n),i=(e,i=(e+t/30)%12)=>n-o*Math.max(Math.min(i-3,9-i,1),-1);return[i(0),i(8),i(4)]}function Wt(t,e,n){const o=(o,i=(o+t/60)%6)=>n-n*e*Math.max(Math.min(i,4-i,1),0);return[o(5),o(3),o(1)]}function Bt(t,e,n){const o=Xt(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)o[i]*=1-e-n,o[i]+=e;return o}function Ht(t,e,n,o,i){return t===i?(e-n)/o+(e<n?6:0):e===i?(n-t)/o+2:(t-e)/o+4}function qt(t){const e=255,n=t.r/e,o=t.g/e,i=t.b/e,r=Math.max(n,o,i),a=Math.min(n,o,i),s=(r+a)/2;let c,u,l;return r!==a&&(l=r-a,u=s>.5?l/(2-r-a):l/(r+a),c=Ht(n,o,i,l,r),c=60*c+.5),[0|c,u||0,s]}function $t(t,e,n,o){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,o)).map(Et)}function Zt(t,e,n){return $t(Xt,t,e,n)}function Vt(t,e,n){return $t(Bt,t,e,n)}function Qt(t,e,n){return $t(Wt,t,e,n)}function Ut(t){return(t%360+360)%360}function Kt(t){const e=Lt.exec(t);let n,o=255;if(!e)return;e[5]!==n&&(o=e[6]?kt(+e[5]):Et(+e[5]));const i=Ut(+e[2]),r=+e[3]/100,a=+e[4]/100;return n="hwb"===e[1]?Vt(i,r,a):"hsv"===e[1]?Qt(i,r,a):Zt(i,r,a),{r:n[0],g:n[1],b:n[2],a:o}}function Jt(t,e){var n=qt(t);n[0]=Ut(n[0]+e),n=Zt(n),t.r=n[0],t.g=n[1],t.b=n[2]}function Gt(t){if(!t)return;const e=qt(t),n=e[0],o=Pt(e[1]),i=Pt(e[2]);return t.a<255?`hsla(${n}, ${o}%, ${i}%, ${Ot(t.a)})`:`hsl(${n}, ${o}%, ${i}%)`}const te={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ee={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function ne(){const t={},e=Object.keys(ee),n=Object.keys(te);let o,i,r,a,s;for(o=0;o<e.length;o++){for(a=s=e[o],i=0;i<n.length;i++)r=n[i],s=s.replace(r,te[r]);r=parseInt(ee[a],16),t[s]=[r>>16&255,r>>8&255,255&r]}return t}let oe;function ie(t){oe||(oe=ne(),oe.transparent=[0,0,0,0]);const e=oe[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}const re=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function ae(t){const e=re.exec(t);let n,o,i,r=255;if(e){if(e[7]!==n){const t=+e[7];r=e[8]?kt(t):_t(255*t,0,255)}return n=+e[1],o=+e[3],i=+e[5],n=255&(e[2]?kt(n):_t(n,0,255)),o=255&(e[4]?kt(o):_t(o,0,255)),i=255&(e[6]?kt(i):_t(i,0,255)),{r:n,g:o,b:i,a:r}}}function se(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Ot(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const ce=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,ue=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function le(t,e,n){const o=ue(Ot(t.r)),i=ue(Ot(t.g)),r=ue(Ot(t.b));return{r:Et(ce(o+n*(ue(Ot(e.r))-o))),g:Et(ce(i+n*(ue(Ot(e.g))-i))),b:Et(ce(r+n*(ue(Ot(e.b))-r))),a:t.a+n*(e.a-t.a)}}function fe(t,e,n){if(t){let o=qt(t);o[e]=Math.max(0,Math.min(o[e]+o[e]*n,0===e?360:1)),o=Zt(o),t.r=o[0],t.g=o[1],t.b=o[2]}}function he(t,e){return t?Object.assign(e||{},t):t}function de(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=Et(t[3]))):(e=he(t,{r:0,g:0,b:0,a:1}),e.a=Et(e.a)),e}function pe(t){return"r"===t.charAt(0)?ae(t):Kt(t)}class me{constructor(t){if(t instanceof me)return t;const e=typeof t;let n;"object"===e?n=de(t):"string"===e&&(n=Ft(t)||ie(t)||pe(t)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var t=he(this._rgb);return t&&(t.a=Ot(t.a)),t}set rgb(t){this._rgb=de(t)}rgbString(){return this._valid?se(this._rgb):void 0}hexString(){return this._valid?Nt(this._rgb):void 0}hslString(){return this._valid?Gt(this._rgb):void 0}mix(t,e){if(t){const n=this.rgb,o=t.rgb;let i;const r=e===i?.5:e,a=2*r-1,s=n.a-o.a,c=((a*s===-1?a:(a+s)/(1+a*s))+1)/2;i=1-c,n.r=255&c*n.r+i*o.r+.5,n.g=255&c*n.g+i*o.g+.5,n.b=255&c*n.b+i*o.b+.5,n.a=r*n.a+(1-r)*o.a,this.rgb=n}return this}interpolate(t,e){return t&&(this._rgb=le(this._rgb,t._rgb,e)),this}clone(){return new me(this.rgb)}alpha(t){return this._rgb.a=Et(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=Ct(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return fe(this._rgb,2,t),this}darken(t){return fe(this._rgb,2,-t),this}saturate(t){return fe(this._rgb,1,t),this}desaturate(t){return fe(this._rgb,1,-t),this}rotate(t){return Jt(this._rgb,t),this}}function ge(t){return new me(t)}function ye(t){if(t&&"object"===typeof t){const e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function be(t){return ye(t)?t:ge(t)}function ve(t){return ye(t)?t:ge(t).saturate(.5).darken(.1).hexString()}const xe=Object.create(null),we=Object.create(null);function Te(t,e){if(!e)return t;const n=e.split(".");for(let o=0,i=n.length;o<i;++o){const e=n[o];t=t[e]||(t[e]=Object.create(null))}return t}function Me(t,e,n){return"string"===typeof e?x(Te(t,e),n):x(Te(t,""),e)}class Se{constructor(t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>ve(e.backgroundColor),this.hoverBorderColor=(t,e)=>ve(e.borderColor),this.hoverColor=(t,e)=>ve(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t)}set(t,e){return Me(this,t,e)}get(t){return Te(this,t)}describe(t,e){return Me(we,t,e)}override(t,e){return Me(xe,t,e)}route(t,e,n,o){const i=Te(this,t),r=Te(this,n),a="_"+e;Object.defineProperties(i,{[a]:{value:i[e],writable:!0},[e]:{enumerable:!0,get(){const t=this[a],e=r[o];return c(t)?Object.assign({},e,t):f(t,e)},set(t){this[a]=t}}})}}var Ce=new Se({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}});function _e(t){return!t||a(t.size)||a(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function ke(t,e,n,o,i){let r=e[i];return r||(r=e[i]=t.measureText(i).width,n.push(i)),r>o&&(o=r),o}function Ee(t,e,n,o){o=o||{};let i=o.data=o.data||{},r=o.garbageCollect=o.garbageCollect||[];o.font!==e&&(i=o.data={},r=o.garbageCollect=[],o.font=e),t.save(),t.font=e;let a=0;const c=n.length;let u,l,f,h,d;for(u=0;u<c;u++)if(h=n[u],void 0!==h&&null!==h&&!0!==s(h))a=ke(t,i,r,a,h);else if(s(h))for(l=0,f=h.length;l<f;l++)d=h[l],void 0===d||null===d||s(d)||(a=ke(t,i,r,a,d));t.restore();const p=r.length/2;if(p>n.length){for(u=0;u<p;u++)delete i[r[u]];r.splice(0,p)}return a}function Oe(t,e,n){const o=t.currentDevicePixelRatio,i=0!==n?Math.max(n/2,.5):0;return Math.round((e-i)*o)/o+i}function Pe(t,e){e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore()}function Ae(t,e,n,o){ze(t,e,n,o,null)}function ze(t,e,n,o,i){let r,a,s,c,u,l;const f=e.pointStyle,h=e.rotation,d=e.radius;let p=(h||0)*j;if(f&&"object"===typeof f&&(r=f.toString(),"[object HTMLImageElement]"===r||"[object HTMLCanvasElement]"===r))return t.save(),t.translate(n,o),t.rotate(p),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void t.restore();if(!(isNaN(d)||d<=0)){switch(t.beginPath(),f){default:i?t.ellipse(n,o,i/2,d,0,0,R):t.arc(n,o,d,0,R),t.closePath();break;case"triangle":t.moveTo(n+Math.sin(p)*d,o-Math.cos(p)*d),p+=N,t.lineTo(n+Math.sin(p)*d,o-Math.cos(p)*d),p+=N,t.lineTo(n+Math.sin(p)*d,o-Math.cos(p)*d),t.closePath();break;case"rectRounded":u=.516*d,c=d-u,a=Math.cos(p+Y)*c,s=Math.sin(p+Y)*c,t.arc(n-a,o-s,u,p-z,p-F),t.arc(n+s,o-a,u,p-F,p),t.arc(n+a,o+s,u,p,p+F),t.arc(n-s,o+a,u,p+F,p+z),t.closePath();break;case"rect":if(!h){c=Math.SQRT1_2*d,l=i?i/2:c,t.rect(n-l,o-c,2*l,2*c);break}p+=Y;case"rectRot":a=Math.cos(p)*d,s=Math.sin(p)*d,t.moveTo(n-a,o-s),t.lineTo(n+s,o-a),t.lineTo(n+a,o+s),t.lineTo(n-s,o+a),t.closePath();break;case"crossRot":p+=Y;case"cross":a=Math.cos(p)*d,s=Math.sin(p)*d,t.moveTo(n-a,o-s),t.lineTo(n+a,o+s),t.moveTo(n+s,o-a),t.lineTo(n-s,o+a);break;case"star":a=Math.cos(p)*d,s=Math.sin(p)*d,t.moveTo(n-a,o-s),t.lineTo(n+a,o+s),t.moveTo(n+s,o-a),t.lineTo(n-s,o+a),p+=Y,a=Math.cos(p)*d,s=Math.sin(p)*d,t.moveTo(n-a,o-s),t.lineTo(n+a,o+s),t.moveTo(n+s,o-a),t.lineTo(n-s,o+a);break;case"line":a=i?i/2:Math.cos(p)*d,s=Math.sin(p)*d,t.moveTo(n-a,o-s),t.lineTo(n+a,o+s);break;case"dash":t.moveTo(n,o),t.lineTo(n+Math.cos(p)*d,o+Math.sin(p)*d);break}t.fill(),e.borderWidth>0&&t.stroke()}}function Re(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function Ie(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function De(t){t.restore()}function je(t,e,n,o,i){if(!e)return t.lineTo(n.x,n.y);if("middle"===i){const o=(e.x+n.x)/2;t.lineTo(o,e.y),t.lineTo(o,n.y)}else"after"===i!==!!o?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function Fe(t,e,n,o){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(o?e.cp1x:e.cp2x,o?e.cp1y:e.cp2y,o?n.cp2x:n.cp1x,o?n.cp2y:n.cp1y,n.x,n.y)}function Ye(t,e,n,o,i,r={}){const c=s(e)?e:[e],u=r.strokeWidth>0&&""!==r.strokeColor;let l,f;for(t.save(),t.font=i.string,Ne(t,r),l=0;l<c.length;++l)f=c[l],u&&(r.strokeColor&&(t.strokeStyle=r.strokeColor),a(r.strokeWidth)||(t.lineWidth=r.strokeWidth),t.strokeText(f,n,o,r.maxWidth)),t.fillText(f,n,o,r.maxWidth),Le(t,n,o,f,r),o+=i.lineHeight;t.restore()}function Ne(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),a(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function Le(t,e,n,o,i){if(i.strikethrough||i.underline){const r=t.measureText(o),a=e-r.actualBoundingBoxLeft,s=e+r.actualBoundingBoxRight,c=n-r.actualBoundingBoxAscent,u=n+r.actualBoundingBoxDescent,l=i.strikethrough?(c+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(a,l),t.lineTo(s,l),t.stroke()}}function Xe(t,e){const{x:n,y:o,w:i,h:r,radius:a}=e;t.arc(n+a.topLeft,o+a.topLeft,a.topLeft,-F,z,!0),t.lineTo(n,o+r-a.bottomLeft),t.arc(n+a.bottomLeft,o+r-a.bottomLeft,a.bottomLeft,z,F,!0),t.lineTo(n+i-a.bottomRight,o+r),t.arc(n+i-a.bottomRight,o+r-a.bottomRight,a.bottomRight,F,0,!0),t.lineTo(n+i,o+a.topRight),t.arc(n+i-a.topRight,o+a.topRight,a.topRight,0,-F,!0),t.lineTo(n+a.topLeft,o)}const We=new RegExp(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/),Be=new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);function He(t,e){const n=(""+t).match(We);if(!n||"normal"===n[1])return 1.2*e;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const qe=t=>+t||0;function $e(t,e){const n={},o=c(e),i=o?Object.keys(e):e,r=c(t)?o?n=>f(t[n],t[e[n]]):e=>t[e]:()=>t;for(const a of i)n[a]=qe(r(a));return n}function Ze(t){return $e(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Ve(t){return $e(t,["topLeft","topRight","bottomLeft","bottomRight"])}function Qe(t){const e=Ze(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Ue(t,e){t=t||{},e=e||Ce.font;let n=f(t.size,e.size);"string"===typeof n&&(n=parseInt(n,10));let i=f(t.style,e.style);i&&!(""+i).match(Be)&&(o.warn('Invalid font style specified: "'+i+'"'),i="");const r={family:f(t.family,e.family),lineHeight:He(f(t.lineHeight,e.lineHeight),n),size:n,style:i,weight:f(t.weight,e.weight),string:""};return r.string=_e(r),r}function Ke(t,e,n,o){let i,r,a,c=!0;for(i=0,r=t.length;i<r;++i)if(a=t[i],void 0!==a&&(void 0!==e&&"function"===typeof a&&(a=a(e),c=!1),void 0!==n&&s(a)&&(a=a[n%a.length],c=!1),void 0!==a))return o&&!c&&(o.cacheable=!1),a}function Je(t,e,n){const{min:o,max:i}=t,r=d(e,(i-o)/2),a=(t,e)=>n&&0===t?0:t+e;return{min:a(o,-Math.abs(r)),max:a(i,r)}}function Ge(t,e){return Object.assign(Object.create(t),e)}function tn(t,e=[""],n=t,o,i=()=>t[0]){E(o)||(o=yn("_fallback",t));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:o,_getTarget:i,override:i=>tn([i,...t],e,n,o)};return new Proxy(r,{deleteProperty(e,n){return delete e[n],delete e._keys,delete t[0][n],!0},get(n,o){return an(n,o,(()=>gn(o,e,t,n)))},getOwnPropertyDescriptor(t,e){return Reflect.getOwnPropertyDescriptor(t._scopes[0],e)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(t,e){return bn(t).includes(e)},ownKeys(t){return bn(t)},set(t,e,n){const o=t._storage||(t._storage=i());return t[e]=o[e]=n,delete t._keys,!0}})}function en(t,e,n,o){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:nn(t,o),setContext:e=>en(t,e,n,o),override:i=>en(t.override(i),e,n,o)};return new Proxy(i,{deleteProperty(e,n){return delete e[n],delete t[n],!0},get(t,e,n){return an(t,e,(()=>sn(t,e,n)))},getOwnPropertyDescriptor(e,n){return e._descriptors.allKeys?Reflect.has(t,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,n)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(e,n){return Reflect.has(t,n)},ownKeys(){return Reflect.ownKeys(t)},set(e,n,o){return t[n]=o,delete e[n],!0}})}function nn(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:o=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:o,isScriptable:O(n)?n:()=>n,isIndexable:O(o)?o:()=>o}}const on=(t,e)=>t?t+k(e):e,rn=(t,e)=>c(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function an(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e))return t[e];const o=n();return t[e]=o,o}function sn(t,e,n){const{_proxy:o,_context:i,_subProxy:r,_descriptors:a}=t;let c=o[e];return O(c)&&a.isScriptable(e)&&(c=cn(e,c,t,n)),s(c)&&c.length&&(c=un(e,c,t,a.isIndexable)),rn(e,c)&&(c=en(c,i,r&&r[e],a)),c}function cn(t,e,n,o){const{_proxy:i,_context:r,_subProxy:a,_stack:s}=n;if(s.has(t))throw new Error("Recursion detected: "+Array.from(s).join("->")+"->"+t);return s.add(t),e=e(r,a||o),s.delete(t),rn(t,e)&&(e=dn(i._scopes,i,t,e)),e}function un(t,e,n,o){const{_proxy:i,_context:r,_subProxy:a,_descriptors:s}=n;if(E(r.index)&&o(t))e=e[r.index%e.length];else if(c(e[0])){const n=e,o=i._scopes.filter((t=>t!==n));e=[];for(const c of n){const n=dn(o,i,t,c);e.push(en(n,r,a&&a[t],s))}}return e}function ln(t,e,n){return O(t)?t(e,n):t}const fn=(t,e)=>!0===t?e:"string"===typeof t?S(e,t):void 0;function hn(t,e,n,o,i){for(const r of e){const e=fn(n,r);if(e){t.add(e);const r=ln(e._fallback,n,i);if(E(r)&&r!==n&&r!==o)return r}else if(!1===e&&E(o)&&n!==o)return null}return!1}function dn(t,e,n,o){const i=e._rootScopes,r=ln(e._fallback,n,o),a=[...t,...i],s=new Set;s.add(o);let c=pn(s,a,n,r||n,o);return null!==c&&((!E(r)||r===n||(c=pn(s,a,r,c,o),null!==c))&&tn(Array.from(s),[""],i,r,(()=>mn(e,n,o))))}function pn(t,e,n,o,i){while(n)n=hn(t,e,n,o,i);return n}function mn(t,e,n){const o=t._getTarget();e in o||(o[e]={});const i=o[e];return s(i)&&c(n)?n:i}function gn(t,e,n,o){let i;for(const r of e)if(i=yn(on(r,t),n),E(i))return rn(t,i)?dn(n,o,t,i):i}function yn(t,e){for(const n of e){if(!n)continue;const e=n[t];if(E(e))return e}}function bn(t){let e=t._keys;return e||(e=t._keys=vn(t._scopes)),e}function vn(t){const e=new Set;for(const n of t)for(const t of Object.keys(n).filter((t=>!t.startsWith("_"))))e.add(t);return Array.from(e)}function xn(t,e,n,o){const{iScale:i}=t,{key:r="r"}=this._parsing,a=new Array(o);let s,c,u,l;for(s=0,c=o;s<c;++s)u=s+n,l=e[u],a[s]={r:i.parse(S(l,r),u)};return a}const wn=Number.EPSILON||1e-14,Tn=(t,e)=>e<t.length&&!t[e].skip&&t[e],Mn=t=>"x"===t?"y":"x";function Sn(t,e,n,o){const i=t.skip?e:t,r=e,a=n.skip?e:n,s=J(r,i),c=J(a,r);let u=s/(s+c),l=c/(s+c);u=isNaN(u)?0:u,l=isNaN(l)?0:l;const f=o*u,h=o*l;return{previous:{x:r.x-f*(a.x-i.x),y:r.y-f*(a.y-i.y)},next:{x:r.x+h*(a.x-i.x),y:r.y+h*(a.y-i.y)}}}function Cn(t,e,n){const o=t.length;let i,r,a,s,c,u=Tn(t,0);for(let l=0;l<o-1;++l)c=u,u=Tn(t,l+1),c&&u&&(q(e[l],0,wn)?n[l]=n[l+1]=0:(i=n[l]/e[l],r=n[l+1]/e[l],s=Math.pow(i,2)+Math.pow(r,2),s<=9||(a=3/Math.sqrt(s),n[l]=i*a*e[l],n[l+1]=r*a*e[l])))}function _n(t,e,n="x"){const o=Mn(n),i=t.length;let r,a,s,c=Tn(t,0);for(let u=0;u<i;++u){if(a=s,s=c,c=Tn(t,u+1),!s)continue;const i=s[n],l=s[o];a&&(r=(i-a[n])/3,s[`cp1${n}`]=i-r,s[`cp1${o}`]=l-r*e[u]),c&&(r=(c[n]-i)/3,s[`cp2${n}`]=i+r,s[`cp2${o}`]=l+r*e[u])}}function kn(t,e="x"){const n=Mn(e),o=t.length,i=Array(o).fill(0),r=Array(o);let a,s,c,u=Tn(t,0);for(a=0;a<o;++a)if(s=c,c=u,u=Tn(t,a+1),c){if(u){const t=u[e]-c[e];i[a]=0!==t?(u[n]-c[n])/t:0}r[a]=s?u?X(i[a-1])!==X(i[a])?0:(i[a-1]+i[a])/2:i[a-1]:i[a]}Cn(t,i,r),_n(t,r,e)}function En(t,e,n){return Math.max(Math.min(t,n),e)}function On(t,e){let n,o,i,r,a,s=Re(t[0],e);for(n=0,o=t.length;n<o;++n)a=r,r=s,s=n<o-1&&Re(t[n+1],e),r&&(i=t[n],a&&(i.cp1x=En(i.cp1x,e.left,e.right),i.cp1y=En(i.cp1y,e.top,e.bottom)),s&&(i.cp2x=En(i.cp2x,e.left,e.right),i.cp2y=En(i.cp2y,e.top,e.bottom)))}function Pn(t,e,n,o,i){let r,a,s,c;if(e.spanGaps&&(t=t.filter((t=>!t.skip))),"monotone"===e.cubicInterpolationMode)kn(t,i);else{let n=o?t[t.length-1]:t[0];for(r=0,a=t.length;r<a;++r)s=t[r],c=Sn(n,s,t[Math.min(r+1,a-(o?0:1))%a],e.tension),s.cp1x=c.previous.x,s.cp1y=c.previous.y,s.cp2x=c.next.x,s.cp2y=c.next.y,n=s}e.capBezierPoints&&On(t,n)}function An(){return"undefined"!==typeof window&&"undefined"!==typeof document}function zn(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function Rn(t,e,n){let o;return"string"===typeof t?(o=parseInt(t,10),-1!==t.indexOf("%")&&(o=o/100*e.parentNode[n])):o=t,o}const In=t=>window.getComputedStyle(t,null);function Dn(t,e){return In(t).getPropertyValue(e)}const jn=["top","right","bottom","left"];function Fn(t,e,n){const o={};n=n?"-"+n:"";for(let i=0;i<4;i++){const r=jn[i];o[r]=parseFloat(t[e+"-"+r+n])||0}return o.width=o.left+o.right,o.height=o.top+o.bottom,o}const Yn=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function Nn(t,e){const n=t.touches,o=n&&n.length?n[0]:t,{offsetX:i,offsetY:r}=o;let a,s,c=!1;if(Yn(i,r,t.target))a=i,s=r;else{const t=e.getBoundingClientRect();a=o.clientX-t.left,s=o.clientY-t.top,c=!0}return{x:a,y:s,box:c}}function Ln(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:o}=e,i=In(n),r="border-box"===i.boxSizing,a=Fn(i,"padding"),s=Fn(i,"border","width"),{x:c,y:u,box:l}=Nn(t,n),f=a.left+(l&&s.left),h=a.top+(l&&s.top);let{width:d,height:p}=e;return r&&(d-=a.width+s.width,p-=a.height+s.height),{x:Math.round((c-f)/d*n.width/o),y:Math.round((u-h)/p*n.height/o)}}function Xn(t,e,n){let o,i;if(void 0===e||void 0===n){const r=zn(t);if(r){const t=r.getBoundingClientRect(),a=In(r),s=Fn(a,"border","width"),c=Fn(a,"padding");e=t.width-c.width-s.width,n=t.height-c.height-s.height,o=Rn(a.maxWidth,r,"clientWidth"),i=Rn(a.maxHeight,r,"clientHeight")}else e=t.clientWidth,n=t.clientHeight}return{width:e,height:n,maxWidth:o||D,maxHeight:i||D}}const Wn=t=>Math.round(10*t)/10;function Bn(t,e,n,o){const i=In(t),r=Fn(i,"margin"),a=Rn(i.maxWidth,t,"clientWidth")||D,s=Rn(i.maxHeight,t,"clientHeight")||D,c=Xn(t,e,n);let{width:u,height:l}=c;if("content-box"===i.boxSizing){const t=Fn(i,"border","width"),e=Fn(i,"padding");u-=e.width+t.width,l-=e.height+t.height}return u=Math.max(0,u-r.width),l=Math.max(0,o?Math.floor(u/o):l-r.height),u=Wn(Math.min(u,a,c.maxWidth)),l=Wn(Math.min(l,s,c.maxHeight)),u&&!l&&(l=Wn(u/2)),{width:u,height:l}}function Hn(t,e,n){const o=e||1,i=Math.floor(t.height*o),r=Math.floor(t.width*o);t.height=i/o,t.width=r/o;const a=t.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==o||a.height!==i||a.width!==r)&&(t.currentDevicePixelRatio=o,a.height=i,a.width=r,t.ctx.setTransform(o,0,0,o,0,0),!0)}const qn=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};window.addEventListener("test",null,e),window.removeEventListener("test",null,e)}catch(e){}return t}();function $n(t,e){const n=Dn(t,e),o=n&&n.match(/^(\d+)(\.\d+)?px$/);return o?+o[1]:void 0}function Zn(t,e,n,o){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function Vn(t,e,n,o){return{x:t.x+n*(e.x-t.x),y:"middle"===o?n<.5?t.y:e.y:"after"===o?n<1?t.y:e.y:n>0?e.y:t.y}}function Qn(t,e,n,o){const i={x:t.cp2x,y:t.cp2y},r={x:e.cp1x,y:e.cp1y},a=Zn(t,i,n),s=Zn(i,r,n),c=Zn(r,e,n),u=Zn(a,s,n),l=Zn(s,c,n);return Zn(u,l,n)}const Un=new Map;function Kn(t,e){e=e||{};const n=t+JSON.stringify(e);let o=Un.get(n);return o||(o=new Intl.NumberFormat(t,e),Un.set(n,o)),o}function Jn(t,e,n){return Kn(e,n).format(t)}const Gn=function(t,e){return{x(n){return t+t+e-n},setWidth(t){e=t},textAlign(t){return"center"===t?t:"right"===t?"left":"right"},xPlus(t,e){return t-e},leftForLtr(t,e){return t-e}}},to=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function eo(t,e,n){return t?Gn(e,n):to()}function no(t,e){let n,o;"ltr"!==e&&"rtl"!==e||(n=t.canvas.style,o=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=o)}function oo(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function io(t){return"angle"===t?{between:et,compare:G,normalize:tt}:{between:it,compare:(t,e)=>t-e,normalize:t=>t}}function ro({start:t,end:e,count:n,loop:o,style:i}){return{start:t%n,end:e%n,loop:o&&(e-t+1)%n===0,style:i}}function ao(t,e,n){const{property:o,start:i,end:r}=n,{between:a,normalize:s}=io(o),c=e.length;let u,l,{start:f,end:h,loop:d}=t;if(d){for(f+=c,h+=c,u=0,l=c;u<l;++u){if(!a(s(e[f%c][o]),i,r))break;f--,h--}f%=c,h%=c}return h<f&&(h+=c),{start:f,end:h,loop:d,style:t.style}}function so(t,e,n){if(!n)return[t];const{property:o,start:i,end:r}=n,a=e.length,{compare:s,between:c,normalize:u}=io(o),{start:l,end:f,loop:h,style:d}=ao(t,e,n),p=[];let m,g,y,b=!1,v=null;const x=()=>c(i,y,m)&&0!==s(i,y),w=()=>0===s(r,m)||c(r,y,m),T=()=>b||x(),M=()=>!b||w();for(let S=l,C=l;S<=f;++S)g=e[S%a],g.skip||(m=u(g[o]),m!==y&&(b=c(m,i,r),null===v&&T()&&(v=0===s(m,i)?S:C),null!==v&&M()&&(p.push(ro({start:v,end:S,loop:h,count:a,style:d})),v=null),C=S,y=m));return null!==v&&p.push(ro({start:v,end:f,loop:h,count:a,style:d})),p}function co(t,e){const n=[],o=t.segments;for(let i=0;i<o.length;i++){const r=so(o[i],t.points,e);r.length&&n.push(...r)}return n}function uo(t,e,n,o){let i=0,r=e-1;if(n&&!o)while(i<e&&!t[i].skip)i++;while(i<e&&t[i].skip)i++;i%=e,n&&(r+=i);while(r>i&&t[r%e].skip)r--;return r%=e,{start:i,end:r}}function lo(t,e,n,o){const i=t.length,r=[];let a,s=e,c=t[e];for(a=e+1;a<=n;++a){const n=t[a%i];n.skip||n.stop?c.skip||(o=!1,r.push({start:e%i,end:(a-1)%i,loop:o}),e=s=n.stop?a:null):(s=a,c.skip&&(e=a)),c=n}return null!==s&&r.push({start:e%i,end:s%i,loop:o}),r}function fo(t,e){const n=t.points,o=t.options.spanGaps,i=n.length;if(!i)return[];const r=!!t._loop,{start:a,end:s}=uo(n,i,r,o);if(!0===o)return ho(t,[{start:a,end:s,loop:r}],n,e);const c=s<a?s+i:s,u=!!t._fullLoop&&0===a&&s===i-1;return ho(t,lo(n,a,c,u),n,e)}function ho(t,e,n,o){return o&&o.setContext&&n?po(t,e,n,o):e}function po(t,e,n,o){const i=t._chart.getContext(),r=mo(t.options),{_datasetIndex:a,options:{spanGaps:s}}=t,c=n.length,u=[];let l=r,f=e[0].start,h=f;function d(t,e,o,i){const r=s?-1:1;if(t!==e){t+=c;while(n[t%c].skip)t-=r;while(n[e%c].skip)e+=r;t%c!==e%c&&(u.push({start:t%c,end:e%c,loop:o,style:i}),l=i,f=e%c)}}for(const p of e){f=s?f:p.start;let t,e=n[f%c];for(h=f+1;h<=p.end;h++){const r=n[h%c];t=mo(o.setContext(Ge(i,{type:"segment",p0:e,p1:r,p0DataIndex:(h-1)%c,p1DataIndex:h%c,datasetIndex:a}))),go(t,l)&&d(f,h-1,p.loop,l),e=r,l=t}f<h-1&&d(f,h-1,p.loop,l)}return u}function mo(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function go(t,e){return e&&JSON.stringify(t)!==JSON.stringify(e)}},47168:function(t,e,n){var o;
/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 Jorik Tangelder;
 * Licensed under the MIT license */(function(i,r,a,s){"use strict";var c,u=["","webkit","Moz","MS","ms","o"],l=r.createElement("div"),f="function",h=Math.round,d=Math.abs,p=Date.now;function m(t,e,n){return setTimeout(T(t,n),e)}function g(t,e,n){return!!Array.isArray(t)&&(y(t,n[e],n),!0)}function y(t,e,n){var o;if(t)if(t.forEach)t.forEach(e,n);else if(t.length!==s){o=0;while(o<t.length)e.call(n,t[o],o,t),o++}else for(o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function b(t,e,n){var o="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",r=i.console&&(i.console.warn||i.console.log);return r&&r.call(i.console,o,n),t.apply(this,arguments)}}c="function"!==typeof Object.assign?function(t){if(t===s||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var o=arguments[n];if(o!==s&&null!==o)for(var i in o)o.hasOwnProperty(i)&&(e[i]=o[i])}return e}:Object.assign;var v=b((function(t,e,n){var o=Object.keys(e),i=0;while(i<o.length)(!n||n&&t[o[i]]===s)&&(t[o[i]]=e[o[i]]),i++;return t}),"extend","Use `assign`."),x=b((function(t,e){return v(t,e,!0)}),"merge","Use `assign`.");function w(t,e,n){var o,i=e.prototype;o=t.prototype=Object.create(i),o.constructor=t,o._super=i,n&&c(o,n)}function T(t,e){return function(){return t.apply(e,arguments)}}function M(t,e){return typeof t==f?t.apply(e&&e[0]||s,e):t}function S(t,e){return t===s?e:t}function C(t,e,n){y(O(e),(function(e){t.addEventListener(e,n,!1)}))}function _(t,e,n){y(O(e),(function(e){t.removeEventListener(e,n,!1)}))}function k(t,e){while(t){if(t==e)return!0;t=t.parentNode}return!1}function E(t,e){return t.indexOf(e)>-1}function O(t){return t.trim().split(/\s+/g)}function P(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);var o=0;while(o<t.length){if(n&&t[o][n]==e||!n&&t[o]===e)return o;o++}return-1}function A(t){return Array.prototype.slice.call(t,0)}function z(t,e,n){var o=[],i=[],r=0;while(r<t.length){var a=e?t[r][e]:t[r];P(i,a)<0&&o.push(t[r]),i[r]=a,r++}return n&&(o=e?o.sort((function(t,n){return t[e]>n[e]})):o.sort()),o}function R(t,e){var n,o,i=e[0].toUpperCase()+e.slice(1),r=0;while(r<u.length){if(n=u[r],o=n?n+i:e,o in t)return o;r++}return s}var I=1;function D(){return I++}function j(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||i}var F=/mobile|tablet|ip(ad|hone|od)|android/i,Y="ontouchstart"in i,N=R(i,"PointerEvent")!==s,L=Y&&F.test(navigator.userAgent),X="touch",W="pen",B="mouse",H="kinect",q=25,$=1,Z=2,V=4,Q=8,U=1,K=2,J=4,G=8,tt=16,et=K|J,nt=G|tt,ot=et|nt,it=["x","y"],rt=["clientX","clientY"];function at(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){M(t.options.enable,[t])&&n.handler(e)},this.init()}function st(t){var e,n=t.options.inputClass;return e=n||(N?Et:L?jt:Y?Lt:Mt),new e(t,ct)}function ct(t,e,n){var o=n.pointers.length,i=n.changedPointers.length,r=e&$&&o-i===0,a=e&(V|Q)&&o-i===0;n.isFirst=!!r,n.isFinal=!!a,r&&(t.session={}),n.eventType=e,ut(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function ut(t,e){var n=t.session,o=e.pointers,i=o.length;n.firstInput||(n.firstInput=ht(e)),i>1&&!n.firstMultiple?n.firstMultiple=ht(e):1===i&&(n.firstMultiple=!1);var r=n.firstInput,a=n.firstMultiple,s=a?a.center:r.center,c=e.center=dt(o);e.timeStamp=p(),e.deltaTime=e.timeStamp-r.timeStamp,e.angle=yt(s,c),e.distance=gt(s,c),lt(n,e),e.offsetDirection=mt(e.deltaX,e.deltaY);var u=pt(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=u.x,e.overallVelocityY=u.y,e.overallVelocity=d(u.x)>d(u.y)?u.x:u.y,e.scale=a?vt(a.pointers,o):1,e.rotation=a?bt(a.pointers,o):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,ft(n,e);var l=t.element;k(e.srcEvent.target,l)&&(l=e.srcEvent.target),e.target=l}function lt(t,e){var n=e.center,o=t.offsetDelta||{},i=t.prevDelta||{},r=t.prevInput||{};e.eventType!==$&&r.eventType!==V||(i=t.prevDelta={x:r.deltaX||0,y:r.deltaY||0},o=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=i.x+(n.x-o.x),e.deltaY=i.y+(n.y-o.y)}function ft(t,e){var n,o,i,r,a=t.lastInterval||e,c=e.timeStamp-a.timeStamp;if(e.eventType!=Q&&(c>q||a.velocity===s)){var u=e.deltaX-a.deltaX,l=e.deltaY-a.deltaY,f=pt(c,u,l);o=f.x,i=f.y,n=d(f.x)>d(f.y)?f.x:f.y,r=mt(u,l),t.lastInterval=e}else n=a.velocity,o=a.velocityX,i=a.velocityY,r=a.direction;e.velocity=n,e.velocityX=o,e.velocityY=i,e.direction=r}function ht(t){var e=[],n=0;while(n<t.pointers.length)e[n]={clientX:h(t.pointers[n].clientX),clientY:h(t.pointers[n].clientY)},n++;return{timeStamp:p(),pointers:e,center:dt(e),deltaX:t.deltaX,deltaY:t.deltaY}}function dt(t){var e=t.length;if(1===e)return{x:h(t[0].clientX),y:h(t[0].clientY)};var n=0,o=0,i=0;while(i<e)n+=t[i].clientX,o+=t[i].clientY,i++;return{x:h(n/e),y:h(o/e)}}function pt(t,e,n){return{x:e/t||0,y:n/t||0}}function mt(t,e){return t===e?U:d(t)>=d(e)?t<0?K:J:e<0?G:tt}function gt(t,e,n){n||(n=it);var o=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return Math.sqrt(o*o+i*i)}function yt(t,e,n){n||(n=it);var o=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return 180*Math.atan2(i,o)/Math.PI}function bt(t,e){return yt(e[1],e[0],rt)+yt(t[1],t[0],rt)}function vt(t,e){return gt(e[0],e[1],rt)/gt(t[0],t[1],rt)}at.prototype={handler:function(){},init:function(){this.evEl&&C(this.element,this.evEl,this.domHandler),this.evTarget&&C(this.target,this.evTarget,this.domHandler),this.evWin&&C(j(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&_(this.element,this.evEl,this.domHandler),this.evTarget&&_(this.target,this.evTarget,this.domHandler),this.evWin&&_(j(this.element),this.evWin,this.domHandler)}};var xt={mousedown:$,mousemove:Z,mouseup:V},wt="mousedown",Tt="mousemove mouseup";function Mt(){this.evEl=wt,this.evWin=Tt,this.pressed=!1,at.apply(this,arguments)}w(Mt,at,{handler:function(t){var e=xt[t.type];e&$&&0===t.button&&(this.pressed=!0),e&Z&&1!==t.which&&(e=V),this.pressed&&(e&V&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:B,srcEvent:t}))}});var St={pointerdown:$,pointermove:Z,pointerup:V,pointercancel:Q,pointerout:Q},Ct={2:X,3:W,4:B,5:H},_t="pointerdown",kt="pointermove pointerup pointercancel";function Et(){this.evEl=_t,this.evWin=kt,at.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}i.MSPointerEvent&&!i.PointerEvent&&(_t="MSPointerDown",kt="MSPointerMove MSPointerUp MSPointerCancel"),w(Et,at,{handler:function(t){var e=this.store,n=!1,o=t.type.toLowerCase().replace("ms",""),i=St[o],r=Ct[t.pointerType]||t.pointerType,a=r==X,s=P(e,t.pointerId,"pointerId");i&$&&(0===t.button||a)?s<0&&(e.push(t),s=e.length-1):i&(V|Q)&&(n=!0),s<0||(e[s]=t,this.callback(this.manager,i,{pointers:e,changedPointers:[t],pointerType:r,srcEvent:t}),n&&e.splice(s,1))}});var Ot={touchstart:$,touchmove:Z,touchend:V,touchcancel:Q},Pt="touchstart",At="touchstart touchmove touchend touchcancel";function zt(){this.evTarget=Pt,this.evWin=At,this.started=!1,at.apply(this,arguments)}function Rt(t,e){var n=A(t.touches),o=A(t.changedTouches);return e&(V|Q)&&(n=z(n.concat(o),"identifier",!0)),[n,o]}w(zt,at,{handler:function(t){var e=Ot[t.type];if(e===$&&(this.started=!0),this.started){var n=Rt.call(this,t,e);e&(V|Q)&&n[0].length-n[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:X,srcEvent:t})}}});var It={touchstart:$,touchmove:Z,touchend:V,touchcancel:Q},Dt="touchstart touchmove touchend touchcancel";function jt(){this.evTarget=Dt,this.targetIds={},at.apply(this,arguments)}function Ft(t,e){var n=A(t.touches),o=this.targetIds;if(e&($|Z)&&1===n.length)return o[n[0].identifier]=!0,[n,n];var i,r,a=A(t.changedTouches),s=[],c=this.target;if(r=n.filter((function(t){return k(t.target,c)})),e===$){i=0;while(i<r.length)o[r[i].identifier]=!0,i++}i=0;while(i<a.length)o[a[i].identifier]&&s.push(a[i]),e&(V|Q)&&delete o[a[i].identifier],i++;return s.length?[z(r.concat(s),"identifier",!0),s]:void 0}w(jt,at,{handler:function(t){var e=It[t.type],n=Ft.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:X,srcEvent:t})}});var Yt=2500,Nt=25;function Lt(){at.apply(this,arguments);var t=T(this.handler,this);this.touch=new jt(this.manager,t),this.mouse=new Mt(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function Xt(t,e){t&$?(this.primaryTouch=e.changedPointers[0].identifier,Wt.call(this,e)):t&(V|Q)&&Wt.call(this,e)}function Wt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY};this.lastTouches.push(n);var o=this.lastTouches,i=function(){var t=o.indexOf(n);t>-1&&o.splice(t,1)};setTimeout(i,Yt)}}function Bt(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var i=this.lastTouches[o],r=Math.abs(e-i.x),a=Math.abs(n-i.y);if(r<=Nt&&a<=Nt)return!0}return!1}w(Lt,at,{handler:function(t,e,n){var o=n.pointerType==X,i=n.pointerType==B;if(!(i&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(o)Xt.call(this,e,n);else if(i&&Bt.call(this,n))return;this.callback(t,e,n)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var Ht=R(l.style,"touchAction"),qt=Ht!==s,$t="compute",Zt="auto",Vt="manipulation",Qt="none",Ut="pan-x",Kt="pan-y",Jt=ee();function Gt(t,e){this.manager=t,this.set(e)}function te(t){if(E(t,Qt))return Qt;var e=E(t,Ut),n=E(t,Kt);return e&&n?Qt:e||n?e?Ut:Kt:E(t,Vt)?Vt:Zt}function ee(){if(!qt)return!1;var t={},e=i.CSS&&i.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){t[n]=!e||i.CSS.supports("touch-action",n)})),t}Gt.prototype={set:function(t){t==$t&&(t=this.compute()),qt&&this.manager.element.style&&Jt[t]&&(this.manager.element.style[Ht]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return y(this.manager.recognizers,(function(e){M(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),te(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var o=this.actions,i=E(o,Qt)&&!Jt[Qt],r=E(o,Kt)&&!Jt[Kt],a=E(o,Ut)&&!Jt[Ut];if(i){var s=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(s&&c&&u)return}if(!a||!r)return i||r&&n&et||a&&n&nt?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var ne=1,oe=2,ie=4,re=8,ae=re,se=16,ce=32;function ue(t){this.options=c({},this.defaults,t||{}),this.id=D(),this.manager=null,this.options.enable=S(this.options.enable,!0),this.state=ne,this.simultaneous={},this.requireFail=[]}function le(t){return t&se?"cancel":t&re?"end":t&ie?"move":t&oe?"start":""}function fe(t){return t==tt?"down":t==G?"up":t==K?"left":t==J?"right":""}function he(t,e){var n=e.manager;return n?n.get(t):t}function de(){ue.apply(this,arguments)}function pe(){de.apply(this,arguments),this.pX=null,this.pY=null}function me(){de.apply(this,arguments)}function ge(){ue.apply(this,arguments),this._timer=null,this._input=null}function ye(){de.apply(this,arguments)}function be(){de.apply(this,arguments)}function ve(){ue.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function xe(t,e){return e=e||{},e.recognizers=S(e.recognizers,xe.defaults.preset),new Me(t,e)}ue.prototype={defaults:{},set:function(t){return c(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(g(t,"recognizeWith",this))return this;var e=this.simultaneous;return t=he(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return g(t,"dropRecognizeWith",this)||(t=he(t,this),delete this.simultaneous[t.id]),this},requireFailure:function(t){if(g(t,"requireFailure",this))return this;var e=this.requireFail;return t=he(t,this),-1===P(e,t)&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(g(t,"dropRequireFailure",this))return this;t=he(t,this);var e=P(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,n=this.state;function o(n){e.manager.emit(n,t)}n<re&&o(e.options.event+le(n)),o(e.options.event),t.additionalEvent&&o(t.additionalEvent),n>=re&&o(e.options.event+le(n))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=ce},canEmit:function(){var t=0;while(t<this.requireFail.length){if(!(this.requireFail[t].state&(ce|ne)))return!1;t++}return!0},recognize:function(t){var e=c({},t);if(!M(this.options.enable,[this,e]))return this.reset(),void(this.state=ce);this.state&(ae|se|ce)&&(this.state=ne),this.state=this.process(e),this.state&(oe|ie|re|se)&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},w(de,ue,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,o=e&(oe|ie),i=this.attrTest(t);return o&&(n&Q||!i)?e|se:o||i?n&V?e|re:e&oe?e|ie:oe:ce}}),w(pe,de,{defaults:{event:"pan",threshold:10,pointers:1,direction:ot},getTouchAction:function(){var t=this.options.direction,e=[];return t&et&&e.push(Kt),t&nt&&e.push(Ut),e},directionTest:function(t){var e=this.options,n=!0,o=t.distance,i=t.direction,r=t.deltaX,a=t.deltaY;return i&e.direction||(e.direction&et?(i=0===r?U:r<0?K:J,n=r!=this.pX,o=Math.abs(t.deltaX)):(i=0===a?U:a<0?G:tt,n=a!=this.pY,o=Math.abs(t.deltaY))),t.direction=i,n&&o>e.threshold&&i&e.direction},attrTest:function(t){return de.prototype.attrTest.call(this,t)&&(this.state&oe||!(this.state&oe)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=fe(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),w(me,de,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[Qt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&oe)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),w(ge,ue,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[Zt]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,o=t.distance<e.threshold,i=t.deltaTime>e.time;if(this._input=t,!o||!n||t.eventType&(V|Q)&&!i)this.reset();else if(t.eventType&$)this.reset(),this._timer=m((function(){this.state=ae,this.tryEmit()}),e.time,this);else if(t.eventType&V)return ae;return ce},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===ae&&(t&&t.eventType&V?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=p(),this.manager.emit(this.options.event,this._input)))}}),w(ye,de,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[Qt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&oe)}}),w(be,de,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:et|nt,pointers:1},getTouchAction:function(){return pe.prototype.getTouchAction.call(this)},attrTest:function(t){var e,n=this.options.direction;return n&(et|nt)?e=t.overallVelocity:n&et?e=t.overallVelocityX:n&nt&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&n&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&d(e)>this.options.velocity&&t.eventType&V},emit:function(t){var e=fe(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),w(ve,ue,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Vt]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,o=t.distance<e.threshold,i=t.deltaTime<e.time;if(this.reset(),t.eventType&$&&0===this.count)return this.failTimeout();if(o&&i&&n){if(t.eventType!=V)return this.failTimeout();var r=!this.pTime||t.timeStamp-this.pTime<e.interval,a=!this.pCenter||gt(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,a&&r?this.count+=1:this.count=1,this._input=t;var s=this.count%e.taps;if(0===s)return this.hasRequireFailures()?(this._timer=m((function(){this.state=ae,this.tryEmit()}),e.interval,this),oe):ae}return ce},failTimeout:function(){return this._timer=m((function(){this.state=ce}),this.options.interval,this),ce},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==ae&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),xe.VERSION="2.0.7",xe.defaults={domEvents:!1,touchAction:$t,enable:!0,inputTarget:null,inputClass:null,preset:[[ye,{enable:!1}],[me,{enable:!1},["rotate"]],[be,{direction:et}],[pe,{direction:et},["swipe"]],[ve],[ve,{event:"doubletap",taps:2},["tap"]],[ge]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var we=1,Te=2;function Me(t,e){this.options=c({},xe.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=st(this),this.touchAction=new Gt(this,this.options.touchAction),Se(this,!0),y(this.options.recognizers,(function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}function Se(t,e){var n,o=t.element;o.style&&(y(t.options.cssProps,(function(i,r){n=R(o.style,r),e?(t.oldCssProps[n]=o.style[n],o.style[n]=i):o.style[n]=t.oldCssProps[n]||""})),e||(t.oldCssProps={}))}function Ce(t,e){var n=r.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}Me.prototype={set:function(t){return c(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?Te:we},recognize:function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var o=this.recognizers,i=e.curRecognizer;(!i||i&&i.state&ae)&&(i=e.curRecognizer=null);var r=0;while(r<o.length)n=o[r],e.stopped===Te||i&&n!=i&&!n.canRecognizeWith(i)?n.reset():n.recognize(t),!i&&n.state&(oe|ie|re)&&(i=e.curRecognizer=n),r++}},get:function(t){if(t instanceof ue)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];return null},add:function(t){if(g(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(g(t,"remove",this))return this;if(t=this.get(t),t){var e=this.recognizers,n=P(e,t);-1!==n&&(e.splice(n,1),this.touchAction.update())}return this},on:function(t,e){if(t!==s&&e!==s){var n=this.handlers;return y(O(t),(function(t){n[t]=n[t]||[],n[t].push(e)})),this}},off:function(t,e){if(t!==s){var n=this.handlers;return y(O(t),(function(t){e?n[t]&&n[t].splice(P(n[t],e),1):delete n[t]})),this}},emit:function(t,e){this.options.domEvents&&Ce(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};var o=0;while(o<n.length)n[o](e),o++}},destroy:function(){this.element&&Se(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},c(xe,{INPUT_START:$,INPUT_MOVE:Z,INPUT_END:V,INPUT_CANCEL:Q,STATE_POSSIBLE:ne,STATE_BEGAN:oe,STATE_CHANGED:ie,STATE_ENDED:re,STATE_RECOGNIZED:ae,STATE_CANCELLED:se,STATE_FAILED:ce,DIRECTION_NONE:U,DIRECTION_LEFT:K,DIRECTION_RIGHT:J,DIRECTION_UP:G,DIRECTION_DOWN:tt,DIRECTION_HORIZONTAL:et,DIRECTION_VERTICAL:nt,DIRECTION_ALL:ot,Manager:Me,Input:at,TouchAction:Gt,TouchInput:jt,MouseInput:Mt,PointerEventInput:Et,TouchMouseInput:Lt,SingleTouchInput:zt,Recognizer:ue,AttrRecognizer:de,Tap:ve,Pan:pe,Swipe:be,Pinch:me,Rotate:ye,Press:ge,on:C,off:_,each:y,merge:x,extend:v,assign:c,inherit:w,bindFn:T,prefixed:R});var _e="undefined"!==typeof i?i:"undefined"!==typeof self?self:{};_e.Hammer=xe,o=function(){return xe}.call(e,n,e,t),o===s||(t.exports=o)})(window,document)}}]);