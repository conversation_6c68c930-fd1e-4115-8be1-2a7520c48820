{
  "//AccessTokenExpireMinute": "AccessToken的逾時時間(分)",
  "AccessTokenExpireMinute": 14400,

  "//RefreshtokenExpireMinute": "RefreshToken的逾時時間(分)",
  "RefreshtokenExpireMinute": 14400,
  "//CustomerIdNameInRequestUrlSegmentSn": "客戶ID名稱在Request Url 字節的第?段(從第0段開始) ",
  "CustomerIdNameInRequestUrlSegmentSn": 1,

  "//SerilogPath": "SeriLog 檔案路徑",
  "SerilogPath": "D:\\Product02\\logs\\WebApi\\",

  "RtuComportStartSn": 1,
  "RtuComportEndSn": 255,

  //"TemporaryTagFilePath": "D:\\Product02\\Task\\TemporaryTagFilePath\\",
  "TemporaryTagFilePath": "D:\\Product02\\TemporaryTagFilePath\\",

  "SaveRealTimeTagData": true,

  "IconWeb": "http://localhost/production-2/",
  "IconLocation": "D:\\Product02\\Icons\\",

  "//TryDecigoCCConfig": "是否測試 DesigoCC 組態正確性",
  "TryDecigoCCConfig": true,

  "//SaveDecigoCCConfig": "是否將DesigoCC 組態存檔",
  "SaveDecigoCCConfig": true,

  "//DesigoCCProcessExpireTime": "處理 Desigo CC 逾時時間 (ms)",
  "DesigoCCProcessExpireTime": 30000,
  "ObixProcessExpireTime": 3000,

  "//每個Batch的 Obix 測點數量": "",
  "ObixElementsEachBatch": 1,
  "LineNotifyApiUrl": "https://notify-api.line.me/api/notify",

  "//ProcessAlarmFromDesigoCC": "要接收從Desigocc 送來的警報?",
  "ProcessAlarmFromDesigoCC": false,

  "//ProcessRealTimeDataValueFromDesigoCC": "是否處理 DesigoCC 送來的即時資料",
  "ProcessRealTimeDataValueFromDesigoCC": false,

  "//SendAlarmSummaryToFront": "是否傳送AlarmSUmmary 到前端",
  "SendAlarmSummaryToFront": false
}
