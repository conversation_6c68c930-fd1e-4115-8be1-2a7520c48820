{
    "AllowedHosts": "*",
    "Serilog": {
        "MinimumLevel": {
            "Default": "Information",
            "Override": {
                "Default": "Information",
                "Microsoft.AspNetCore": "Information"
            }
        },
        "WriteTo": [
            {
                "Name": "Console",
                "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
            },
            {
                "Name": "File",
                "Args": {
                    "path": "D:/Product02/logs/WebApi/Api-.log",
                    "rollingInterval": "Day",
                    "fileSizeLimitBytes": 52428800, // 50 MB
                    "rollOnFileSizeLimit": true,
                    "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"
                }
            }
        ]
    },
    "LicenseChecker": {
        "IsTrialLicense": false,
        "ServiceUrl": "http://*************:5115"
    }
}