{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["FastEnum >= 1.8.0", "Serilog >= 3.1.1", "System.ServiceModel.Primitives >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj", "projectName": "Oco.Core.Vars", "projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "http://192.168.1.154:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services\\Oco.Core.Services.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FastEnum": {"target": "Package", "version": "[1.8.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "System.ServiceModel.Primitives": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1803", "level": "Warning", "warningLevel": 1, "message": "You are running the 'restore' operation with an 'HTTP' source, 'http://192.168.1.154:14235/v3/index.json'. Non-HTTPS access will be removed in a future version. Consider migrating to an 'HTTPS' source."}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "FastEnum"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "ClosedXML"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "Microsoft.Extensions.DependencyInjection.Abstractions"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "<PERSON><PERSON><PERSON>"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "System.Linq.Async"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://192.168.1.154:14235/v3/index.json.", "libraryId": "MQTTnet"}]}