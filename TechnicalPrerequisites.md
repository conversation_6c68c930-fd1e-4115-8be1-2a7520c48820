# 技術前提約束文檔

## 📋 概述

本文檔定義了 PLC Frontend 專案的技術前提條件和約束，確保開發環境的一致性和專案的穩定性。

## 🔧 開發環境要求

### 必要軟體版本

#### Node.js 環境
- **Node.js**: 18.16.1 (嚴格要求，不可使用其他版本)
- **npm**: 9.5.1 (嚴格要求，不可使用其他版本)
- **原因**: 確保依賴兼容性和構建一致性

#### 版本管理工具
- **nvm**: 推薦使用 Node Version Manager
- **Git**: 2.30+ 版本

### 環境設置步驟

```bash
# 1. 安裝指定 Node.js 版本
nvm install 18.16.1
nvm alias default 18.16.1
nvm use 18.16.1

# 2. 驗證版本
node -v   # 必須顯示 v18.16.1
npm -v    # 必須顯示 9.5.1

# 3. 安裝專案依賴
npm install --legacy-peer-deps

# 4. 啟動開發服務器
npm run serve
```

## 📦 核心技術棧

### 前端框架
- **Vue.js**: 3.2.45
  - 使用 Composition API
  - 支持 TypeScript（可選）
  - 向後兼容 Options API

### UI 組件庫
- **Ant Design Vue**: 3.2.15
  - 完整的企業級 UI 組件
  - 支持主題定制
  - 響應式設計

### 狀態管理
- **Vuex**: 4.x（現有系統）
  - 用於現有功能維護
  - 不建議新功能使用

- **Pinia**: 2.0.28（新功能推薦）
  - Vue 3 官方推薦
  - 更好的 TypeScript 支持
  - 用於主題管理等新功能

### 路由管理
- **Vue Router**: 4.x
  - 支持動態路由
  - 路由守衛
  - 懶加載

### 構建工具
- **Webpack**: 5.x
  - 模塊打包
  - 代碼分割
  - 熱重載

### 樣式處理
- **Sass/SCSS**: 預處理器
- **CSS Variables**: 主題系統基礎
- **PostCSS**: 自動前綴和優化

### 圖表庫
- **ApexCharts**: 主要圖表庫
- **Chart.js**: 輔助圖表庫
- **Vue3-ApexCharts**: Vue 3 整合

## 🏗️ 架構約束

### 組件架構
- **單文件組件**: 使用 `.vue` 文件格式
- **組合式 API**: 新組件優先使用 Composition API
- **組件通信**: Props down, Events up 原則

### 狀態管理架構
```
應用狀態
├── Vuex Store (現有功能)
│   ├── modules/
│   │   ├── auth/
│   │   ├── data/
│   │   └── ...
└── Pinia Stores (新功能)
    ├── theme.js
    ├── user.js
    └── ...
```

### 路由架構
```
路由結構
├── AdminRoutes.js (管理員路由)
├── FeaturesRoutes.js (功能路由)
├── authRoutes.js (認證路由)
└── protectedRoute.js (受保護路由)
```

## 🎨 主題系統約束

### CSS 變數系統
- **必須使用**: 所有顏色值必須使用 CSS 變數
- **變數定義**: 在 `src/static/css/style.css` 中定義
- **命名規範**: 語義化命名，如 `--primary`, `--background`

### 主題配置
```javascript
// 主題結構約束
themes: {
  light: {
    // 主要顏色
    primary: string,
    primaryHover: string,
    // 背景顏色
    background: string,
    cardBackground: string,
    // 文字顏色
    textPrimary: string,
    textSecondary: string,
    // 邊框顏色
    borderNormal: string,
    // 狀態顏色
    success: string,
    warning: string,
    error: string,
    info: string
  },
  dark: {
    // 相同結構
  }
}
```

### 主題切換約束
- **實時切換**: 不可重新加載頁面
- **持久化**: 必須保存到 localStorage
- **系統偏好**: 支持系統主題檢測
- **過渡效果**: 使用 CSS transition

## 📁 文件組織約束

### 目錄結構
```
src/
├── components/          # 可重用組件
│   ├── utilities/       # 工具組件（如主題切換）
│   ├── charts/          # 圖表組件
│   ├── forms/           # 表單組件
│   └── ...
├── stores/              # Pinia 狀態管理
├── vuex/                # Vuex 狀態管理
├── layout/              # 佈局組件
├── view/                # 頁面組件
├── routes/              # 路由配置
├── static/              # 靜態資源
│   ├── css/             # 全局樣式
│   └── img/             # 圖片資源
├── config/              # 配置文件
├── composable/          # 組合式函數
├── utility/             # 工具函數
└── i18n/                # 國際化
```

### 命名約束
- **組件文件**: PascalCase（如 `ThemeToggle.vue`）
- **頁面文件**: kebab-case（如 `user-profile.vue`）
- **工具文件**: camelCase（如 `formatDate.js`）
- **常量文件**: UPPER_SNAKE_CASE（如 `API_ENDPOINTS.js`）

## 🔌 API 約束

### HTTP 客戶端
- **Axios**: 用於 HTTP 請求
- **攔截器**: 統一錯誤處理和認證
- **基礎 URL**: 通過環境變數配置

### API 結構
```javascript
// API 響應格式約束
{
  success: boolean,
  data: any,
  message: string,
  code: number
}
```

## 🧪 測試約束

### 測試框架
- **Jest**: 單元測試框架
- **Vue Test Utils**: Vue 組件測試
- **Testing Library**: 用戶行為測試

### 測試覆蓋率
- **最低要求**: 70% 代碼覆蓋率
- **關鍵功能**: 100% 覆蓋率
- **主題系統**: 必須測試主題切換功能

## 🚀 構建約束

### 構建配置
- **目標瀏覽器**: ES2015+
- **模塊格式**: ES Modules
- **代碼分割**: 按路由分割
- **資源優化**: 圖片壓縮、CSS 最小化

### 環境配置
```javascript
// 環境變數約束
VUE_APP_API_BASE_URL=string    // API 基礎 URL
VUE_APP_TITLE=string           // 應用標題
VUE_APP_VERSION=string         // 版本號
NODE_ENV=development|production // 環境類型
```

## 📱 瀏覽器支持約束

### 支持的瀏覽器
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 不支持的瀏覽器
- **Internet Explorer**: 所有版本
- **舊版 Chrome**: < 90
- **舊版 Firefox**: < 88

## 🔒 安全約束

### 代碼安全
- **XSS 防護**: 使用 Vue 內建防護
- **CSRF 防護**: 使用 CSRF Token
- **輸入驗證**: 前後端雙重驗證

### 依賴安全
- **定期審計**: 使用 `npm audit`
- **版本鎖定**: 使用 `package-lock.json`
- **漏洞修復**: 及時更新有漏洞的依賴

## 📊 性能約束

### 性能指標
- **首屏加載**: < 3 秒
- **路由切換**: < 500ms
- **主題切換**: < 200ms
- **API 響應**: < 2 秒

### 優化要求
- **代碼分割**: 按需加載
- **圖片優化**: WebP 格式優先
- **緩存策略**: 合理使用瀏覽器緩存

## 🔄 版本控制約束

### Git 工作流
- **主分支**: `main`（生產環境）
- **開發分支**: `develop`（開發環境）
- **功能分支**: `feature/*`
- **修復分支**: `hotfix/*`

### 提交規範
```
type(scope): description

feat: 新功能
fix: 修復
docs: 文檔
style: 格式
refactor: 重構
test: 測試
chore: 構建
```

## ⚠️ 限制和禁止

### 技術限制
- **不可使用**: jQuery 或其他 DOM 操作庫
- **不可引入**: 未經批准的第三方庫
- **不可修改**: 核心配置文件（除非必要）

### 架構限制
- **不可繞過**: 現有狀態管理架構
- **不可破壞**: 現有組件 API
- **不可移除**: 現有功能（除非明確要求）

## 📞 技術支持

### 聯繫方式
- **技術主管**: 架構和設計決策
- **開發團隊**: 實現細節和問題解決
- **專案經理**: 需求和優先級

### 文檔更新
- **定期審查**: 每月檢查技術約束
- **版本控制**: 文檔變更需要審批
- **通知機制**: 重要變更需要通知全團隊

---

**重要提醒**: 違反技術前提約束可能導致專案不穩定、構建失敗或部署問題。請嚴格遵守所有約束條件。
