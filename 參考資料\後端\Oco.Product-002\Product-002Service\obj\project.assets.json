{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["Microsoft.AspNetCore.SignalR.Client >= 8.0.5", "Microsoft.Extensions.Hosting >= 8.0.0", "Microsoft.Extensions.Hosting.WindowsServices >= 8.0.0", "Serilog >= 3.1.1", "Serilog.AspNetCore >= 8.0.1", "Serilog.Settings.Configuration >= 8.0.0", "Serilog.Sinks.Console >= 5.0.1", "Serilog.Sinks.File >= 5.0.0", "System.Linq.Async >= 6.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Product-002Service\\Product-002Service.csproj", "projectName": "Product-002Service", "projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Product-002Service\\Product-002Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Product-002Service\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "http://*************:14235/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services.Cached\\Oco.Core.Services.Cached.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Services.Cached\\Oco.Core.Services.Cached.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core.Vars\\Oco.Core.Vars.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Core\\Oco.Core.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Message\\Oco.Message.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Product-002.Model\\Oco.Product-002.Model.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.RabbitMqtt\\Oco.RabbitMqtt.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.RabbitMqtt\\Oco.RabbitMqtt.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\Oco.Repositories\\Oco.Repositories.csproj"}, "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj": {"projectPath": "D:\\Projects\\plc-frontend\\參考資料\\後端\\Oco.Product-002\\TagImportAndExport\\TagImportAndExport.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.1, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Linq.Async": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.300/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1803", "level": "Warning", "warningLevel": 1, "message": "You are running the 'restore' operation with an 'HTTP' source, 'http://*************:14235/v3/index.json'. Non-HTTPS access will be removed in a future version. Consider migrating to an 'HTTPS' source."}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://*************:14235/v3/index.json.", "libraryId": "Microsoft.Extensions.Hosting"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://*************:14235/v3/index.json.", "libraryId": "System.Linq.Async"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source http://*************:14235/v3/index.json.", "libraryId": "Microsoft.Extensions.Hosting.WindowsServices"}]}