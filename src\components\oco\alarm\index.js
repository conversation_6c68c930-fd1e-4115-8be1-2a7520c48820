import { computed, defineComponent, ref } from "vue";
import DataTables from "@/components/table/DataTable.vue";
import { RealTime, ActionSpan } from "./style";
import ModalHelper from "@/utility/modalHelper";
import { useStore } from "vuex";
import router from "@/routes/protectedRoute";
import dayjs from "dayjs";
import cctvStream from "@/components/oco/util/cctvModal/Index.vue";
import { useTagInfo } from "@/composable/tagInfo";
import { usePermission } from "@/composable/permission";
export default defineComponent({
  components: {
    DataTables,
    RealTime,
    cctvStream,
  },
  props: {
    collapsed: {
      type: String,
      require: true,
    },
    alarmSummary: {
      type: Array,
      require: true,
    },
    pause: {
      type: Boolean,
      require: true,
    },
  },
  emits: ["onPauseChanged", "changePanel"],
  setup(props, { emit }) {
    const { dispatch } = useStore();
    const { permission: alarmPermission } = usePermission("alarm-realtime");
    const windowWidth = window.innerWidth;
    const changePanel = (e) => {
      emit("changePanel", e);
    };

    const getCCTVList = (tagId) => {
      const cctvList = useTagInfo(tagId, "CctvList");
      if (cctvList && cctvList.length > 0) {
        return cctvList.map((el) => el.Id);
      } else {
        return [];
      }
    };

    const tableData = computed(() => {
      return props.alarmSummary.map((el) => {
        return {
          ...el,
          time: dayjs(el.AlarmTime).format("YYYY-MM-DD HH:mm:ss"),
          action: (
            <ActionSpan>
              {(el.AlarmState === 1 || el.AlarmState === 3) && alarmPermission.update && (
                <a-button
                  type="primary"
                  ghost
                  onClick={() => {
                    checkAlarm({ id: el.Id });
                  }}
                >
                  <unicon name="check"></unicon>
                </a-button>
              )}
              <a-button
                type="primary"
                ghost
                onClick={() => {
                  openSOP(el);
                }}
              >
                <unicon name="file-alt"></unicon>
              </a-button>
              {el.PageId && (
                <a-button type="primary" ghost>
                  <unicon
                    name="image"
                    onClick={() => {
                      openImage(el);
                    }}
                  ></unicon>
                </a-button>
              )}
              {getCCTVList(el.ComponentId).length > 0 && (
                <a-button type="primary" ghost>
                  <unicon
                    name="video"
                    onClick={() => {
                      openCCTVModal(getCCTVList(el.ComponentId));
                    }}
                  ></unicon>
                </a-button>
              )}
            </ActionSpan>
          ),
        };
      });
    });
    const columns = [
      { title: "操作", dataIndex: "action", key: "action", width: 190 },
      { title: "時間", dataIndex: "time", key: "time", width: 110 },
      { title: "地區", dataIndex: "RegionName", key: "RegionName", width: 120 },
      {
        title: "說明",
        dataIndex: "AlarmDescription",
        key: "AlarmDescription",
        width: 230,
      },
      {
        title: "測點",
        dataIndex: "ComponentName",
        key: "ComponentName",
        width: 310,
      },
      {
        title: "警報等級",
        dataIndex: "PriorityText",
        key: "PriorityText",
        width: 80,
      },
      {
        title: "狀態",
        dataIndex: "AlarmStateText",
        key: "AlarmStateText",
        width: 80,
      },
    ];

    const getRowClassName = ({ AlarmState }) => {
      if (AlarmState === 2) {
        return "text-checked";
      } else if (AlarmState === 1) {
        return "text-alert";
      } else {
        return "text-normal";
      }
    };

    const onPauseChanged = (e) => {
      emit("onPauseChanged", e.target.checked);
    };

    const openSOP = ({ ComponentName, AlarmSop }) => {
      ModalHelper.info({
        title: `${ComponentName} SOP`,
        content: AlarmSop,
      });
    };

    const openImage = (data) => {
      router.push({
        name: "gui-main",
        params: { id: data.PageId },
      });
    };

    const CCTVModal = ref(false);
    const currCCTV = ref([]);

    const openCCTVModal = (CctvIdList) => {
      currCCTV.value = CctvIdList;
      CCTVModal.value = true;
    };

    const closeCCTVModal = () => {
      CCTVModal.value = false;
    };

    const checkAlarm = async ({ id }) => {
      ModalHelper.confirm({
        title: "確認警報?",
        okText: "確認",
        cancelText: "取消",
        onOk: async () => {
          try {
            await dispatch("alarm/checkAlarm", id);
            ModalHelper.success({
              title: "已確認",
            });
          } catch (err) {
            ModalHelper.error({
              title: "發生錯誤",
              content: err.message,
            });
          }
        },
      });
    };

    return {
      windowWidth,
      changePanel,
      tableData,
      columns,
      getRowClassName,
      checkAlarm,
      onPauseChanged,
      CCTVModal,
      currCCTV,
      closeCCTVModal,
    };
  },
});
