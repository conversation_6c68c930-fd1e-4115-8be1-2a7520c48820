﻿namespace WinFormImport
{
    partial class MyForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            btnSelectFile = new Button();
            opfDlgImport = new OpenFileDialog();
            txtbxFileName = new TextBox();
            btnImport = new Button();
            CmbxCustomerId = new ComboBox();
            btnRefreshCustomerIdList = new Button();
            grpBx1 = new GroupBox();
            groupBox2 = new GroupBox();
            label1 = new Label();
            groupBox1 = new GroupBox();
            btnExport = new Button();
            lblCounter = new Label();
            txtBxNote = new TextBox();
            label2 = new Label();
            grpBx1.SuspendLayout();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // btnSelectFile
            // 
            btnSelectFile.Location = new Point(271, 48);
            btnSelectFile.Name = "btnSelectFile";
            btnSelectFile.Size = new Size(75, 23);
            btnSelectFile.TabIndex = 0;
            btnSelectFile.Text = "選定檔案";
            btnSelectFile.UseVisualStyleBackColor = true;
            btnSelectFile.Click += btnSelectFile_Click;
            // 
            // opfDlgImport
            // 
            opfDlgImport.FileName = "openFileDialog1";
            opfDlgImport.FileOk += opfDlgImport_FileOk;
            // 
            // txtbxFileName
            // 
            txtbxFileName.Location = new Point(129, 19);
            txtbxFileName.Name = "txtbxFileName";
            txtbxFileName.Size = new Size(313, 23);
            txtbxFileName.TabIndex = 1;
            txtbxFileName.Text = "*.csv";
            txtbxFileName.TextChanged += txtbxFileName_TextChanged;
            // 
            // btnImport
            // 
            btnImport.Location = new Point(367, 48);
            btnImport.Name = "btnImport";
            btnImport.Size = new Size(75, 23);
            btnImport.TabIndex = 2;
            btnImport.Text = "匯入";
            btnImport.UseVisualStyleBackColor = true;
            btnImport.Click += btnImport_Click;
            // 
            // CmbxCustomerId
            // 
            CmbxCustomerId.DropDownStyle = ComboBoxStyle.DropDownList;
            CmbxCustomerId.FormattingEnabled = true;
            CmbxCustomerId.IntegralHeight = false;
            CmbxCustomerId.Location = new Point(29, 36);
            CmbxCustomerId.Name = "CmbxCustomerId";
            CmbxCustomerId.Size = new Size(372, 23);
            CmbxCustomerId.TabIndex = 3;
            CmbxCustomerId.SelectedIndexChanged += CmbxCustomerId_SelectedIndexChanged;
            // 
            // btnRefreshCustomerIdList
            // 
            btnRefreshCustomerIdList.Location = new Point(407, 35);
            btnRefreshCustomerIdList.Name = "btnRefreshCustomerIdList";
            btnRefreshCustomerIdList.Size = new Size(96, 23);
            btnRefreshCustomerIdList.TabIndex = 4;
            btnRefreshCustomerIdList.Text = "更新客戶清單";
            btnRefreshCustomerIdList.UseVisualStyleBackColor = true;
            btnRefreshCustomerIdList.Click += btnRefreshCustomerIdList_Click;
            // 
            // grpBx1
            // 
            grpBx1.Controls.Add(groupBox2);
            grpBx1.Controls.Add(groupBox1);
            grpBx1.Location = new Point(12, 119);
            grpBx1.Name = "grpBx1";
            grpBx1.Size = new Size(495, 175);
            grpBx1.TabIndex = 5;
            grpBx1.TabStop = false;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(btnImport);
            groupBox2.Controls.Add(btnSelectFile);
            groupBox2.Controls.Add(txtbxFileName);
            groupBox2.Location = new Point(6, 83);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(463, 86);
            groupBox2.TabIndex = 7;
            groupBox2.TabStop = false;
            groupBox2.Text = "匯入";
            // 
            // label1
            // 
            label1.Location = new Point(6, 19);
            label1.Name = "label1";
            label1.Size = new Size(117, 23);
            label1.TabIndex = 3;
            label1.Text = "匯入資料來源(csv)：";
            label1.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(btnExport);
            groupBox1.Location = new Point(6, 22);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(463, 55);
            groupBox1.TabIndex = 7;
            groupBox1.TabStop = false;
            groupBox1.Text = "匯出";
            // 
            // btnExport
            // 
            btnExport.Location = new Point(11, 21);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(431, 23);
            btnExport.TabIndex = 6;
            btnExport.Text = "匯出為csv 檔案";
            btnExport.UseVisualStyleBackColor = true;
            btnExport.Click += btnExport_Click;
            // 
            // lblCounter
            // 
            lblCounter.Font = new Font("微軟正黑體", 30F, FontStyle.Bold);
            lblCounter.ForeColor = SystemColors.Desktop;
            lblCounter.Location = new Point(12, 62);
            lblCounter.Name = "lblCounter";
            lblCounter.Size = new Size(495, 55);
            lblCounter.TabIndex = 7;
            lblCounter.Text = "00";
            lblCounter.TextAlign = ContentAlignment.MiddleCenter;
            lblCounter.Visible = false;
            lblCounter.Click += lblCounter_Click;
            // 
            // txtBxNote
            // 
            txtBxNote.Location = new Point(12, 306);
            txtBxNote.Name = "txtBxNote";
            txtBxNote.ReadOnly = true;
            txtBxNote.Size = new Size(495, 23);
            txtBxNote.TabIndex = 7;
            // 
            // label2
            // 
            label2.Location = new Point(29, 9);
            label2.Name = "label2";
            label2.Size = new Size(90, 23);
            label2.TabIndex = 8;
            label2.Text = "客戶清單：";
            label2.TextAlign = ContentAlignment.MiddleLeft;
            label2.Click += label2_Click;
            // 
            // MyForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(515, 337);
            Controls.Add(label2);
            Controls.Add(txtBxNote);
            Controls.Add(lblCounter);
            Controls.Add(grpBx1);
            Controls.Add(btnRefreshCustomerIdList);
            Controls.Add(CmbxCustomerId);
            Name = "MyForm";
            Text = "Tag Import-Export-ver00006";
            Load += Form1_Load;
            grpBx1.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button btnSelectFile;
        private OpenFileDialog opfDlgImport;
        private TextBox txtbxFileName;
        private Button btnImport;
        private ComboBox CmbxCustomerId;
        private Button btnRefreshCustomerIdList;
        private GroupBox grpBx1;
        private Label label1;
        private Button btnExport;
        private GroupBox groupBox2;
        private GroupBox groupBox1;
        private Label lblCounter;
        private TextBox txtBxNote;
        private Label label2;
    }
}
