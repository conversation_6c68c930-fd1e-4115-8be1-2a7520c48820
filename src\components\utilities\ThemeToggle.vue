<template>
  <div class="theme-toggle">
    <a-tooltip :title="tooltipText" placement="bottom">
      <a-button
        type="text"
        @click="toggleTheme"
        class="theme-toggle-btn"
        :class="{ 'dark-mode': isDarkMode }"
      >
        <component :is="isDarkMode ? SunOutlined : MoonOutlined" />
      </a-button>
    </a-tooltip>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { SunOutlined, MoonOutlined } from '@ant-design/icons-vue'

export default {
  name: 'ThemeToggle',
  setup() {
    const themeStore = useThemeStore()

    const isDarkMode = computed(() => themeStore.isDarkMode)
    
    const themeIcon = computed(() => {
      return isDarkMode.value ? 'SunOutlined' : 'MoonOutlined'
    })
    
    const tooltipText = computed(() => {
      return isDarkMode.value ? '切換到淺色主題' : '切換到深色主題'
    })

    const toggleTheme = () => {
      themeStore.toggleTheme()
    }

    return {
      isDarkMode,
      themeIcon,
      tooltipText,
      toggleTheme,
      SunOutlined,
      MoonOutlined
    }
  }
}
</script>

<style scoped>
.theme-toggle {
  display: inline-block;
}

.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--text-primary);
  background: transparent;
  border: 1px solid var(--border-normal);
}

.theme-toggle-btn:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: scale(1.05);
}

.theme-toggle-btn:focus {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.theme-toggle-btn.dark-mode {
  color: var(--text-primary);
  border-color: var(--border-normal);
}

.theme-toggle-btn.dark-mode:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* 圖標動畫效果 */
.theme-toggle-btn .anticon {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.theme-toggle-btn:hover .anticon {
  transform: rotate(15deg);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .theme-toggle-btn {
    width: 36px;
    height: 36px;
  }
  
  .theme-toggle-btn .anticon {
    font-size: 16px;
  }
}
</style>
