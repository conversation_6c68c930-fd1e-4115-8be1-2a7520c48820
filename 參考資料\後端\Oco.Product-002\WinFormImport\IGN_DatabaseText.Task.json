{"//DbWriterText": "Data Source=localhost;Initial Catalog=Oco.Product-002;Integrated Security=True;", "//DbReaderText": "Data Source=localhost;Initial Catalog=Oco.Product-002;Integrated Security=True;", "DbWriterText": "User ID =postgres;Password=****;Host=localhost;Port=5432;Database=Oco.Product-002;Pooling=true", "DbReaderText": "User ID =postgres;Password=****;Host=localhost;Port=5432;Database=Oco.Product-002;Pooling=true", "--DbWriterText": "User ID =postgres;Password=****;Host=*************;Port=5432;Database=Oco.Product-002;Pooling=true", "--DbReaderText": "User ID=postgres;Password=****;Host=*************;Port=5432;Database=Oco.Product-002;Pooling=true", "LogDir": "C:\\ImportTagNote\\", "LogFileFormat": "yyyy-MM-dd HH-mm-ss", "ExportDir": "C:\\ExportTagFile\\", "ExportFilePostFixFormat": "yyyyMMddHHmmss"}